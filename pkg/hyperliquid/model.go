package hyperliquid

type UniverseData struct {
	Universe []struct {
		SzDecimals    int    `json:"szDecimals"`
		Name          string `json:"name"`
		MaxLeverage   int    `json:"maxLeverage"`
		MarginTableId int    `json:"marginTableId"`
		IsDelisted    bool   `json:"isDelisted,omitempty"`
		OnlyIsolated  bool   `json:"onlyIsolated,omitempty"`
	} `json:"universe"`
	MarginTables interface{} `json:"marginTables"`
}

type StatisticData struct {
	Funding      string   `json:"funding"`
	OpenInterest string   `json:"openInterest"`
	PrevDayPx    string   `json:"prevDayPx"`
	DayNtlVlm    string   `json:"dayNtlVlm"`
	Premium      string   `json:"premium"`
	OraclePx     string   `json:"oraclePx"`
	MarkPx       string   `json:"markPx"`
	MidPx        string   `json:"midPx"`
	ImpactPxs    []string `json:"impactPxs"`
	DayBaseVlm   string   `json:"dayBaseVlm"`
}

type PerpAssetResponse []interface{}

type Balance struct {
	Coin     string `json:"coin"`
	Token    int    `json:"token"`
	Hold     string `json:"hold"`
	Total    string `json:"total"`
	EntryNtl string `json:"entryNtl"`
}

type UserTokenBalancesResponse struct {
	Balance []Balance `json:"balances"`
}

// MarginSummary represents the margin summary data
type MarginSummary struct {
	AccountValue    string `json:"accountValue"`
	TotalNtlPos     string `json:"totalNtlPos"`
	TotalRawUsd     string `json:"totalRawUsd"`
	TotalMarginUsed string `json:"totalMarginUsed"`
}

// Leverage represents the leverage information
type Leverage struct {
	Type  string `json:"type"`
	Value int    `json:"value"`
}

// Funding represents cumulative funding data
type Funding struct {
	AllTime     string `json:"allTime"`
	SinceOpen   string `json:"sinceOpen"`
	SinceChange string `json:"sinceChange"`
}

// Position represents a trading position
type Position struct {
	Coin           string   `json:"coin"`
	Szi            string   `json:"szi"`
	Leverage       Leverage `json:"leverage"`
	EntryPx        string   `json:"entryPx"`
	PositionValue  string   `json:"positionValue"`
	UnrealizedPnl  string   `json:"unrealizedPnl"`
	ReturnOnEquity string   `json:"returnOnEquity"`
	LiquidationPx  *string  `json:"liquidationPx"`
	MarginUsed     string   `json:"marginUsed"`
	MaxLeverage    int      `json:"maxLeverage"`
	CumFunding     Funding  `json:"cumFunding"`
}

// AssetPosition represents an asset position
type AssetPosition struct {
	Type     string   `json:"type"`
	Position Position `json:"position"`
}

// AccountSummary represents the complete account data structure
type AccountSummary struct {
	MarginSummary              MarginSummary   `json:"marginSummary"`
	CrossMarginSummary         MarginSummary   `json:"crossMarginSummary"`
	CrossMaintenanceMarginUsed string          `json:"crossMaintenanceMarginUsed"`
	Withdrawable               string          `json:"withdrawable"`
	AssetPositions             []AssetPosition `json:"assetPositions"`
	Time                       int64           `json:"time"`
}

type TradeHistory struct {
	ClosedPnl     string  `json:"closedPnl"`
	Coin          string  `json:"coin"`
	Crossed       bool    `json:"crossed"`
	Dir           string  `json:"dir"`
	Hash          string  `json:"hash"`
	Oid           int64   `json:"oid"`
	Px            string  `json:"px"`
	Side          string  `json:"side"`
	StartPosition string  `json:"startPosition"`
	Sz            string  `json:"sz"`
	Time          int64   `json:"time"`
	Fee           string  `json:"fee"`
	FeeToken      string  `json:"feeToken"`
	BuilderFee    *string `json:"builderFee,omitempty"`
	Tid           int64   `json:"tid"`
}

type CategoryList struct {
	Dex       []string
	Trending  []string
	PreLaunch []string
	AI        []string
	Layer1    []string
	Layer2    []string
	Defi      []string
	Gaming    []string
	Meme      []string
}

package hyperliquid

import (
	"bytes"
	"encoding/json"
	"fmt"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	"go.uber.org/zap"
	"golang.org/x/net/html"
	"io"
	"net/http"
	"regexp"
	"strings"
	"time"
)

func CrawSymbolListFromAPI() (UniverseData, []StatisticData, error) {
	apiURL := InfoAPIMainnetEndpoint
	reqBody := []byte(`{"type": "metaAndAssetCtxs"}`)
	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	if err != nil {
		return UniverseData{}, nil, err
	}

	// Perform the request
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return UniverseData{}, nil, err
	}

	if resp.StatusCode != http.StatusOK {
		return UniverseData{}, nil, fmt.Errorf("status code %d", resp.StatusCode)
	}

	// Read and parse the response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return UniverseData{}, nil, err
	}

	var response PerpAssetResponse
	if err = json.Unmarshal(body, &response); err != nil {
		return UniverseData{}, nil, err
	}

	if len(response) < 2 || resp.Status != "200 OK" {
		global.GVA_LOG.Info("[task] CrawlHyperLiquid status ", zap.Any("status", resp.Status))
		return UniverseData{}, nil, nil
	}

	var universeData UniverseData
	if err = utils.JsonDecode[UniverseData](response[0], &universeData); err != nil {
		return UniverseData{}, nil, err
	}

	var statisticDataArray []StatisticData
	if err = utils.JsonDecode[[]StatisticData](response[1], &statisticDataArray); err != nil {
		return UniverseData{}, nil, err
	}

	if len(statisticDataArray) != len(universeData.Universe) {
		return UniverseData{}, nil, fmt.Errorf("Number of funding data don't match\n")
	}

	return universeData, statisticDataArray, nil
}

func GetMainPageURL() (string, error) {
	resp, err := http.Get(global.GVA_HYPERLIQUID_URL)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("HTTP error: %s", resp.Status)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	doc, err := html.Parse(strings.NewReader(string(body)))
	if err != nil {
		return "", fmt.Errorf("failed to parse HTML: %v", err)
	}

	var mainURL string
	// Recursive function to traverse the HTML node tree.
	var traverse func(*html.Node)
	traverse = func(n *html.Node) {
		if n.Type == html.ElementNode && n.Data == "script" {
			for _, attr := range n.Attr {
				if attr.Key == "src" && strings.Contains(attr.Val, "main.") && strings.HasSuffix(attr.Val, ".js") {
					mainURL = attr.Val
					return // Found the URL, so return.
				}
			}
		}
		for c := n.FirstChild; c != nil; c = c.NextSibling {
			if mainURL != "" {
				return // No need to traverse further if URL is found.
			}
			traverse(c)
		}
	}

	traverse(doc)
	if mainURL == "" {
		return "", fmt.Errorf("main.js URL not found")
	}
	return global.GVA_HYPERLIQUID_URL + mainURL, nil
}

func FetchMainPageContent(url string) (string, error) {
	resp, err := http.Get(url)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("HTTP error: %s", resp.Status)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

func ParseMainPageContent(content string) *CategoryList {
	// Extract the data using the regular expressions.
	dexMatch := regexp.MustCompile(utils.DexRegex).FindStringSubmatch(content)
	trendMatch := regexp.MustCompile(utils.TrendRegex).FindStringSubmatch(content)
	preLaunchMatch := regexp.MustCompile(utils.PreLaunchRegex).FindStringSubmatch(content)
	AIMatch := regexp.MustCompile(utils.AIRegex).FindStringSubmatch(content)
	layer1Match := regexp.MustCompile(utils.Layer1Regex).FindStringSubmatch(content)
	layer2Match := regexp.MustCompile(utils.Layer2Regex).FindStringSubmatch(content)
	defiMatch := regexp.MustCompile(utils.DefiRegex).FindStringSubmatch(content)
	gamingMatch := regexp.MustCompile(utils.GamingRegex).FindStringSubmatch(content)
	memeMatch := regexp.MustCompile(utils.MemeRegex).FindStringSubmatch(content)

	return &CategoryList{
		Dex:       utils.ParseStringArray(dexMatch[1]),
		Trending:  utils.ParseStringArray(trendMatch[1]),
		PreLaunch: utils.ParseStringArray(preLaunchMatch[1]),
		AI:        utils.ParseStringArray(AIMatch[1]),
		Layer1:    utils.ParseStringArray(layer1Match[1]),
		Layer2:    utils.ParseStringArray(layer2Match[1]),
		Defi:      utils.ParseStringArray(defiMatch[1]),
		Gaming:    utils.ParseStringArray(gamingMatch[1]),
		Meme:      utils.ParseStringArray(memeMatch[1]),
	}
}

func GetAccountSummaryFromAPI(userWallet string) (AccountSummary, error) {
	apiURL := global.GVA_HYPERLIQUID_INFO_ENDPOINT
	reqBody := []byte(`{"type": "clearinghouseState","user": "` + userWallet + `"}`)
	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	if err != nil {
		return AccountSummary{}, err
	}

	// Perform the request
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return AccountSummary{}, err
	}

	if resp.StatusCode != http.StatusOK {
		return AccountSummary{}, fmt.Errorf("%d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return AccountSummary{}, err
	}
	defer resp.Body.Close()

	var accountSummary AccountSummary
	if err = json.Unmarshal(body, &accountSummary); err != nil {
		return AccountSummary{}, err
	}

	return accountSummary, nil
}

func GetUserTradeHistoryFromAPI(userWallet string) ([]TradeHistory, error) {
	apiURL := global.GVA_HYPERLIQUID_INFO_ENDPOINT
	reqBody := []byte(`{"type": "userFills","user": "` + userWallet + `"}`)
	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	if err != nil {
		return nil, err
	}

	// Perform the request
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("status code %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var histories []TradeHistory
	if err = json.Unmarshal(body, &histories); err != nil {
		return nil, err
	}

	return histories, nil
}

package coinmarketcap

type CoinMarketCapResponse struct {
	Status Status                  `json:"status"`
	Data   map[string]CryptoDetail `json:"data"`
}

// Status represents the status info from the API
type Status struct {
	Timestamp    string `json:"timestamp"`
	ErrorCode    int    `json:"error_code"`
	ErrorMessage string `json:"error_message,omitempty"`
	Elapsed      int    `json:"elapsed"`
	CreditCount  int    `json:"credit_count"`
	Notice       string `json:"notice,omitempty"`
}

// CryptoDetail represents cryptocurrency data
type CryptoDetail struct {
	ID                            int      `json:"id"`
	Name                          string   `json:"name"`
	Symbol                        string   `json:"symbol"`
	Slug                          string   `json:"slug"`
	NumMarketPairs                int      `json:"num_market_pairs"`
	DateAdded                     string   `json:"date_added"`
	Tags                          []string `json:"tags"`
	MaxSupply                     float64  `json:"max_supply,omitempty"`
	CirculatingSupply             float64  `json:"circulating_supply"`
	TotalSupply                   float64  `json:"total_supply"`
	IsActive                      int      `json:"is_active"`
	InfiniteSupply                bool     `json:"infinite_supply"`
	Platform                      Platform `json:"platform,omitempty"`
	CmcRank                       int      `json:"cmc_rank,omitempty"`
	IsFiat                        int      `json:"is_fiat"`
	SelfReportedCirculatingSupply float64  `json:"self_reported_circulating_supply,omitempty"`
	SelfReportedMarketCap         float64  `json:"self_reported_market_cap,omitempty"`
	TvlRatio                      float64  `json:"tvl_ratio,omitempty"`
	LastUpdated                   string   `json:"last_updated"`
	Quote                         Quote    `json:"quote"`
}

// Platform represents the platform on which a token operates
type Platform struct {
	ID           int    `json:"id"`
	Name         string `json:"name"`
	Symbol       string `json:"symbol"`
	Slug         string `json:"slug"`
	TokenAddress string `json:"token_address"`
}

// Quote represents the pricing information
type Quote struct {
	USD CurrencyQuote `json:"USD"`
}

// CurrencyQuote represents the pricing information in a specific currency
type CurrencyQuote struct {
	Price                 float64 `json:"price,omitempty"`
	Volume24h             float64 `json:"volume_24h"`
	VolumeChange24h       float64 `json:"volume_change_24h"`
	PercentChange1h       float64 `json:"percent_change_1h"`
	PercentChange24h      float64 `json:"percent_change_24h"`
	PercentChange7d       float64 `json:"percent_change_7d"`
	PercentChange30d      float64 `json:"percent_change_30d"`
	PercentChange60d      float64 `json:"percent_change_60d"`
	PercentChange90d      float64 `json:"percent_change_90d"`
	MarketCap             float64 `json:"market_cap,omitempty"`
	MarketCapDominance    float64 `json:"market_cap_dominance"`
	FullyDilutedMarketCap float64 `json:"fully_diluted_market_cap"`
	TVL                   float64 `json:"tvl,omitempty"`
	LastUpdated           string  `json:"last_updated"`
}

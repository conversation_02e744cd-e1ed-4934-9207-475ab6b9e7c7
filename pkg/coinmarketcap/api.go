package coinmarketcap

import (
	"encoding/json"
	"fmt"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"io"
	"log"
	"net/http"
	"net/url"
	"os"
)

func GetCoinMarketCapData(field, value string) (CoinMarketCapResponse, error) {
	var (
		apiURL = InfoAPIEndpoint
		apiKey = global.GVA_CONFIG.CoinmMarketCap.APIKey
	)

	client := &http.Client{}
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		log.Print(err)
		os.Exit(1)
	}

	q := url.Values{}
	q.Add("convert", "USD")
	q.Add(field, value)

	req.Header.Set("Accepts", "application/json")
	req.Header.Add("X-CMC_PRO_API_KEY", apiKey)
	req.URL.RawQuery = q.Encode()

	resp, err := client.Do(req)
	if err != nil {
		return CoinMarketCapResponse{}, err
	}

	if resp.StatusCode != http.StatusOK {
		return CoinMarketCapResponse{}, fmt.Errorf("status code %d", resp.StatusCode)
	}

	// Read and parse the response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return CoinMarketCapResponse{}, err
	}

	var response CoinMarketCapResponse
	err = json.Unmarshal(body, &response)
	if err != nil {
		return CoinMarketCapResponse{}, err
	}

	return response, nil
}

package rango

import (
	"encoding/hex"
	"encoding/json"
	"fmt"
	"testing"
)

// ///////////////////Evm//////////////////////

func TestGetExchangeMeta(t *testing.T) {
	result, err := GetExchangeMeta()
	if err != nil {
		t.Fatalf("GetExchangeMeta failed: %v", err)
	}

	resultJSON, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		t.Fatalf("Marshal result failed: %v", err)
	}
	fmt.Printf("GetExchangeMeta Result:\n%s\n", string(resultJSON))

	if result == nil {
		t.Error("Result is nil")
	}
	if len(result.Tokens) == 0 {
		t.<PERSON>rror("No tokens found")
	}
	if len(result.Blockchains) == 0 {
		t.<PERSON><PERSON>r("No blockchains found")
	}
	if len(result.Swappers) == 0 {
		t.<PERSON><PERSON>r("No swappers found")
	}
}

func TestGetTheBestRoute(t *testing.T) {
	fromBlockchain := "TRON"
	fromSymbol := "USDT"
	fromAddress := "******************************************"
	fromTokenAddress := ""
	toBlockchain := "ARBITRUM"
	toSymbol := "USDC"
	toTokenAddress := ""
	toAddress := "******************************************"
	amount := "0.0001"
	affiliatePercent := "0.3"
	affiliateWallets := map[string]string{
		"BSC": "******************************************",
	}
	result, err := GetBestRoute(fromBlockchain, fromSymbol, fromTokenAddress, fromAddress, toBlockchain, toSymbol, toTokenAddress,
		toAddress, amount, affiliatePercent, affiliateWallets)
	if err != nil {
		t.Fatalf("GetBestRoute failed: %v", err)
	}

	resultJSON, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		t.Fatalf("Marshal result failed: %v", err)
	}
	fmt.Printf("GetBestRoute Result:\n%s\n", string(resultJSON))

	if result == nil {
		t.Error("Result is nil")
	}
	if result.Result.OutputAmount == "" {
		t.Error("OutputAmount is empty")
	}
	if len(result.Result.Swaps) == 0 {
		t.Error("No swaps found")
	}
}

func TestConfirmRoute(t *testing.T) {

	selectedWallets := map[string]string{
		"BSC":      "******************************************",
		"ARBITRUM": "******************************************",
	}
	destination := "******************************************"
	requestId := "a61a720d-d6c1-4cb9-9774-850db9c35adc"

	result, err := ConfirmRoute(selectedWallets, destination, requestId)
	if err != nil {
		t.Fatalf("ConfirmRoute failed: %v", err)
	}

	resultJSON, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		t.Fatalf("Marshal result failed: %v", err)
	}
	fmt.Printf("ConfirmRoute Result:\n%s\n", string(resultJSON))

	if result == nil {
		t.Error("Result is nil")
	}
	if result.Result.RequestId == "" {
		t.Error("RequestId is empty")
	}
	if result.Result.Result.OutputAmount == "" {
		t.Error("OutputAmount is empty")
	}
	if len(result.Result.Result.Swaps) == 0 {
		t.Error("No swaps found")
	}
}

func TestCreateTx(t *testing.T) {
	requestId := "a61a720d-d6c1-4cb9-9774-850db9c35adc"
	userSettings := UserSettings{
		Slippage:        3,
		InfiniteApprove: false,
	}
	validations := Validations{
		Balance: true,
		Fee:     true,
		Approve: true,
	}
	step := int64(1)

	result, err := CreateTx(requestId, userSettings, validations, step)
	if err != nil {
		t.Fatalf("CreateTx failed: %v", err)
	}

	jsonData, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		t.Fatalf("Marshal result failed: %v", err)
	}
	fmt.Printf("CreateTx result:\n%s\n", string(jsonData))

	if !result.Ok {
		t.Error("result.Ok is false")
	}
	if result.Transaction == nil {
		t.Error("Tx should not be nil")
	}
}

func TestSendSwapTransaction(t *testing.T) {
	var datastr = "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"
	dataBytes, err := hex.DecodeString(datastr)
	if err != nil {
		t.Errorf("Error decoding hex string: %v", err)
	}
	txHash, err := SendTransaction(dataBytes)
	if err != nil {
		t.Fatalf("SendTransaction failed: %v", err)
	}
	fmt.Printf("SendTransaction Result:\n%s\n", txHash)
}
func TestPolygonSendSwapTransaction(t *testing.T) {
	var datastr = "14d08fca000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000002200000000000000000000000001a7cb744d9a70b2c644b90e498493c0c7a21b92b000000000000000000000000000000005d8971cee8df4b0f8b56b03a7df2579f0000000000000000000000002791bca1f2de4661ed88a30c99a7a9449aa841740000000000000000000000003c499c542cef5e3811e1192ce70d8cc03d5c33590000000000000000000000000000000000000000000000000000000000090bbd0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003e82a86dba9e843aa1ce1ec8c3bb7290e25d8c1b00000000000000000000000000000000000000000000000000000000000905960000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000006a0000000000000000000000000000000000000000000000000000000000000180000000000000000000000000000000000000000000000000000000000000001152616e676f207075626c6963205465737400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000020000000000000000000000000e592427a0aece92de3edee1f18e0157c05861564000000000000000000000000e592427a0aece92de3edee1f18e0157c058615640000000000000000000000002791bca1f2de4661ed88a30c99a7a9449aa841740000000000000000000000003c499c542cef5e3811e1192ce70d8cc03d5c335900000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000090bbd00000000000000000000000000000000000000000000000000000000000000e000000000000000000000000000000000000000000000000000000000000001c4ac9650d80000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000124c04b8d59000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000a000000000000000000000000069460570c93f9de5e2edbc3052bf10125f0ca22d000000000000000000000000000000000000000000000000000001965d2b2dec0000000000000000000000000000000000000000000000000000000000090bbd00000000000000000000000000000000000000000000000000000000000904a3000000000000000000000000000000000000000000000000000000000000002b2791bca1f2de4661ed88a30c99a7a9449aa841740000643c499c542cef5e3811e1192ce70d8cc03d5c33590000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"
	dataBytes, err := hex.DecodeString(datastr)
	if err != nil {
		t.Errorf("Error decoding hex string: %v", err)
	}
	txHash, err := PolygonSendTransaction(dataBytes)
	if err != nil {
		t.Fatalf("SendTransaction failed: %v", err)
	}
	fmt.Printf("SendTransaction Result:\n%s\n", txHash)
}
func TestCheckStatus(t *testing.T) {
	requestId := "a705fa3d-9dbe-42e8-9432-bb5bee009f31"

	txHash := "0x65869f82255abaa806247fccb0c0f5f10bf84822c0ee43f19e8f6740d78335b3"
	step := int64(1)

	result, err := CheckStatus(requestId, txHash, step)
	if err != nil {
		t.Fatalf("CheckStatus failed: %v", err)
	}

	jsonData, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		t.Fatalf("Marshal result failed: %v", err)
	}
	fmt.Printf("CheckStatus result:\n%s\n", string(jsonData))

	if result == nil {
		t.Error("Result should not be nil")
	}
}

func TestCheckApproval(t *testing.T) {
	requestId := "5d98aac8-7d65-4f5b-b94f-05bc4dd78987"
	//requestId := "9f7017fe-dc61-4519-8417-37e1af3062ed" //fail

	txHash := "0xa61415ed41a23eb56531e3e422137e5cabadeb0a7def6ae9cd25455df73c8a45" //success
	//txHash := "0x91a49759bcbcfdfb470d0332231f176b45c115c369e305c5c039e973d45ea276" //fail
	result, err := CheckApproval(requestId, txHash)
	if err != nil {
		t.Fatalf("CheckApproval failed: %v", err)
	}

	jsonData, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		t.Fatalf("Marshal result failed: %v", err)
	}
	fmt.Printf("CheckApproval result:\n%s\n", string(jsonData))

	if result == nil {
		t.Error("Result should not be nil")
	}
}

// just for reporting and we recommend calling it.
func TestReportTx(t *testing.T) {
	//requestId := "5d98aac8-7d65-4f5b-b94f-05bc4dd78987"
	//requestId := "9f7017fe-dc61-4519-8417-37e1af3062ed" //fail
	requestId := "688b308e-a06b-4a4e-a837-220d458b8642" //fail

	step := int64(1)
	eventType := "SEND_TX_FAILED"
	reason := "RPC Error"

	result, err := ReportTx(requestId, step, eventType, reason)
	if err != nil {
		t.Fatalf("ReportTx failed: %v", err)
	}

	jsonData, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		t.Fatalf("Marshal result failed: %v", err)
	}
	fmt.Printf("ReportTx result:\n%s\n", string(jsonData))

	if result == nil {
		t.Error("Result should not be nil")
	}
	if result.RequestId == "" {
		t.Error("RequestId should not be empty")
	}
	if result.Status == "" {
		t.Error("Status should not be empty")
	}
}

func TestGetCustomToken(t *testing.T) {
	//blockchain := "SOLANA"
	//address := "3yoMkf3X6bDxjks6YaWwNk4SAbuaysLg1a4BjQKToQAA"
	blockchain := "AVAX_CCHAIN"
	tokenAddress := "******************************************"

	result, err := GetCustomToken(blockchain, tokenAddress)
	if err != nil {
		t.Fatalf("GetCustomToken failed: %v", err)
	}

	jsonData, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		t.Fatalf("Marshal result failed: %v", err)
	}
	fmt.Printf("GetCustomToken result:\n%s\n", string(jsonData))

	if result == nil {
		t.Error("Result should not be nil")
	}
}

func TestGetWalletDetails(t *testing.T) {
	//address := "BSC.******************************************"
	address := "AVAX_CCHAIN.******************************************"
	result, err := GetWalletDetails(address)
	if err != nil {
		t.Fatalf("GetWalletDetails failed: %v", err)
	}

	jsonData, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		t.Fatalf("Marshal result failed: %v", err)
	}
	fmt.Printf("GetWalletDetails result:\n%s\n", string(jsonData))

	if result == nil {
		t.Error("Result should not be nil")
	}
}

////////////////////NoEvm//////////////////////////

func TestNoEvmGetTheBestRoute(t *testing.T) {
	fromBlockchain := "SOLANA"
	fromSymbol := "SOL"
	fromAddress := "7zpri23i8vag4ba2U2ESJF7ik8oFSBqm3cWQjn6wMTxT"
	fromTokenAddress := ""
	toBlockchain := "POLYGON"
	toSymbol := "POL"
	toTokenAddress := ""
	toAddress := "******************************************"
	amount := "0.001"
	affiliatePercent := " 0.3"
	affiliateWallets := map[string]string{
		"BSC": "******************************************",
	}
	result, err := GetBestRoute(fromBlockchain, fromSymbol, fromTokenAddress, fromAddress, toBlockchain, toSymbol, toTokenAddress,
		toAddress, amount, affiliatePercent, affiliateWallets)
	if err != nil {
		t.Fatalf("GetBestRoute failed: %v", err)
	}

	resultJSON, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		t.Fatalf("Marshal result failed: %v", err)
	}
	fmt.Printf("GetBestRoute Result:\n%s\n", string(resultJSON))

	if result == nil {
		t.Error("Result is nil")
	}
	if result.Result.OutputAmount == "" {
		t.Error("OutputAmount is empty")
	}
	if len(result.Result.Swaps) == 0 {
		t.Error("No swaps found")
	}
}

func TestNoEvmGetAllPossibleRoutes(t *testing.T) {
	tests := []struct {
		name             string
		fromBlockchain   string
		fromSymbol       string
		fromTokenAddress string
		toBlockchain     string
		toSymbol         string
		toTokenAddress   string
		amount           string
		slippage         string
		affiliatePercent string
		affiliateWallets map[string]string
		wantErr          bool
	}{
		//{
		//	name:           "SOL to POL",
		//	fromBlockchain: "SOLANA",
		//	fromSymbol:     "SOL",
		//	toBlockchain:   "POLYGON",
		//	toSymbol:       "POL",
		//	toTokenAddress: "",
		//	amount:         "0.1",
		//	slippage:       "1.0",
		//	wantErr:        false,
		//},
		//fromBlockchain := "TRON"
		//	fromSymbol := "USDT"
		//	fromAddress := "******************************************"
		//	toBlockchain := "ARBITRUM"
		//	toSymbol := "USDC"
		//	toTokenAddress := ""
		//	toAddress := "******************************************"
		//	amount := "0.0001"
		{
			name:             "USDT to USDC",
			fromBlockchain:   "TRON",
			fromSymbol:       "USDT",
			fromTokenAddress: "",
			toBlockchain:     "ARBITRUM",
			toSymbol:         "ETH",
			toTokenAddress:   "",
			amount:           "1000",
			slippage:         "1.0",
			affiliatePercent: " 0.3",
			affiliateWallets: map[string]string{
				"BSC": "******************************************",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GetAllPossibleRoutes(
				tt.fromBlockchain, tt.fromSymbol, tt.fromTokenAddress, tt.toBlockchain, tt.toSymbol, tt.toTokenAddress, tt.amount, tt.slippage,
				tt.affiliatePercent, tt.affiliateWallets,
			)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got nil")
				}
				return
			}

			if err != nil {
				t.Fatalf("GetAllPossibleRoutes failed: %v", err)
			}

			resultJSON, err := json.MarshalIndent(result, "", "  ")
			if err != nil {
				t.Fatalf("Marshal result failed: %v", err)
			}
			t.Logf("GetAllPossibleRoutes Result:\n%s\n", string(resultJSON))

			if result == nil {
				t.Error("Result is nil")
			}

			if len(result.Results) == 0 {
				t.Error("No routes found")
			}

			for i, route := range result.Results {
				if route.OutputAmount == "" {
					t.Errorf("Route %d: OutputAmount is empty", i)
				}

				if len(route.Swaps) == 0 {
					t.Errorf("Route %d: No swaps found", i)
				}

				for j, swap := range route.Swaps {
					if swap.FromAmount == "" {
						t.Errorf("Route %d, Swap %d: FromAmount is empty", i, j)
					}
					if swap.ToAmount == "" {
						t.Errorf("Route %d, Swap %d: ToAmount is empty", i, j)
					}
					if swap.SwapperId == "" {
						t.Errorf("Route %d, Swap %d: SwapperId is empty", i, j)
					}
				}

				if len(route.Scores) == 0 {
					t.Errorf("Route %d: No scores found", i)
				}

				if route.PriceImpactUsd == "" {
					t.Errorf("Route %d: PriceImpactUsd is empty", i)
				}
				if route.PriceImpactUsdPercent == "" {
					t.Errorf("Route %d: PriceImpactUsdPercent is empty", i)
				}
			}
		})
	}
}

func TestNoEvmGetExchangeMeta(t *testing.T) {
	result, err := GetExchangeMeta()
	if err != nil {
		t.Fatalf("GetExchangeMeta failed: %v", err)
	}

	resultJSON, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		t.Fatalf("Marshal result failed: %v", err)
	}
	fmt.Printf("GetExchangeMeta Result:\n%s\n", string(resultJSON))

	if result == nil {
		t.Error("Result is nil")
	}
	if len(result.Tokens) == 0 {
		t.Error("No tokens found")
	}
	if len(result.Blockchains) == 0 {
		t.Error("No blockchains found")
	}
	if len(result.Swappers) == 0 {
		t.Error("No swappers found")
	}
}

func TestNoEvmConfirmRoute(t *testing.T) {

	selectedWallets := map[string]string{
		"SOLANA":  "7zpri23i8vag4ba2U2ESJF7ik8oFSBqm3cWQjn6wMTxT",
		"POLYGON": "******************************************",
	}
	destination := "******************************************"
	//requestId := "8f54a91e-0b8e-471a-8cb0-f5d0bdc1365d"
	requestId := "b2da6b41-ba8d-4421-b055-e7b0c24dcbd8"

	result, err := ConfirmRoute(selectedWallets, destination, requestId)
	if err != nil {
		t.Fatalf("ConfirmRoute failed: %v", err)
	}

	resultJSON, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		t.Fatalf("Marshal result failed: %v", err)
	}
	fmt.Printf("ConfirmRoute Result:\n%s\n", string(resultJSON))

	if result == nil {
		t.Error("Result is nil")
	}
	if result.Result.RequestId == "" {
		t.Error("RequestId is empty")
	}
	if result.Result.Result.OutputAmount == "" {
		t.Error("OutputAmount is empty")
	}
	if len(result.Result.Result.Swaps) == 0 {
		t.Error("No swaps found")
	}
}

func TestNoEvmCreateTx(t *testing.T) {
	//requestId := "8f54a91e-0b8e-471a-8cb0-f5d0bdc1365d"
	requestId := "9b4fa818-8ce1-426c-8fd9-b4956e380a7d"
	userSettings := UserSettings{
		Slippage:        0.5,
		InfiniteApprove: false,
	}
	validations := Validations{
		Balance: true,
		Fee:     true,
		Approve: true,
	}
	step := int64(1)

	result, err := CreateTx(requestId, userSettings, validations, step)
	if err != nil {
		t.Fatalf("CreateTx failed: %v", err)
	}

	jsonData, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		t.Fatalf("Marshal result failed: %v", err)
	}
	fmt.Printf("CreateTx result:\n%s\n", string(jsonData))

	if !result.Ok {
		t.Error("result.Ok is false")
	}
	if result.Transaction == nil {
		t.Error("Tx should not be nil")
	}
	txHash, err := SendNoEvmTransaction(result.Transaction.SerializedMessage)
	if err != nil {
		t.Fatalf("SendTransaction failed: %v", err)
	}
	fmt.Printf("SendTransaction Result:\n%s\n", txHash)

}

func TestNoEvmCheckStatus(t *testing.T) {
	//requestId := "8f54a91e-0b8e-471a-8cb0-f5d0bdc1365d"
	requestId := "720e00cd-7ef2-46c8-8449-da8c5563a2f2"
	//txHash := "54z6CPACKD2AXiCfugLMMwMUyNo15XFAdS2yqYcvsS2ZtZE13EoqeQm51KdJmQGkHeeSZxufXCGRvz78qgYghZ8e" //success
	txHash := "AEt3Ne7Tn5DTugteiTQBMG6Z2ooQK2VLR3YvyJNdrMBJ6QDDAUv9hLwQGL4Viv458qqUoc7hBrBj5c25bY5ZGSh" //success
	step := int64(1)

	result, err := CheckStatus(requestId, txHash, step)
	if err != nil {
		t.Fatalf("CheckStatus failed: %v", err)
	}

	jsonData, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		t.Fatalf("Marshal result failed: %v", err)
	}
	fmt.Printf("CheckStatus result:\n%s\n", string(jsonData))

	if result == nil {
		t.Error("Result should not be nil")
	}
}

func TestNoEvmCheckApproval(t *testing.T) {
	requestId := "5d98aac8-7d65-4f5b-b94f-05bc4dd78987"
	//requestId := "9f7017fe-dc61-4519-8417-37e1af3062ed" //fail

	txHash := "0xa61415ed41a23eb56531e3e422137e5cabadeb0a7def6ae9cd25455df73c8a45" //success
	//txHash := "0x91a49759bcbcfdfb470d0332231f176b45c115c369e305c5c039e973d45ea276" //fail
	result, err := CheckApproval(requestId, txHash)
	if err != nil {
		t.Fatalf("CheckApproval failed: %v", err)
	}

	jsonData, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		t.Fatalf("Marshal result failed: %v", err)
	}
	fmt.Printf("CheckApproval result:\n%s\n", string(jsonData))

	if result == nil {
		t.Error("Result should not be nil")
	}
}

// just for reporting and we recommend calling it.
func TestNoEvmReportTx(t *testing.T) {
	//requestId := "5d98aac8-7d65-4f5b-b94f-05bc4dd78987"
	//requestId := "9f7017fe-dc61-4519-8417-37e1af3062ed" //fail
	requestId := "688b308e-a06b-4a4e-a837-220d458b8642" //fail

	step := int64(1)
	eventType := "SEND_TX_FAILED"
	reason := "RPC Error"

	result, err := ReportTx(requestId, step, eventType, reason)
	if err != nil {
		t.Fatalf("ReportTx failed: %v", err)
	}

	jsonData, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		t.Fatalf("Marshal result failed: %v", err)
	}
	fmt.Printf("ReportTx result:\n%s\n", string(jsonData))

	if result == nil {
		t.Error("Result should not be nil")
	}
	if result.RequestId == "" {
		t.Error("RequestId should not be empty")
	}
	if result.Status == "" {
		t.Error("Status should not be empty")
	}
}

func TestNoEvmGetCustomToken(t *testing.T) {
	//blockchain := "SOLANA"
	//address := "3yoMkf3X6bDxjks6YaWwNk4SAbuaysLg1a4BjQKToQAA"
	blockchain := "AVAX_CCHAIN"
	tokenAddress := "******************************************"

	result, err := GetCustomToken(blockchain, tokenAddress)
	if err != nil {
		t.Fatalf("GetCustomToken failed: %v", err)
	}

	jsonData, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		t.Fatalf("Marshal result failed: %v", err)
	}
	fmt.Printf("GetCustomToken result:\n%s\n", string(jsonData))

	if result == nil {
		t.Error("Result should not be nil")
	}
}

func TestNoEvmGetWalletDetails(t *testing.T) {
	//address := "BSC.******************************************"
	address := "SOLANA.2kFftiAFBEpTkcxSnmWyyKP1MkiudYAJ16UQTwjouYui"
	result, err := GetWalletDetails(address)
	if err != nil {
		t.Fatalf("GetWalletDetails failed: %v", err)
	}

	jsonData, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		t.Fatalf("Marshal result failed: %v", err)
	}
	fmt.Printf("GetWalletDetails result:\n%s\n", string(jsonData))

	if result == nil {
		t.Error("Result should not be nil")
	}
}

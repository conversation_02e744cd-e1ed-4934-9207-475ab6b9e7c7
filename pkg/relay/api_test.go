package relay_test

import (
	"context"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"testing"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/relay"

	"math/big"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/gagliardetto/solana-go"
	"github.com/gagliardetto/solana-go/rpc"
)

func TestSol2Usdc(t *testing.T) {

	// Solana RPC endpoint
	client := rpc.New(rpc.MainNetBeta_RPC)

	// 你的私钥（base58 格式）
	privateKeyStr := ""
	privateKey, err := solana.PrivateKeyFromBase58(privateKeyStr)
	if err != nil {
		log.Fatalf("failed to parse private key: %v", err)
	}
	publicKey := privateKey.PublicKey()

	// 获取最近 blockhash
	recent, err := client.GetLatestBlockhash(context.Background(), rpc.CommitmentFinalized)
	if err != nil {
		log.Fatalf("failed to get latest blockhash: %v", err)
	}
	resp, err := relay.GetRelayQuote(&relay.QuoteRequest{
		User:                 "********************************************",
		OriginChainId:        792703809,
		DestinationChainId:   42161,
		OriginCurrency:       "********************************",
		DestinationCurrency:  "******************************************",
		Recipient:            "0x075Bb2a896cF978155a5bD85e9A3CF39B4D215eA",
		TradeType:            "EXACT_INPUT",
		Amount:               "********",
		Referrer:             "relay.link",
		UseExternalLiquidity: false,
		UseDepositAddress:    false,
		TopupGas:             false,
	})
	if err != nil {
		panic(fmt.Errorf("failed to get quote: %v", err))
	}
	fmt.Println("RequestId", resp.Steps[0].RequestId)
	// 构造指令 (示例，仅展示一个指令，按需添加多个)
	var instructions []solana.Instruction

	for _, i2 := range resp.Steps[0].Items[0].Data.Instructions {
		// 解析指令的 keys
		var instructionKeys []*solana.AccountMeta
		for _, key := range i2.Keys {
			instructionKeys = append(instructionKeys, &solana.AccountMeta{
				PublicKey:  solana.MustPublicKeyFromBase58(key.Pubkey),
				IsSigner:   key.IsSigner,
				IsWritable: key.IsWritable,
			})
		}
		data, err := hex.DecodeString(i2.Data)
		if err != nil {
			log.Fatalf("failed to decode data: %v", err)
		}
		// 构造指令
		instruction := &solana.GenericInstruction{
			AccountValues: instructionKeys,
			ProgID:        solana.MustPublicKeyFromBase58(i2.ProgramId),
			DataBytes:     data,
		}

		instructions = append(instructions, instruction)

	}
	// 构造交易
	tx, err := solana.NewTransaction(
		instructions,
		recent.Value.Blockhash,
		solana.TransactionPayer(publicKey),
	)
	if err != nil {
		log.Fatalf("failed to create transaction: %v", err)
	}

	// 签名交易
	_, err = tx.Sign(
		func(key solana.PublicKey) *solana.PrivateKey {
			if key.Equals(publicKey) {
				return &privateKey
			}
			return nil
		},
	)
	if err != nil {
		log.Fatalf("failed to sign transaction: %v", err)
	}

	// 打印交易信息（可选调试）
	fmt.Println(tx.Message)
	opts := rpc.TransactionOpts{
		SkipPreflight:       false,
		PreflightCommitment: rpc.CommitmentFinalized,
	}
	// 发送交易
	sig, err := client.SendTransactionWithOpts(
		context.Background(),
		tx,
		opts,
	)
	if err != nil {
		log.Fatalf("failed to send transaction: %v", err)
	}

	fmt.Printf("Transaction signature: %s\n", sig.String())

}

func TestGetIntentStatus(t *testing.T) {
	//resp1, err := relay.GetIntentStatus("0x3e82b8dd97d3e5d704dc77ad3ad2a3e10ad2e9c67c68ac71efcc610d4fee65b8")
	resp1, err := relay.GetStatus("0x868ebcdbfd378638ba36986ca4ae4b9c0bce033a3197878c2ca66cbc8c049bfa")
	if err != nil {
		log.Fatalf("failed to get intent status: %v", err)
	}
	fmt.Println(resp1)
}

func TestUsdc2Sol(t *testing.T) {
	req := &relay.QuoteRequest{
		User:                 "0x075Bb2a896cF978155a5bD85e9A3CF39B4D215eA",
		OriginChainId:        42161,
		DestinationChainId:   792703809,
		OriginCurrency:       "******************************************",
		DestinationCurrency:  "********************************",
		Recipient:            "********************************************",
		TradeType:            "EXACT_INPUT",
		Amount:               "********",
		Referrer:             "relay.link",
		UseExternalLiquidity: false,
		UseDepositAddress:    false,
		TopupGas:             false,
	}
	resp, err := relay.GetRelayQuote(req)
	if err != nil {
		t.Fatalf("GetRelayQuote error: %v", err)
	}
	if len(resp.Steps) == 0 || len(resp.Steps[0].Items) == 0 {
		t.Fatalf("no steps or items in response")
	}
	item := resp.Steps[0].Items[0]
	fmt.Println("RequestId", resp.Steps[0].RequestId)

	b, err := json.Marshal(item.Data)
	if err != nil {
		t.Fatalf("marshal item data error: %v", err)
	}
	var evmData EvmStepData
	if err := json.Unmarshal(b, &evmData); err != nil || evmData.From == "" {
		t.Fatalf("step item data is not EVM type or missing fields: %v", err)
	}
	if evmData.To == "" || evmData.Data == "" {
		t.Errorf("EVM step missing to/data fields")
	}
	txhash, err := SendEvmTx(&evmData,
		"",
		"https://summer-distinguished-breeze.arbitrum-mainnet.quiknode.pro/6681c4b31fa02ee860081babc885d918c856a012/")
	if err != nil {
		t.Fatalf("SendEvmTx error: %v", err)
	}
	fmt.Println(txhash)

}

type EvmStepData struct {
	From                 string `json:"from"`
	To                   string `json:"to"`
	Data                 string `json:"data"`
	Value                string `json:"value"`
	ChainId              int64  `json:"chainId"`
	Gas                  string `json:"gas"`
	MaxFeePerGas         string `json:"maxFeePerGas"`
	MaxPriorityFeePerGas string `json:"maxPriorityFeePerGas"`
}

func SendEvmTx(evmData *EvmStepData, privateKeyHex, rpcUrl string) (string, error) {
	client, err := ethclient.Dial(rpcUrl)
	if err != nil {
		return "", err
	}
	defer client.Close()

	privateKey, err := crypto.HexToECDSA(privateKeyHex)
	if err != nil {
		return "", err
	}
	fromAddress := common.HexToAddress(evmData.From)
	nonce, err := client.PendingNonceAt(context.Background(), fromAddress)
	if err != nil {
		return "", err
	}
	value := new(big.Int)
	value.SetString(evmData.Value, 10)
	gasLimit := uint64(0)
	fmt.Sscanf(evmData.Gas, "%d", &gasLimit)
	maxFeePerGas := new(big.Int)
	maxFeePerGas.SetString(evmData.MaxFeePerGas, 10)
	maxPriorityFeePerGas := new(big.Int)
	maxPriorityFeePerGas.SetString(evmData.MaxPriorityFeePerGas, 10)

	toAddress := common.HexToAddress(evmData.To)
	data, _ := hex.DecodeString(evmData.Data[2:]) // 去掉0x

	chainID := big.NewInt(evmData.ChainId)
	tx := types.NewTx(&types.DynamicFeeTx{
		ChainID:   chainID,
		Nonce:     nonce,
		GasTipCap: maxPriorityFeePerGas,
		GasFeeCap: maxFeePerGas,
		Gas:       gasLimit,
		To:        &toAddress,
		Value:     value,
		Data:      data,
	})

	signedTx, err := types.SignTx(tx, types.NewLondonSigner(chainID), privateKey)
	if err != nil {
		return "", err
	}
	err = client.SendTransaction(context.Background(), signedTx)
	if err != nil {
		return "", err
	}
	return signedTx.Hash().Hex(), nil
}

//config, err := relay.GetConfig("https://api.relay.link", 42161, 792703809, "9a4aP4wxxwQx2SJtLqdGFiAwnFigf3tfG2uKU4D47kJZ")
//if err != nil {
//    // 处理错误
//}
//fmt.Println(config)

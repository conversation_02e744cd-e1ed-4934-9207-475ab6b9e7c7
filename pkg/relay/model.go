package relay

import (
	"encoding/hex"
	"fmt"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/gagliardetto/solana-go"
	"math/big"
)

type QuoteRequest struct {
	User                 string `json:"user"`
	OriginChainId        int    `json:"originChainId"`
	DestinationChainId   int    `json:"destinationChainId"`
	OriginCurrency       string `json:"originCurrency"`
	DestinationCurrency  string `json:"destinationCurrency"`
	Recipient            string `json:"recipient"`
	TradeType            string `json:"tradeType"`
	Amount               string `json:"amount"`
	Referrer             string `json:"referrer"`
	UseExternalLiquidity bool   `json:"useExternalLiquidity"`
	UseDepositAddress    bool   `json:"useDepositAddress"`
	TopupGas             bool   `json:"topupGas"`
}

type QuoteResponse struct {
	Steps     []Step  `json:"steps"`
	Fees      Fees    `json:"fees"`
	Details   Details `json:"details"`
	Message   string  `json:"message"`
	ErrorCode string  `json:"errorCode"`
}

type Step struct {
	Id             string     `json:"id"`
	Action         string     `json:"action"`
	Description    string     `json:"description"`
	Kind           string     `json:"kind"`
	Items          []StepItem `json:"items"`
	RequestId      string     `json:"requestId"`
	DepositAddress string     `json:"depositAddress"`
}

type StepItem struct {
	Status string     `json:"status"`
	Data   StepData   `json:"data"`
	Check  *StepCheck `json:"check"`
}

type StepData struct {
	From                 string `json:"from"`
	To                   string `json:"to"`
	Data                 string `json:"data"`
	Value                string `json:"value"`
	ChainId              int    `json:"chainId"`
	Gas                  string `json:"gas"`
	MaxFeePerGas         string `json:"maxFeePerGas"`
	MaxPriorityFeePerGas string `json:"maxPriorityFeePerGas"`
	// no evm
	Instructions                []*Instruction `json:"instructions"`
	AddressLookupTableAddresses []string       `json:"addressLookupTableAddresses"`
}

func (evmData *StepData) GetTransitionData(nonce uint64) (string, string, error) {
	value := new(big.Int)
	value.SetString(evmData.Value, 10)
	gasLimit := uint64(0)
	fmt.Sscanf(evmData.Gas, "%d", &gasLimit)
	maxFeePerGas := new(big.Int)
	maxFeePerGas.SetString(evmData.MaxFeePerGas, 10)
	maxPriorityFeePerGas := new(big.Int)
	maxPriorityFeePerGas.SetString(evmData.MaxPriorityFeePerGas, 10)

	toAddress := common.HexToAddress(evmData.To)
	data, _ := hex.DecodeString(evmData.Data[2:]) // 去掉0x

	chainID := big.NewInt(int64(evmData.ChainId))
	tx := types.NewTx(&types.DynamicFeeTx{
		ChainID:   chainID,
		Nonce:     nonce,
		GasTipCap: maxPriorityFeePerGas,
		GasFeeCap: maxFeePerGas,
		Gas:       gasLimit,
		To:        &toAddress,
		Value:     value,
		Data:      data,
	})
	bytes, err := tx.MarshalJSON()
	if err != nil {
		return "", "", err
	}
	return string(bytes), tx.Hash().String(), nil
}

func (evmData *StepData) GetNoEvmTransitionData(blockhash *solana.Hash) (string, string, error) {
	var instructions []solana.Instruction
	for _, i2 := range evmData.Instructions {
		// 解析指令的 keys
		var instructionKeys []*solana.AccountMeta
		for _, key := range i2.Keys {
			instructionKeys = append(instructionKeys, &solana.AccountMeta{
				PublicKey:  solana.MustPublicKeyFromBase58(key.Pubkey),
				IsSigner:   key.IsSigner,
				IsWritable: key.IsWritable,
			})
		}
		data, err := hex.DecodeString(i2.Data)
		if err != nil {
			return "", "", fmt.Errorf("failed to decode instruction data: %w", err)
		}
		// 构造指令
		instruction := &solana.GenericInstruction{
			AccountValues: instructionKeys,
			ProgID:        solana.MustPublicKeyFromBase58(i2.ProgramId),
			DataBytes:     data,
		}

		instructions = append(instructions, instruction)

	}
	// 构造交易
	tx, err := solana.NewTransaction(
		instructions,
		*blockhash,
	)
	if err != nil {
		return "", "", fmt.Errorf("failed to create transaction: %v", err)
	}
	txHash, err := tx.ToBase64()
	if err != nil {
		return "", "", fmt.Errorf("failed to encode transaction: %v", err)
	}
	data, err := tx.Message.MarshalJSON()
	if err != nil {
		return "", "", fmt.Errorf("failed to encode transaction: %v", err)
	}
	return string(data), txHash, err
}

type Instruction struct {
	Keys      []InstructionKey `json:"keys"`
	ProgramId string           `json:"programId"`
	Data      string           `json:"data"`
}

type InstructionKey struct {
	Pubkey     string `json:"pubkey"`
	IsSigner   bool   `json:"isSigner"`
	IsWritable bool   `json:"isWritable"`
}

type StepCheck struct {
	Endpoint string `json:"endpoint"`
	Method   string `json:"method"`
}

type CurrencyMetadata struct {
	LogoURI  string `json:"logoURI"`
	Verified bool   `json:"verified"`
}

type Currency struct {
	ChainId  int64            `json:"chainId"`
	Address  string           `json:"address"`
	Symbol   string           `json:"symbol"`
	Name     string           `json:"name"`
	Decimals int              `json:"decimals"`
	Metadata CurrencyMetadata `json:"metadata"`
}

type FeeItem struct {
	Currency        Currency `json:"currency"`
	Amount          string   `json:"amount"`
	AmountFormatted string   `json:"amountFormatted"`
	AmountUsd       string   `json:"amountUsd"`
	MinimumAmount   string   `json:"minimumAmount"`
}

type Fees struct {
	Gas            FeeItem `json:"gas"`
	Relayer        FeeItem `json:"relayer"`
	RelayerGas     FeeItem `json:"relayerGas"`
	RelayerService FeeItem `json:"relayerService"`
	App            FeeItem `json:"app"`
}

type Details struct {
	Operation  string `json:"operation"`
	Sender     string `json:"sender"`
	Recipient  string `json:"recipient"`
	CurrencyIn struct {
		Currency        Currency `json:"currency"`
		Amount          string   `json:"amount"`
		AmountFormatted string   `json:"amountFormatted"`
		AmountUsd       string   `json:"amountUsd"`
		MinimumAmount   string   `json:"minimumAmount"`
	} `json:"currencyIn"`
	CurrencyOut struct {
		Currency        Currency `json:"currency"`
		Amount          string   `json:"amount"`
		AmountFormatted string   `json:"amountFormatted"`
		AmountUsd       string   `json:"amountUsd"`
		MinimumAmount   string   `json:"minimumAmount"`
	} `json:"currencyOut"`
	CurrencyGasTopup struct {
		Currency struct {
			ChainId  int    `json:"chainId"`
			Address  string `json:"address"`
			Symbol   string `json:"symbol"`
			Name     string `json:"name"`
			Decimals int    `json:"decimals"`
			Metadata struct {
				LogoURI  string `json:"logoURI"`
				Verified bool   `json:"verified"`
			} `json:"metadata"`
		} `json:"currency"`
		Amount          string `json:"amount"`
		AmountFormatted string `json:"amountFormatted"`
		AmountUsd       string `json:"amountUsd"`
		MinimumAmount   string `json:"minimumAmount"`
	} `json:"currencyGasTopup"`
	TotalImpact struct {
		Usd     string `json:"usd"`
		Percent string `json:"percent"`
	} `json:"totalImpact"`
	SwapImpact struct {
		Usd     string `json:"usd"`
		Percent string `json:"percent"`
	} `json:"swapImpact"`
	Rate              string `json:"rate"`
	SlippageTolerance struct {
		Origin struct {
			Usd     string `json:"usd"`
			Value   string `json:"value"`
			Percent string `json:"percent"`
		} `json:"origin"`
		Destination struct {
			Usd     string `json:"usd"`
			Value   string `json:"value"`
			Percent string `json:"percent"`
		} `json:"destination"`
	} `json:"slippageTolerance"`
	TimeEstimate float64 `json:"timeEstimate"`
	UserBalance  string  `json:"userBalance"`
}
type IntentStatusResponse struct {
	Status             string   `json:"status"`
	InTxHashes         []string `json:"inTxHashes"`
	TxHashes           []string `json:"txHashes"`
	Time               int64    `json:"time"`
	OriginChainId      int64    `json:"originChainId"`
	DestinationChainId int64    `json:"destinationChainId"`
}

type MetaData struct {
	Chains []*Chain `json:"chains"`
}

type Chain struct {
	Id                     int             `json:"id"`
	Name                   string          `json:"name"`
	DisplayName            string          `json:"displayName"`
	HttpRpcUrl             string          `json:"httpRpcUrl"`
	WsRpcUrl               string          `json:"wsRpcUrl"`
	ExplorerUrl            string          `json:"explorerUrl"`
	ExplorerName           string          `json:"explorerName"`
	DepositEnabled         bool            `json:"depositEnabled"`
	TokenSupport           string          `json:"tokenSupport"`
	Disabled               bool            `json:"disabled"`
	PartialDisableLimit    int             `json:"partialDisableLimit"`
	BlockProductionLagging bool            `json:"blockProductionLagging"`
	Currency               ChainCurrency   `json:"currency"`
	WithdrawalFee          int             `json:"withdrawalFee"`
	DepositFee             int             `json:"depositFee"`
	SurgeEnabled           bool            `json:"surgeEnabled"`
	FeaturedTokens         []FeaturedToken `json:"featuredTokens"`
	Erc20Currencies        []Erc20Currency `json:"erc20Currencies"`
	IconUrl                string          `json:"iconUrl"`
	Contracts              ChainContracts  `json:"contracts"`
	VmType                 string          `json:"vmType"`
	BaseChainId            int             `json:"baseChainId"`
	SolverAddresses        []string        `json:"solverAddresses"`
	Tags                   []interface{}   `json:"tags"`
	LogoUrl                string          `json:"logoUrl,omitempty"`
	BrandColor             string          `json:"brandColor,omitempty"`
	ExplorerPaths          *ExplorerPaths  `json:"explorerPaths,omitempty"`
}

type ChainCurrency struct {
	Id               string `json:"id"`
	Symbol           string `json:"symbol"`
	Name             string `json:"name"`
	Address          string `json:"address"`
	Decimals         int    `json:"decimals"`
	SupportsBridging bool   `json:"supportsBridging"`
}

type FeaturedToken struct {
	Id               string           `json:"id,omitempty"`
	Symbol           string           `json:"symbol"`
	Name             string           `json:"name"`
	Address          string           `json:"address"`
	Decimals         int              `json:"decimals"`
	SupportsBridging bool             `json:"supportsBridging"`
	Metadata         FeaturedMetadata `json:"metadata"`
}

type FeaturedMetadata struct {
	LogoURI string `json:"logoURI,omitempty"`
}

type Erc20Currency struct {
	Id               string `json:"id"`
	Symbol           string `json:"symbol"`
	Name             string `json:"name"`
	Address          string `json:"address"`
	Decimals         int    `json:"decimals"`
	SupportsBridging bool   `json:"supportsBridging"`
	WithdrawalFee    int    `json:"withdrawalFee"`
	DepositFee       int    `json:"depositFee"`
	SurgeEnabled     bool   `json:"surgeEnabled"`
	SupportsPermit   bool   `json:"supportsPermit,omitempty"`
}

type ChainContracts struct {
	Multicall3           string `json:"multicall3"`
	Multicaller          string `json:"multicaller"`
	OnlyOwnerMulticaller string `json:"onlyOwnerMulticaller"`
	RelayReceiver        string `json:"relayReceiver"`
	Erc20Router          string `json:"erc20Router"`
	ApprovalProxy        string `json:"approvalProxy"`
}

type ExplorerPaths struct {
	Transaction string `json:"transaction"`
}
type GetConfigResponse struct {
	Enabled                   bool         `json:"enabled"`
	User                      UserConfig   `json:"user"`
	Fee                       string       `json:"fee"`
	Solver                    SolverConfig `json:"solver"`
	SupportsExternalLiquidity bool         `json:"supportsExternalLiquidity"`
}

type UserConfig struct {
	Balance string `json:"balance"`
}

type SolverConfig struct {
	Address            string `json:"address"`
	Balance            string `json:"balance"`
	CapacityPerRequest string `json:"capacityPerRequest"`
}

package symbol

import "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"

type SymbolStatistic struct {
	Symbol            string  `json:"symbol"`
	MaxLeverage       int     `json:"maxLeverage"`
	Funding           float64 `json:"funding"`
	MarketCap         float64 `json:"marketCap"`
	TotalSupply       float64 `json:"totalSupply"`
	CirculatingSupply float64 `json:"circulatingSupply"`
	OpenInterest      float64 `json:"openInterest"`
	PrevDayPx         float64 `json:"prevDayPx"`
	DayNtlVlm         float64 `json:"volume"`
	MarkPx            float64 `json:"currentPrice"`
	ChangePx          float64 `json:"changePx"`
	ChangePxPercent   float64 `json:"changPxPercent"`
}

type CategoryList struct {
	Dex       []string
	Trending  []string
	PreLaunch []string
	AI        []string
	Layer1    []string
	Layer2    []string
	Defi      []string
	Gaming    []string
	Meme      []string
}

type CategoryDetail struct {
	Dex       []SymbolStatistic
	Trending  []SymbolStatistic
	PreLaunch []SymbolStatistic
	Ai        []SymbolStatistic
	Layer1    []SymbolStatistic
	Layer2    []SymbolStatistic
	Defi      []SymbolStatistic
	Gaming    []SymbolStatistic
	Meme      []SymbolStatistic
}

var OverallDataMap = make(map[string]model.Coin)

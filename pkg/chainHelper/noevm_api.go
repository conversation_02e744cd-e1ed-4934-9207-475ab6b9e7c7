package chainHelper

import (
	"context"
	"fmt"
	bin "github.com/gagliardetto/binary"
	"github.com/gagliardetto/solana-go"
	"log"

	"github.com/gagliardetto/solana-go/rpc"
)

const (
	SOLANA_PRIVATE_KEY = ""
)

func SendNoEvmTransaction(SerializedMessage []int) (string, error) {
	// 1.link to Solana RPC
	//rpcClient := rpc.New(rpc.MainNetBeta_RPC)
	rpcClient := rpc.New(SOLANA_Url)

	// 2. get the last blockhash
	blockhashResp, err := rpcClient.GetLatestBlockhash(context.Background(), rpc.CommitmentFinalized)
	if err != nil {
		log.Fatalf("get blockhash fail: %v", err)
	}
	recentBlockhash := blockhashResp.Value.Blockhash
	fmt.Println("the latest blockhash:", recentBlockhash)

	// 3. convert []int to []byte
	messageBytes := make([]byte, len(SerializedMessage))
	for i, v := range SerializedMessage {
		messageBytes[i] = byte(v)
	}

	// 4. Deserialize the transaction
	tx, err := solana.TransactionFromDecoder(bin.NewBinDecoder(messageBytes))
	if err != nil {
		log.Fatalf("fail to deserialize the transaction: %v", err)
	}

	// 5. replace recentBlockhash
	tx.Message.RecentBlockhash = recentBlockhash

	// 6. Sign the transaction with the private key.
	signer, err := solana.PrivateKeyFromBase58(SOLANA_PRIVATE_KEY)
	if err != nil {
		log.Fatalf("failed to parse the private key.: %v", err)
	}

	_, err = tx.Sign(func(key solana.PublicKey) *solana.PrivateKey {
		if key.Equals(signer.PublicKey()) {
			return &signer
		}
		return nil
	})
	if err != nil {
		log.Fatalf("failed to sign the transaction: %v", err)
	}

	// 8. SendTransaction
	txSignature, err := rpcClient.SendTransaction(context.Background(), tx)
	if err != nil {
		log.Fatalf("fail to send transaction: %v", err)
	}
	fmt.Println("send transaction over, Signature:", txSignature)

	return txSignature.String(), nil
}

func GetSolBlockHash() (solana.Hash, error) {
	// 1.link to Solana RPC
	rpcClient := rpc.New(SOLANA_Url)

	// 2. get the last blockhash
	blockhashResp, err := rpcClient.GetLatestBlockhash(context.Background(), rpc.CommitmentFinalized)
	if err != nil {
		log.Fatalf("get blockhash fail: %v", err)
	}
	recentBlockhash := blockhashResp.Value.Blockhash
	fmt.Println("the latest blockhash:", recentBlockhash)

	return recentBlockhash, nil
}

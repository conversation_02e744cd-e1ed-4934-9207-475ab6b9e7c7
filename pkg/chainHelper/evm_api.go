package chainHelper

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
)

func Url(chainHexId string) string {
	switch chainHexId {
	case "0x1":
		return ETH_Url
	case "0x38":
		return BSC_Url
	case "0x89":
		return POLYGON_Url
	case "0xa4b1":
		return ARBITRUM_Url
	case "SOLANA":
		return SOLANA_Url
	default:
		return ""
	}
}

func GetEthUserNonce(chainId, address string) (string, error) {
	url := Url(chainId)
	payload := map[string]interface{}{
		"jsonrpc": "2.0",
		"id":      1,
		"method":  "eth_getTransactionCount",
		"params":  []interface{}{address, "latest"},
	}
	body, _ := json.Marshal(payload)
	resp, err := http.Post(url, "application/json", bytes.NewBuffer(body))
	if err != nil {
		fmt.Println("Request error:", err)
		return "", err
	}
	defer resp.Body.Close()
	respBody, _ := ioutil.ReadAll(resp.Body)

	var response struct {
		Id      int    `json:"id"`
		Jsonrpc string `json:"jsonrpc"`
		Result  string `json:"result"`
		Error   struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		} `json:"error"`
	}
	err = json.Unmarshal(respBody, &response)
	if err != nil {
		fmt.Println("Error unmarshalling response:", err)
		return "", err
	}
	return response.Result, err
}

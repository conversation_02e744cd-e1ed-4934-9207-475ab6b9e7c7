-- Rename a column from "address_patterns" to "address_patterns "
ALTER TABLE "public"."rango_meta_chains" RENAME COLUMN "address_patterns" TO "address_patterns ";
-- Create "user_notification_settings" table
CREATE TABLE "public"."user_notification_settings" (
  "id" uuid NOT NULL,
  "user_id" uuid NULL,
  "symbol" text NULL,
  "type" text NULL,
  "value" numeric NULL,
  "create_at" timestamp NULL,
  "update_at" timestamp NULL,
  "is_active" boolean NULL,
  "is_reminder_once" boolean NULL,
  "is_deleted" boolean NULL DEFAULT false,
  "note" text NULL,
  PRIMARY KEY ("id")
);
-- <PERSON><PERSON> index "idx_symbol" to table: "user_notification_settings"
CREATE INDEX "idx_symbol" ON "public"."user_notification_settings" ("symbol");
-- <PERSON><PERSON> index "idx_user" to table: "user_notification_settings"
CREATE INDEX "idx_user" ON "public"."user_notification_settings" ("user_id");

#### Install atlas cli

- macOS
```
brew install ariga/tap/atlas
```

- linux
```
curl -sSf https://atlasgo.sh | sh
```

### Development flow
- Use local connection for development.
- Don't apply migration to shared environment, it will be applied by CD or Devops

1. Write your model first, and add it to atlasloader
2. Generate migration file
```
$ make db-diff
```

3. Apply your migration to local
```
$ make db-apply
```
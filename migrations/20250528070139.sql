-- Create "order" table
CREATE TABLE "public"."order" (
  "id" uuid NOT NULL,
  "created_at" timestamptz NULL,
  "update_at" timestamptz NULL,
  "user_id" uuid NULL,
  "oid" uuid NULL,
  "type" character varying(100) NULL,
  "symbol" text NULL,
  "direction" text NULL,
  "status" text NULL,
  "size" numeric NULL,
  "origin_size" numeric NULL,
  "order_px" numeric NULL,
  "trigger_px" numeric NULL,
  "reduce_only" boolean NULL,
  PRIMARY KEY ("id")
);
-- Create "position" table
CREATE TABLE "public"."position" (
  "id" uuid NOT NULL,
  "created_at" timestamptz NULL,
  "update_at" timestamptz NULL,
  "symbol" text NULL,
  "user_id" uuid NULL,
  "type" text NULL,
  "size" numeric NULL,
  "position_value" numeric NULL,
  "entry_px" numeric NULL,
  "liquidation_px" numeric NULL,
  "margin" numeric NULL,
  "funding_fee" numeric NULL,
  PRIMARY KEY ("id")
);
-- <PERSON><PERSON> index "idx_symbol_user" to table: "position"
CREATE UNIQUE INDEX "idx_symbol_user" ON "public"."position" ("symbol", "user_id");

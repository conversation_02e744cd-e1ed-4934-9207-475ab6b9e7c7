-- Create "coin" table
CREATE TABLE "public"."coin" (
  "symbol" text NOT NULL,
  "sz_decimals" bigint NULL,
  "max_leverage" bigint NULL,
  "margin_table_id" bigint NULL,
  "is_delisted" boolean NULL,
  "only_isolated" boolean NULL,
  "last_updated" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("symbol")
);
-- Create "coin_statistic" table
CREATE TABLE "public"."coin_statistic" (
  "symbol" text NOT NULL,
  "market_cap" numeric NULL,
  "total_supply" numeric NULL,
  "funding" numeric NULL,
  "open_interest" numeric NULL,
  "prev_day_px" numeric NULL,
  "day_ntl_vlm" numeric NULL,
  "premium" numeric NULL,
  "oracle_px" numeric NULL,
  "mark_px" numeric NULL,
  "mid_px" numeric NULL,
  "impact_px_bid" numeric NULL,
  "impact_px_ask" numeric NULL,
  "day_base_vlm" numeric NULL,
  "change_px" numeric NULL,
  "change_px_percent" numeric NULL,
  "last_updated" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("symbol")
);

-- Create "rango_affiliate" table
CREATE TABLE "public"."rango_affiliate" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "created_at" timestamptz NULL,
  "updated_at" timestamptz NULL,
  "deleted_at" timestamptz NULL,
  "chain" text NULL,
  "percent" text NULL,
  "wallet" text NULL,
  "status" bigint NULL,
  PRIMARY KEY ("id")
);
-- Create index "idx_rango_affiliate_chain" to table: "rango_affiliate"
CREATE UNIQUE INDEX "idx_rango_affiliate_chain" ON "public"."rango_affiliate" ("chain");
-- Create index "idx_rango_affiliate_deleted_at" to table: "rango_affiliate"
CREATE INDEX "idx_rango_affiliate_deleted_at" ON "public"."rango_affiliate" ("deleted_at");
-- Create index "idx_rango_affiliate_percent" to table: "rango_affiliate"
CREATE INDEX "idx_rango_affiliate_percent" ON "public"."rango_affiliate" ("percent");
-- Create index "idx_rango_affiliate_status" to table: "rango_affiliate"
CREATE INDEX "idx_rango_affiliate_status" ON "public"."rango_affiliate" ("status");
-- Create "rango_history" table
CREATE TABLE "public"."rango_history" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "created_at" timestamptz NULL,
  "updated_at" timestamptz NULL,
  "deleted_at" timestamptz NULL,
  "request_id" text NULL,
  "user_address" text NULL,
  "from_blockchain" text NULL,
  "from_symbol" text NULL,
  "from_address" text NULL,
  "to_blockchain" text NULL,
  "to_symbol" text NULL,
  "to_address" text NULL,
  "request_amount" text NULL,
  "output_amount" text NULL,
  "result_type" text NULL,
  "validation_status" text NULL,
  "wallet_not_supporting_from_blockchain" boolean NULL,
  "missing_blockchains" text NULL,
  "diagnosis_messages" text NULL,
  "status" text NULL,
  "step" bigint NULL,
  "fail_reason" text NULL,
  PRIMARY KEY ("id")
);
-- Create index "idx_rango_history_deleted_at" to table: "rango_history"
CREATE INDEX "idx_rango_history_deleted_at" ON "public"."rango_history" ("deleted_at");
-- Create index "idx_rango_history_request_id" to table: "rango_history"
CREATE UNIQUE INDEX "idx_rango_history_request_id" ON "public"."rango_history" ("request_id");
-- Create index "idx_rango_history_status" to table: "rango_history"
CREATE INDEX "idx_rango_history_status" ON "public"."rango_history" ("status");
-- Create "rango_meta_chains" table
CREATE TABLE "public"."rango_meta_chains" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "created_at" timestamptz NULL,
  "updated_at" timestamptz NULL,
  "deleted_at" timestamptz NULL,
  "name" text NULL,
  "chain_id" text NULL,
  "default_decimals" bigint NULL,
  "address_patterns " text NULL,
  "fee_assets" bytea NULL,
  "logo" text NULL,
  "display_name" text NULL,
  "short_name" text NULL,
  "sort" bigint NULL,
  "color" text NULL,
  "enabled" boolean NULL,
  "type" text NULL,
  "info" bytea NULL,
  PRIMARY KEY ("id")
);
-- Create index "idx_rango_meta_chains_chain_id" to table: "rango_meta_chains"
CREATE INDEX "idx_rango_meta_chains_chain_id" ON "public"."rango_meta_chains" ("chain_id");
-- Create index "idx_rango_meta_chains_deleted_at" to table: "rango_meta_chains"
CREATE INDEX "idx_rango_meta_chains_deleted_at" ON "public"."rango_meta_chains" ("deleted_at");
-- Create index "idx_rango_meta_chains_name" to table: "rango_meta_chains"
CREATE UNIQUE INDEX "idx_rango_meta_chains_name" ON "public"."rango_meta_chains" ("name");
-- Create "rango_meta_swappers" table
CREATE TABLE "public"."rango_meta_swappers" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "created_at" timestamptz NULL,
  "updated_at" timestamptz NULL,
  "deleted_at" timestamptz NULL,
  "swapper_id" text NULL,
  "title" text NULL,
  "logo" text NULL,
  "swapper_group" text NULL,
  "types" text NULL,
  "enabled" boolean NULL,
  PRIMARY KEY ("id")
);
-- Create index "idx_rango_meta_swappers_deleted_at" to table: "rango_meta_swappers"
CREATE INDEX "idx_rango_meta_swappers_deleted_at" ON "public"."rango_meta_swappers" ("deleted_at");
-- Create index "idx_rango_meta_swappers_enabled" to table: "rango_meta_swappers"
CREATE INDEX "idx_rango_meta_swappers_enabled" ON "public"."rango_meta_swappers" ("enabled");
-- Create index "idx_rango_meta_swappers_swapper_id" to table: "rango_meta_swappers"
CREATE UNIQUE INDEX "idx_rango_meta_swappers_swapper_id" ON "public"."rango_meta_swappers" ("swapper_id");
-- Create "rango_meta_tokens" table
CREATE TABLE "public"."rango_meta_tokens" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "created_at" timestamptz NULL,
  "updated_at" timestamptz NULL,
  "deleted_at" timestamptz NULL,
  "address" text NULL,
  "block_chain" text NULL,
  "symbol" text NULL,
  "name" text NULL,
  "image" text NULL,
  "usd_price" numeric NULL,
  "decimals" bigint NULL,
  "is_popular" boolean NULL,
  "is_secondary_coin" boolean NULL,
  "coin_source" text NULL,
  "coin_source_url" text NULL,
  "supported_swappers" text NULL,
  PRIMARY KEY ("id")
);
-- Create index "idx_chain_addr_symbol" to table: "rango_meta_tokens"
CREATE UNIQUE INDEX "idx_chain_addr_symbol" ON "public"."rango_meta_tokens" ("address", "block_chain", "symbol");
-- Create index "idx_rango_meta_tokens_deleted_at" to table: "rango_meta_tokens"
CREATE INDEX "idx_rango_meta_tokens_deleted_at" ON "public"."rango_meta_tokens" ("deleted_at");
-- Create index "idx_rango_meta_tokens_name" to table: "rango_meta_tokens"
CREATE INDEX "idx_rango_meta_tokens_name" ON "public"."rango_meta_tokens" ("name");
-- Create "rango_swaps" table
CREATE TABLE "public"."rango_swaps" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "created_at" timestamptz NULL,
  "updated_at" timestamptz NULL,
  "deleted_at" timestamptz NULL,
  "request_id" text NULL,
  "user_address" text NULL,
  "swapper_id" text NULL,
  "swapper_logo" text NULL,
  "swapper_type" text NULL,
  "from_amount" text NULL,
  "to_amount" text NULL,
  "fee" bytea NULL,
  "estimated_time_in_seconds" bigint NULL,
  "swap_chain_type" text NULL,
  "max_required_sign" bigint NULL,
  "step" bigint NULL,
  "call_data" bytea NULL,
  "call_data_hash" text NULL,
  "tx_hash" text NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_rango_history_swaps" FOREIGN KEY ("request_id") REFERENCES "public"."rango_history" ("request_id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_rango_swaps_deleted_at" to table: "rango_swaps"
CREATE INDEX "idx_rango_swaps_deleted_at" ON "public"."rango_swaps" ("deleted_at");
-- Create index "idx_rango_swaps_request_id" to table: "rango_swaps"
CREATE INDEX "idx_rango_swaps_request_id" ON "public"."rango_swaps" ("request_id");
-- Create index "idx_rango_swaps_step" to table: "rango_swaps"
CREATE INDEX "idx_rango_swaps_step" ON "public"."rango_swaps" ("step");
-- Create index "idx_rango_swaps_swap_chain_type" to table: "rango_swaps"
CREATE INDEX "idx_rango_swaps_swap_chain_type" ON "public"."rango_swaps" ("swap_chain_type");
-- Create index "idx_rango_swaps_swapper_id" to table: "rango_swaps"
CREATE INDEX "idx_rango_swaps_swapper_id" ON "public"."rango_swaps" ("swapper_id");
-- Create index "idx_rango_swaps_swapper_type" to table: "rango_swaps"
CREATE INDEX "idx_rango_swaps_swapper_type" ON "public"."rango_swaps" ("swapper_type");
-- Create index "idx_rango_swaps_user_address" to table: "rango_swaps"
CREATE INDEX "idx_rango_swaps_user_address" ON "public"."rango_swaps" ("user_address");

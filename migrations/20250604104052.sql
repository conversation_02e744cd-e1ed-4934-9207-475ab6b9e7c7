-- Modify "coin" table
ALTER TABLE "public"."coin" ADD COLUMN "address" text NULL;
-- Create "user_balance" table
CREATE TABLE "public"."user_balance" (
  "user_id" uuid NOT NULL,
  "symbol" text NULL,
  "balance" numeric NULL,
  "created_at" timestamptz NULL,
  PRIMARY KEY ("user_id")
);
-- <PERSON><PERSON> index "idx_user_symbol" to table: "user_balance"
CREATE UNIQUE INDEX "idx_user_symbol" ON "public"."user_balance" ("user_id", "symbol");
-- Create "user_wallet" table
CREATE TABLE "public"."user_wallet" (
  "user_id" uuid NOT NULL,
  "created_at" timestamptz NULL,
  "wallet_address" text NULL,
  PRIMARY KEY ("user_id")
);

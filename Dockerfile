FROM golang:1.23 AS builder

WORKDIR /app

COPY go.mod go.sum ./
RUN go mod download

COPY . .

RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o /build/app cmd/app/main.go
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o /build/graphql cmd/graphql/main.go
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o /build/scheduler cmd/scheduler/main.go
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o /build/hyperliquid_worker cmd/hyperliquid_worker/main.go
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o /build/notification_worker cmd/notification_worker/main.go
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o /build/rango_worker cmd/rango_worker/main.go
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o /build/cli cmd/cli/main.go

FROM alpine:latest
WORKDIR /app

RUN apk add --no-cache bash

RUN wget https://release.ariga.io/atlas/atlas-linux-amd64-latest -O /usr/local/bin/atlas \
       && chmod +x /usr/local/bin/atlas

COPY --from=builder /build/ /app/
COPY --from=builder /app/config.yaml /app/config.yaml
COPY migrations/ migrations/

COPY entrypoint.sh ./entrypoint.sh
RUN chmod +x ./entrypoint.sh

EXPOSE 9000

ENTRYPOINT ["./entrypoint.sh"]
CMD ["./app"]

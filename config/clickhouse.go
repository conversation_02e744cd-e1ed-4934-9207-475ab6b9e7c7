package config

import "fmt"

type Clickhouse struct {
	GeneralDB `yaml:",inline" mapstructure:",squash"`
}

// Dsn get dsn based on configuration file
func (p *GeneralDB) Dsn() string {
	if p.IsSecure {
		return fmt.Sprintf("https://%s:%s@%s:%s/%s?secure=true&skip_verify=true",
			p.Username, p.Password, p.Path, p.Port, p.Dbname)
	} else {
		return fmt.Sprintf("http://%s:%s@%s:%s/%s?secure=false&skip_verify=true",
			p.Username, p.Password, p.Path, p.Port, p.Dbname)
	}
}

// LinkDsn generate dsn based on dbname
func (p *GeneralDB) LinkDsn(dbname string) string {
	if p.IsSecure {
		return fmt.Sprintf("https://%s:%s@%s:%s/%s?secure=true&skip_verify=true",
			p.Username, p.Password, p.Path, p.Port, p.Dbname)
	} else {
		return fmt.Sprintf("http://%s:%s@%s:%s/%s?secure=false&skip_verify=true",
			p.Username, p.Password, p.Path, p.Port, p.Dbname)
	}
}

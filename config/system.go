package config

type System struct {
	DbType        string `mapstructure:"db-type" json:"db-type" yaml:"db-type"` // Database type: mysql (default) | sqlite | sqlserver | postgresql
	OssType       string `mapstructure:"oss-type" json:"oss-type" yaml:"oss-type"`
	RouterPrefix  string `mapstructure:"router-prefix" json:"router-prefix" yaml:"router-prefix"`
	GraphqlPrefix string `mapstructure:"graphql-prefix" json:"graphql-prefix" yaml:"graphql-prefix"`
	Addr          int    `mapstructure:"addr" json:"addr" yaml:"addr"` // port value
	LimitCountIP  int    `mapstructure:"iplimit-count" json:"iplimit-count" yaml:"iplimit-count"`
	LimitTimeIP   int    `mapstructure:"iplimit-time" json:"iplimit-time" yaml:"iplimit-time"`
	UseMultipoint bool   `mapstructure:"use-multipoint" json:"use-multipoint" yaml:"use-multipoint"` // multi login blocking
	UseRedis      bool   `mapstructure:"use-redis" json:"use-redis" yaml:"use-redis"`
	UseMongo      bool   `mapstructure:"use-mongo" json:"use-mongo" yaml:"use-mongo"`
	UseStrictAuth bool   `mapstructure:"use-strict-auth" json:"use-strict-auth" yaml:"use-strict-auth"` // use tree role assignment mode
}

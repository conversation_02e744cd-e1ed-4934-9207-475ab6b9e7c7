package config

type Redis struct {
	Name         string   `mapstructure:"name" json:"name" yaml:"name"`                         // Represents the name of the current instance
	Addr         string   `mapstructure:"addr" json:"addr" yaml:"addr"`                         // Server address:port
	Password     string   `mapstructure:"password" json:"password" yaml:"password"`             // Password
	DB           int      `mapstructure:"db" json:"db" yaml:"db"`                               // Which database of Redis in single-instance mode
	UseCluster   bool     `mapstructure:"useCluster" json:"useCluster" yaml:"useCluster"`       // Whether to use cluster mode
	ClusterAddrs []string `mapstructure:"clusterAddrs" json:"clusterAddrs" yaml:"clusterAddrs"` // List of node addresses in cluster mode
}

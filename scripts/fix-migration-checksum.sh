#!/bin/bash

# Fix Migration Checksum Script
# This script fixes Atlas migration checksum errors

set -e

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Change to project directory
cd "$PROJECT_DIR"

echo "🔧 Fixing Atlas migration checksum errors..."

# Check if migrations directory exists
if [ ! -d "migrations" ]; then
    echo "❌ Error: migrations directory not found!"
    exit 1
fi

# Re-hash migration files
echo "📝 Re-hashing migration files..."
atlas migrate hash --dir file://migrations

# Validate migrations
echo "✅ Validating migrations..."
atlas migrate validate --dir file://migrations

echo "🎉 Migration checksum fixed successfully!"
echo ""
echo "You can now:"
echo "  - Deploy to unstable: git push origin unstable"
echo "  - Apply migrations locally: make db-apply"
echo "  - Apply migrations with Dock<PERSON>: make db-apply-docker"

#!/bin/bash

# Load Environment Variables Script
# This script loads environment variables from the appropriate .env file

set -e

# Default environment
ENV=${1:-local}

# Environment file paths
ENV_DIR="env"
ENV_FILE="${ENV_DIR}/${ENV}.env"

# Check if environment file exists
if [ ! -f "$ENV_FILE" ]; then
    echo "Error: Environment file '$ENV_FILE' not found!"
    echo "Available environments:"
    ls -1 "$ENV_DIR"/*.env 2>/dev/null | sed 's|.*/||; s|\.env$||' || echo "No environment files found in $ENV_DIR/"
    exit 1
fi

# Load environment variables
echo "Loading environment variables from: $ENV_FILE"
export $(grep -v '^#' "$ENV_FILE" | grep -v '^$' | xargs)

# Verify critical variables are set
REQUIRED_VARS=(
    "POSTGRES_HOST"
    "POSTGRES_PORT"
    "POSTGRES_USER"
    "POSTGRES_PASS"
    "POSTGRES_DB"
    "SERVER_PORT"
)

echo "Verifying required environment variables..."
for var in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!var}" ]; then
        echo "Error: Required environment variable '$var' is not set!"
        exit 1
    fi
done

echo "Environment variables loaded successfully!"
echo "Environment: $ENV"
echo "Database: ${POSTGRES_USER}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}"
echo "Server: ${SERVER_HOST:-0.0.0.0}:${SERVER_PORT}"

# Export environment for use in other scripts
export XBIT_AGENT_ENV="$ENV"

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: proto/user/v1/signing.proto

package user_signing

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	UserSigningService_SignEvmTransaction_FullMethodName         = "/user.signing.v1.UserSigningService/SignEvmTransaction"
	UserSigningService_SignTransaction_FullMethodName            = "/user.signing.v1.UserSigningService/SignTransaction"
	UserSigningService_ApproveWithdrawTransaction_FullMethodName = "/user.signing.v1.UserSigningService/ApproveWithdrawTransaction"
)

// UserSigningServiceClient is the client API for UserSigningService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserSigningServiceClient interface {
	SignEvmTransaction(ctx context.Context, in *SignUserTransactionEvmRequest, opts ...grpc.CallOption) (*SignUserTransactionEvmResponse, error)
	SignTransaction(ctx context.Context, in *SignUserTransactionRequest, opts ...grpc.CallOption) (*SignUserTransactionResponse, error)
	ApproveWithdrawTransaction(ctx context.Context, in *ApproveWithdrawRequest, opts ...grpc.CallOption) (*ApproveWithdrawResponse, error)
}

type userSigningServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserSigningServiceClient(cc grpc.ClientConnInterface) UserSigningServiceClient {
	return &userSigningServiceClient{cc}
}

func (c *userSigningServiceClient) SignEvmTransaction(ctx context.Context, in *SignUserTransactionEvmRequest, opts ...grpc.CallOption) (*SignUserTransactionEvmResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SignUserTransactionEvmResponse)
	err := c.cc.Invoke(ctx, UserSigningService_SignEvmTransaction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userSigningServiceClient) SignTransaction(ctx context.Context, in *SignUserTransactionRequest, opts ...grpc.CallOption) (*SignUserTransactionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SignUserTransactionResponse)
	err := c.cc.Invoke(ctx, UserSigningService_SignTransaction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userSigningServiceClient) ApproveWithdrawTransaction(ctx context.Context, in *ApproveWithdrawRequest, opts ...grpc.CallOption) (*ApproveWithdrawResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ApproveWithdrawResponse)
	err := c.cc.Invoke(ctx, UserSigningService_ApproveWithdrawTransaction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserSigningServiceServer is the server API for UserSigningService service.
// All implementations must embed UnimplementedUserSigningServiceServer
// for forward compatibility.
type UserSigningServiceServer interface {
	SignEvmTransaction(context.Context, *SignUserTransactionEvmRequest) (*SignUserTransactionEvmResponse, error)
	SignTransaction(context.Context, *SignUserTransactionRequest) (*SignUserTransactionResponse, error)
	ApproveWithdrawTransaction(context.Context, *ApproveWithdrawRequest) (*ApproveWithdrawResponse, error)
	mustEmbedUnimplementedUserSigningServiceServer()
}

// UnimplementedUserSigningServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedUserSigningServiceServer struct{}

func (UnimplementedUserSigningServiceServer) SignEvmTransaction(context.Context, *SignUserTransactionEvmRequest) (*SignUserTransactionEvmResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignEvmTransaction not implemented")
}
func (UnimplementedUserSigningServiceServer) SignTransaction(context.Context, *SignUserTransactionRequest) (*SignUserTransactionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignTransaction not implemented")
}
func (UnimplementedUserSigningServiceServer) ApproveWithdrawTransaction(context.Context, *ApproveWithdrawRequest) (*ApproveWithdrawResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApproveWithdrawTransaction not implemented")
}
func (UnimplementedUserSigningServiceServer) mustEmbedUnimplementedUserSigningServiceServer() {}
func (UnimplementedUserSigningServiceServer) testEmbeddedByValue()                            {}

// UnsafeUserSigningServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserSigningServiceServer will
// result in compilation errors.
type UnsafeUserSigningServiceServer interface {
	mustEmbedUnimplementedUserSigningServiceServer()
}

func RegisterUserSigningServiceServer(s grpc.ServiceRegistrar, srv UserSigningServiceServer) {
	// If the following call pancis, it indicates UnimplementedUserSigningServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&UserSigningService_ServiceDesc, srv)
}

func _UserSigningService_SignEvmTransaction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignUserTransactionEvmRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserSigningServiceServer).SignEvmTransaction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserSigningService_SignEvmTransaction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserSigningServiceServer).SignEvmTransaction(ctx, req.(*SignUserTransactionEvmRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserSigningService_SignTransaction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignUserTransactionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserSigningServiceServer).SignTransaction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserSigningService_SignTransaction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserSigningServiceServer).SignTransaction(ctx, req.(*SignUserTransactionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserSigningService_ApproveWithdrawTransaction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApproveWithdrawRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserSigningServiceServer).ApproveWithdrawTransaction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserSigningService_ApproveWithdrawTransaction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserSigningServiceServer).ApproveWithdrawTransaction(ctx, req.(*ApproveWithdrawRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserSigningService_ServiceDesc is the grpc.ServiceDesc for UserSigningService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserSigningService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "user.signing.v1.UserSigningService",
	HandlerType: (*UserSigningServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SignEvmTransaction",
			Handler:    _UserSigningService_SignEvmTransaction_Handler,
		},
		{
			MethodName: "SignTransaction",
			Handler:    _UserSigningService_SignTransaction_Handler,
		},
		{
			MethodName: "ApproveWithdrawTransaction",
			Handler:    _UserSigningService_ApproveWithdrawTransaction_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/user/v1/signing.proto",
}

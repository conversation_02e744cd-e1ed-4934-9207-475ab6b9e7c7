// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: proto/user/v1/signing.proto

package user_signing

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ChainType int32

const (
	ChainType_EVM     ChainType = 0
	ChainType_SOLANA  ChainType = 1
	ChainType_TRON    ChainType = 2
	ChainType_ARB     ChainType = 3
	ChainType_BSC     ChainType = 4
	ChainType_POLYGON ChainType = 5
)

// Enum value maps for ChainType.
var (
	ChainType_name = map[int32]string{
		0: "EVM",
		1: "SOLANA",
		2: "TRON",
		3: "ARB",
		4: "BSC",
		5: "POLYGON",
	}
	ChainType_value = map[string]int32{
		"EVM":     0,
		"SOLANA":  1,
		"TRON":    2,
		"ARB":     3,
		"BSC":     4,
		"POLYGON": 5,
	}
)

func (x ChainType) Enum() *ChainType {
	p := new(ChainType)
	*p = x
	return p
}

func (x ChainType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChainType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_user_v1_signing_proto_enumTypes[0].Descriptor()
}

func (ChainType) Type() protoreflect.EnumType {
	return &file_proto_user_v1_signing_proto_enumTypes[0]
}

func (x ChainType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChainType.Descriptor instead.
func (ChainType) EnumDescriptor() ([]byte, []int) {
	return file_proto_user_v1_signing_proto_rawDescGZIP(), []int{0}
}

type SignUserTransactionEvmRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	UserId               string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	To                   string                 `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
	Data                 string                 `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Value                *string                `protobuf:"bytes,4,opt,name=value,proto3,oneof" json:"value,omitempty"`
	Chain                ChainType              `protobuf:"varint,5,opt,name=chain,proto3,enum=user.signing.v1.ChainType" json:"chain,omitempty"`
	MaxFeePerGas         *string                `protobuf:"bytes,6,opt,name=maxFeePerGas,proto3,oneof" json:"maxFeePerGas,omitempty"`
	MaxPriorityFeePerGas *string                `protobuf:"bytes,7,opt,name=maxPriorityFeePerGas,proto3,oneof" json:"maxPriorityFeePerGas,omitempty"`
	GasLimit             *string                `protobuf:"bytes,8,opt,name=gasLimit,proto3,oneof" json:"gasLimit,omitempty"`
	GasPrice             *string                `protobuf:"bytes,9,opt,name=gasPrice,proto3,oneof" json:"gasPrice,omitempty"`
	From                 string                 `protobuf:"bytes,10,opt,name=from,proto3" json:"from,omitempty"`
	Nonce                string                 `protobuf:"bytes,11,opt,name=nonce,proto3" json:"nonce,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *SignUserTransactionEvmRequest) Reset() {
	*x = SignUserTransactionEvmRequest{}
	mi := &file_proto_user_v1_signing_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignUserTransactionEvmRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignUserTransactionEvmRequest) ProtoMessage() {}

func (x *SignUserTransactionEvmRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_v1_signing_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignUserTransactionEvmRequest.ProtoReflect.Descriptor instead.
func (*SignUserTransactionEvmRequest) Descriptor() ([]byte, []int) {
	return file_proto_user_v1_signing_proto_rawDescGZIP(), []int{0}
}

func (x *SignUserTransactionEvmRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SignUserTransactionEvmRequest) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *SignUserTransactionEvmRequest) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *SignUserTransactionEvmRequest) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

func (x *SignUserTransactionEvmRequest) GetChain() ChainType {
	if x != nil {
		return x.Chain
	}
	return ChainType_EVM
}

func (x *SignUserTransactionEvmRequest) GetMaxFeePerGas() string {
	if x != nil && x.MaxFeePerGas != nil {
		return *x.MaxFeePerGas
	}
	return ""
}

func (x *SignUserTransactionEvmRequest) GetMaxPriorityFeePerGas() string {
	if x != nil && x.MaxPriorityFeePerGas != nil {
		return *x.MaxPriorityFeePerGas
	}
	return ""
}

func (x *SignUserTransactionEvmRequest) GetGasLimit() string {
	if x != nil && x.GasLimit != nil {
		return *x.GasLimit
	}
	return ""
}

func (x *SignUserTransactionEvmRequest) GetGasPrice() string {
	if x != nil && x.GasPrice != nil {
		return *x.GasPrice
	}
	return ""
}

func (x *SignUserTransactionEvmRequest) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *SignUserTransactionEvmRequest) GetNonce() string {
	if x != nil {
		return x.Nonce
	}
	return ""
}

type SignUserTransactionEvmResponse struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	UserId               string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	To                   string                 `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
	Data                 string                 `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Value                *string                `protobuf:"bytes,4,opt,name=value,proto3,oneof" json:"value,omitempty"`
	Chain                ChainType              `protobuf:"varint,5,opt,name=chain,proto3,enum=user.signing.v1.ChainType" json:"chain,omitempty"`
	MaxFeePerGas         *string                `protobuf:"bytes,6,opt,name=maxFeePerGas,proto3,oneof" json:"maxFeePerGas,omitempty"`
	MaxPriorityFeePerGas *string                `protobuf:"bytes,7,opt,name=maxPriorityFeePerGas,proto3,oneof" json:"maxPriorityFeePerGas,omitempty"`
	GasLimit             *string                `protobuf:"bytes,8,opt,name=gasLimit,proto3,oneof" json:"gasLimit,omitempty"`
	GasPrice             *string                `protobuf:"bytes,9,opt,name=gasPrice,proto3,oneof" json:"gasPrice,omitempty"`
	From                 string                 `protobuf:"bytes,10,opt,name=from,proto3" json:"from,omitempty"`
	SignedTransaction    string                 `protobuf:"bytes,11,opt,name=signed_transaction,json=signedTransaction,proto3" json:"signed_transaction,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *SignUserTransactionEvmResponse) Reset() {
	*x = SignUserTransactionEvmResponse{}
	mi := &file_proto_user_v1_signing_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignUserTransactionEvmResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignUserTransactionEvmResponse) ProtoMessage() {}

func (x *SignUserTransactionEvmResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_v1_signing_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignUserTransactionEvmResponse.ProtoReflect.Descriptor instead.
func (*SignUserTransactionEvmResponse) Descriptor() ([]byte, []int) {
	return file_proto_user_v1_signing_proto_rawDescGZIP(), []int{1}
}

func (x *SignUserTransactionEvmResponse) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SignUserTransactionEvmResponse) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *SignUserTransactionEvmResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *SignUserTransactionEvmResponse) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

func (x *SignUserTransactionEvmResponse) GetChain() ChainType {
	if x != nil {
		return x.Chain
	}
	return ChainType_EVM
}

func (x *SignUserTransactionEvmResponse) GetMaxFeePerGas() string {
	if x != nil && x.MaxFeePerGas != nil {
		return *x.MaxFeePerGas
	}
	return ""
}

func (x *SignUserTransactionEvmResponse) GetMaxPriorityFeePerGas() string {
	if x != nil && x.MaxPriorityFeePerGas != nil {
		return *x.MaxPriorityFeePerGas
	}
	return ""
}

func (x *SignUserTransactionEvmResponse) GetGasLimit() string {
	if x != nil && x.GasLimit != nil {
		return *x.GasLimit
	}
	return ""
}

func (x *SignUserTransactionEvmResponse) GetGasPrice() string {
	if x != nil && x.GasPrice != nil {
		return *x.GasPrice
	}
	return ""
}

func (x *SignUserTransactionEvmResponse) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *SignUserTransactionEvmResponse) GetSignedTransaction() string {
	if x != nil {
		return x.SignedTransaction
	}
	return ""
}

type SignUserTransactionRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	UserId              string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	UnsignedTransaction string                 `protobuf:"bytes,2,opt,name=unsigned_transaction,json=unsignedTransaction,proto3" json:"unsigned_transaction,omitempty"`
	Chain               ChainType              `protobuf:"varint,3,opt,name=chain,proto3,enum=user.signing.v1.ChainType" json:"chain,omitempty"`
	Address             string                 `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *SignUserTransactionRequest) Reset() {
	*x = SignUserTransactionRequest{}
	mi := &file_proto_user_v1_signing_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignUserTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignUserTransactionRequest) ProtoMessage() {}

func (x *SignUserTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_v1_signing_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignUserTransactionRequest.ProtoReflect.Descriptor instead.
func (*SignUserTransactionRequest) Descriptor() ([]byte, []int) {
	return file_proto_user_v1_signing_proto_rawDescGZIP(), []int{2}
}

func (x *SignUserTransactionRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SignUserTransactionRequest) GetUnsignedTransaction() string {
	if x != nil {
		return x.UnsignedTransaction
	}
	return ""
}

func (x *SignUserTransactionRequest) GetChain() ChainType {
	if x != nil {
		return x.Chain
	}
	return ChainType_EVM
}

func (x *SignUserTransactionRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type SignUserTransactionResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	SignedTransaction string                 `protobuf:"bytes,1,opt,name=signed_transaction,json=signedTransaction,proto3" json:"signed_transaction,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *SignUserTransactionResponse) Reset() {
	*x = SignUserTransactionResponse{}
	mi := &file_proto_user_v1_signing_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignUserTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignUserTransactionResponse) ProtoMessage() {}

func (x *SignUserTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_v1_signing_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignUserTransactionResponse.ProtoReflect.Descriptor instead.
func (*SignUserTransactionResponse) Descriptor() ([]byte, []int) {
	return file_proto_user_v1_signing_proto_rawDescGZIP(), []int{3}
}

func (x *SignUserTransactionResponse) GetSignedTransaction() string {
	if x != nil {
		return x.SignedTransaction
	}
	return ""
}

type ApproveWithdrawRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	UserId         string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Chain          ChainType              `protobuf:"varint,2,opt,name=chain,proto3,enum=user.signing.v1.ChainType" json:"chain,omitempty"`
	Address        string                 `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	ReceiveAddress string                 `protobuf:"bytes,4,opt,name=receive_address,json=receiveAddress,proto3" json:"receive_address,omitempty"`
	Amount         string                 `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	ActivityId     string                 `protobuf:"bytes,6,opt,name=activityId,proto3" json:"activityId,omitempty"`
	Token          string                 `protobuf:"bytes,7,opt,name=token,proto3" json:"token,omitempty"`
	Decimals       int32                  `protobuf:"varint,8,opt,name=decimals,proto3" json:"decimals,omitempty"`
	Signature      *string                `protobuf:"bytes,9,opt,name=signature,proto3,oneof" json:"signature,omitempty"`
	Message        *string                `protobuf:"bytes,10,opt,name=message,proto3,oneof" json:"message,omitempty"`
	IsOkxWallet    *bool                  `protobuf:"varint,11,opt,name=isOkxWallet,proto3,oneof" json:"isOkxWallet,omitempty"`
	OidcToken      *string                `protobuf:"bytes,12,opt,name=oidcToken,proto3,oneof" json:"oidcToken,omitempty"`
	OtpId          *string                `protobuf:"bytes,13,opt,name=otpId,proto3,oneof" json:"otpId,omitempty"`
	OtpCode        *string                `protobuf:"bytes,14,opt,name=otpCode,proto3,oneof" json:"otpCode,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ApproveWithdrawRequest) Reset() {
	*x = ApproveWithdrawRequest{}
	mi := &file_proto_user_v1_signing_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApproveWithdrawRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApproveWithdrawRequest) ProtoMessage() {}

func (x *ApproveWithdrawRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_v1_signing_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApproveWithdrawRequest.ProtoReflect.Descriptor instead.
func (*ApproveWithdrawRequest) Descriptor() ([]byte, []int) {
	return file_proto_user_v1_signing_proto_rawDescGZIP(), []int{4}
}

func (x *ApproveWithdrawRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ApproveWithdrawRequest) GetChain() ChainType {
	if x != nil {
		return x.Chain
	}
	return ChainType_EVM
}

func (x *ApproveWithdrawRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *ApproveWithdrawRequest) GetReceiveAddress() string {
	if x != nil {
		return x.ReceiveAddress
	}
	return ""
}

func (x *ApproveWithdrawRequest) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *ApproveWithdrawRequest) GetActivityId() string {
	if x != nil {
		return x.ActivityId
	}
	return ""
}

func (x *ApproveWithdrawRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *ApproveWithdrawRequest) GetDecimals() int32 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *ApproveWithdrawRequest) GetSignature() string {
	if x != nil && x.Signature != nil {
		return *x.Signature
	}
	return ""
}

func (x *ApproveWithdrawRequest) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *ApproveWithdrawRequest) GetIsOkxWallet() bool {
	if x != nil && x.IsOkxWallet != nil {
		return *x.IsOkxWallet
	}
	return false
}

func (x *ApproveWithdrawRequest) GetOidcToken() string {
	if x != nil && x.OidcToken != nil {
		return *x.OidcToken
	}
	return ""
}

func (x *ApproveWithdrawRequest) GetOtpId() string {
	if x != nil && x.OtpId != nil {
		return *x.OtpId
	}
	return ""
}

func (x *ApproveWithdrawRequest) GetOtpCode() string {
	if x != nil && x.OtpCode != nil {
		return *x.OtpCode
	}
	return ""
}

type ApproveWithdrawResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	SignedTransaction string                 `protobuf:"bytes,1,opt,name=signed_transaction,json=signedTransaction,proto3" json:"signed_transaction,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ApproveWithdrawResponse) Reset() {
	*x = ApproveWithdrawResponse{}
	mi := &file_proto_user_v1_signing_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApproveWithdrawResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApproveWithdrawResponse) ProtoMessage() {}

func (x *ApproveWithdrawResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_v1_signing_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApproveWithdrawResponse.ProtoReflect.Descriptor instead.
func (*ApproveWithdrawResponse) Descriptor() ([]byte, []int) {
	return file_proto_user_v1_signing_proto_rawDescGZIP(), []int{5}
}

func (x *ApproveWithdrawResponse) GetSignedTransaction() string {
	if x != nil {
		return x.SignedTransaction
	}
	return ""
}

var File_proto_user_v1_signing_proto protoreflect.FileDescriptor

const file_proto_user_v1_signing_proto_rawDesc = "" +
	"\n" +
	"\x1bproto/user/v1/signing.proto\x12\x0fuser.signing.v1\"\xc5\x03\n" +
	"\x1dSignUserTransactionEvmRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x0e\n" +
	"\x02to\x18\x02 \x01(\tR\x02to\x12\x12\n" +
	"\x04data\x18\x03 \x01(\tR\x04data\x12\x19\n" +
	"\x05value\x18\x04 \x01(\tH\x00R\x05value\x88\x01\x01\x120\n" +
	"\x05chain\x18\x05 \x01(\x0e2\x1a.user.signing.v1.ChainTypeR\x05chain\x12'\n" +
	"\fmaxFeePerGas\x18\x06 \x01(\tH\x01R\fmaxFeePerGas\x88\x01\x01\x127\n" +
	"\x14maxPriorityFeePerGas\x18\a \x01(\tH\x02R\x14maxPriorityFeePerGas\x88\x01\x01\x12\x1f\n" +
	"\bgasLimit\x18\b \x01(\tH\x03R\bgasLimit\x88\x01\x01\x12\x1f\n" +
	"\bgasPrice\x18\t \x01(\tH\x04R\bgasPrice\x88\x01\x01\x12\x12\n" +
	"\x04from\x18\n" +
	" \x01(\tR\x04from\x12\x14\n" +
	"\x05nonce\x18\v \x01(\tR\x05nonceB\b\n" +
	"\x06_valueB\x0f\n" +
	"\r_maxFeePerGasB\x17\n" +
	"\x15_maxPriorityFeePerGasB\v\n" +
	"\t_gasLimitB\v\n" +
	"\t_gasPrice\"\xdf\x03\n" +
	"\x1eSignUserTransactionEvmResponse\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x0e\n" +
	"\x02to\x18\x02 \x01(\tR\x02to\x12\x12\n" +
	"\x04data\x18\x03 \x01(\tR\x04data\x12\x19\n" +
	"\x05value\x18\x04 \x01(\tH\x00R\x05value\x88\x01\x01\x120\n" +
	"\x05chain\x18\x05 \x01(\x0e2\x1a.user.signing.v1.ChainTypeR\x05chain\x12'\n" +
	"\fmaxFeePerGas\x18\x06 \x01(\tH\x01R\fmaxFeePerGas\x88\x01\x01\x127\n" +
	"\x14maxPriorityFeePerGas\x18\a \x01(\tH\x02R\x14maxPriorityFeePerGas\x88\x01\x01\x12\x1f\n" +
	"\bgasLimit\x18\b \x01(\tH\x03R\bgasLimit\x88\x01\x01\x12\x1f\n" +
	"\bgasPrice\x18\t \x01(\tH\x04R\bgasPrice\x88\x01\x01\x12\x12\n" +
	"\x04from\x18\n" +
	" \x01(\tR\x04from\x12-\n" +
	"\x12signed_transaction\x18\v \x01(\tR\x11signedTransactionB\b\n" +
	"\x06_valueB\x0f\n" +
	"\r_maxFeePerGasB\x17\n" +
	"\x15_maxPriorityFeePerGasB\v\n" +
	"\t_gasLimitB\v\n" +
	"\t_gasPrice\"\xb4\x01\n" +
	"\x1aSignUserTransactionRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x121\n" +
	"\x14unsigned_transaction\x18\x02 \x01(\tR\x13unsignedTransaction\x120\n" +
	"\x05chain\x18\x03 \x01(\x0e2\x1a.user.signing.v1.ChainTypeR\x05chain\x12\x18\n" +
	"\aaddress\x18\x04 \x01(\tR\aaddress\"L\n" +
	"\x1bSignUserTransactionResponse\x12-\n" +
	"\x12signed_transaction\x18\x01 \x01(\tR\x11signedTransaction\"\xa4\x04\n" +
	"\x16ApproveWithdrawRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x120\n" +
	"\x05chain\x18\x02 \x01(\x0e2\x1a.user.signing.v1.ChainTypeR\x05chain\x12\x18\n" +
	"\aaddress\x18\x03 \x01(\tR\aaddress\x12'\n" +
	"\x0freceive_address\x18\x04 \x01(\tR\x0ereceiveAddress\x12\x16\n" +
	"\x06amount\x18\x05 \x01(\tR\x06amount\x12\x1e\n" +
	"\n" +
	"activityId\x18\x06 \x01(\tR\n" +
	"activityId\x12\x14\n" +
	"\x05token\x18\a \x01(\tR\x05token\x12\x1a\n" +
	"\bdecimals\x18\b \x01(\x05R\bdecimals\x12!\n" +
	"\tsignature\x18\t \x01(\tH\x00R\tsignature\x88\x01\x01\x12\x1d\n" +
	"\amessage\x18\n" +
	" \x01(\tH\x01R\amessage\x88\x01\x01\x12%\n" +
	"\visOkxWallet\x18\v \x01(\bH\x02R\visOkxWallet\x88\x01\x01\x12!\n" +
	"\toidcToken\x18\f \x01(\tH\x03R\toidcToken\x88\x01\x01\x12\x19\n" +
	"\x05otpId\x18\r \x01(\tH\x04R\x05otpId\x88\x01\x01\x12\x1d\n" +
	"\aotpCode\x18\x0e \x01(\tH\x05R\aotpCode\x88\x01\x01B\f\n" +
	"\n" +
	"_signatureB\n" +
	"\n" +
	"\b_messageB\x0e\n" +
	"\f_isOkxWalletB\f\n" +
	"\n" +
	"_oidcTokenB\b\n" +
	"\x06_otpIdB\n" +
	"\n" +
	"\b_otpCode\"H\n" +
	"\x17ApproveWithdrawResponse\x12-\n" +
	"\x12signed_transaction\x18\x01 \x01(\tR\x11signedTransaction*I\n" +
	"\tChainType\x12\a\n" +
	"\x03EVM\x10\x00\x12\n" +
	"\n" +
	"\x06SOLANA\x10\x01\x12\b\n" +
	"\x04TRON\x10\x02\x12\a\n" +
	"\x03ARB\x10\x03\x12\a\n" +
	"\x03BSC\x10\x04\x12\v\n" +
	"\aPOLYGON\x10\x052\xf0\x02\n" +
	"\x12UserSigningService\x12w\n" +
	"\x12SignEvmTransaction\x12..user.signing.v1.SignUserTransactionEvmRequest\x1a/.user.signing.v1.SignUserTransactionEvmResponse\"\x00\x12n\n" +
	"\x0fSignTransaction\x12+.user.signing.v1.SignUserTransactionRequest\x1a,.user.signing.v1.SignUserTransactionResponse\"\x00\x12q\n" +
	"\x1aApproveWithdrawTransaction\x12'.user.signing.v1.ApproveWithdrawRequest\x1a(.user.signing.v1.ApproveWithdrawResponse\"\x00B\x16Z\x14user/v1;user_signingb\x06proto3"

var (
	file_proto_user_v1_signing_proto_rawDescOnce sync.Once
	file_proto_user_v1_signing_proto_rawDescData []byte
)

func file_proto_user_v1_signing_proto_rawDescGZIP() []byte {
	file_proto_user_v1_signing_proto_rawDescOnce.Do(func() {
		file_proto_user_v1_signing_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_user_v1_signing_proto_rawDesc), len(file_proto_user_v1_signing_proto_rawDesc)))
	})
	return file_proto_user_v1_signing_proto_rawDescData
}

var file_proto_user_v1_signing_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_user_v1_signing_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_proto_user_v1_signing_proto_goTypes = []any{
	(ChainType)(0),                         // 0: user.signing.v1.ChainType
	(*SignUserTransactionEvmRequest)(nil),  // 1: user.signing.v1.SignUserTransactionEvmRequest
	(*SignUserTransactionEvmResponse)(nil), // 2: user.signing.v1.SignUserTransactionEvmResponse
	(*SignUserTransactionRequest)(nil),     // 3: user.signing.v1.SignUserTransactionRequest
	(*SignUserTransactionResponse)(nil),    // 4: user.signing.v1.SignUserTransactionResponse
	(*ApproveWithdrawRequest)(nil),         // 5: user.signing.v1.ApproveWithdrawRequest
	(*ApproveWithdrawResponse)(nil),        // 6: user.signing.v1.ApproveWithdrawResponse
}
var file_proto_user_v1_signing_proto_depIdxs = []int32{
	0, // 0: user.signing.v1.SignUserTransactionEvmRequest.chain:type_name -> user.signing.v1.ChainType
	0, // 1: user.signing.v1.SignUserTransactionEvmResponse.chain:type_name -> user.signing.v1.ChainType
	0, // 2: user.signing.v1.SignUserTransactionRequest.chain:type_name -> user.signing.v1.ChainType
	0, // 3: user.signing.v1.ApproveWithdrawRequest.chain:type_name -> user.signing.v1.ChainType
	1, // 4: user.signing.v1.UserSigningService.SignEvmTransaction:input_type -> user.signing.v1.SignUserTransactionEvmRequest
	3, // 5: user.signing.v1.UserSigningService.SignTransaction:input_type -> user.signing.v1.SignUserTransactionRequest
	5, // 6: user.signing.v1.UserSigningService.ApproveWithdrawTransaction:input_type -> user.signing.v1.ApproveWithdrawRequest
	2, // 7: user.signing.v1.UserSigningService.SignEvmTransaction:output_type -> user.signing.v1.SignUserTransactionEvmResponse
	4, // 8: user.signing.v1.UserSigningService.SignTransaction:output_type -> user.signing.v1.SignUserTransactionResponse
	6, // 9: user.signing.v1.UserSigningService.ApproveWithdrawTransaction:output_type -> user.signing.v1.ApproveWithdrawResponse
	7, // [7:10] is the sub-list for method output_type
	4, // [4:7] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_proto_user_v1_signing_proto_init() }
func file_proto_user_v1_signing_proto_init() {
	if File_proto_user_v1_signing_proto != nil {
		return
	}
	file_proto_user_v1_signing_proto_msgTypes[0].OneofWrappers = []any{}
	file_proto_user_v1_signing_proto_msgTypes[1].OneofWrappers = []any{}
	file_proto_user_v1_signing_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_user_v1_signing_proto_rawDesc), len(file_proto_user_v1_signing_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_user_v1_signing_proto_goTypes,
		DependencyIndexes: file_proto_user_v1_signing_proto_depIdxs,
		EnumInfos:         file_proto_user_v1_signing_proto_enumTypes,
		MessageInfos:      file_proto_user_v1_signing_proto_msgTypes,
	}.Build()
	File_proto_user_v1_signing_proto = out.File
	file_proto_user_v1_signing_proto_goTypes = nil
	file_proto_user_v1_signing_proto_depIdxs = nil
}

Server = ./server
ServerName = xbit_server

# 定义变量
GOOS = linux   # 目标系统（可覆盖）
GOARCH = amd64 # 目标架构
BinDir = ./bin

build:
	mkdir -p $(BinDir)
	cd $(Server) && GOOS=$(GOOS) GOARCH=$(GOARCH) go build -o ../$(BinDir)/$(ServerName) main.go
	@echo "Server built for $(GOOS)/$(GOARCH) in $(BinDir)"

db-diff:
	# atlas migrate diff [flags] [name]
	atlas migrate diff --env gorm

db-rehash:
	atlas migrate hash

db-apply:
	atlas migrate apply --url "postgres://dev:dev@localhost:5432/dex_v2?sslmode=disable" --dir file://migrations

gqlgen:
	go run github.com/99designs/gqlgen generate --config gqlgen.yml
package app_error

import "fmt"

type AppError struct {
	Code    string
	Message string
	Data    map[string]any
}

type E *AppError

func (e *AppError) Error() string {
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

func New(code, message string) *AppError {
	return NewWithMeta(code, message, nil)
}
func NewWithMeta(code, message string, data map[string]any) *AppError {
	return &AppError{
		Message: message,
		Code:    code,
		Data:    data,
	}
}

func From(code string, e error) *AppError {
	return FromWithMeta(code, e, nil)
}
func FromWithMeta(code string, e error, data map[string]any) *AppError {
	return &AppError{
		Message: e.Error(),
		Code:    code,
		Data:    data,
	}
}

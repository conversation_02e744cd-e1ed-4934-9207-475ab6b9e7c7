package utils

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

type ResponseOK struct {
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

func Ok(c *gin.Context, data any) {
	c.JSON(http.StatusOK, ResponseOK{
		Code:    "Success",
		Message: "Success",
		Data:    data,
	})
}

func AbortWithAPIError(ctx *gin.Context, err *APIError) {
	ctx.Error(err).SetType(gin.ErrorTypePublic)
	ctx.Abort()
}

type APIError struct {
	Status    int
	ErrorCode string
	Message   string
}

func (e APIError) Error() string {
	return e.Message
}

// TODO: gql_error and APIError must be use same error source from AppError
var (
	ErrSessionTokenInvalid = &APIError{
		Status:    http.StatusBadRequest,
		ErrorCode: "ErrSessionTokenInvalid",
		Message:   "Session token invalid",
	}

	ErrHmacTokenInvalid = &APIError{
		Status:    http.StatusUnauthorized,
		ErrorCode: "ErrHmacTokenInvalid",
		Message:   "Hmac token invalid",
	}

	ErrApikeyInvalid = &APIError{
		Status:    http.StatusUnauthorized,
		ErrorCode: "ErrApikeyInvalid",
		Message:   "Api key invalid",
	}

	ErrPlayerNotFound = &APIError{
		Status:    http.StatusBadRequest,
		ErrorCode: "ErrPlayerNotFound",
		Message:   "Player not found",
	}

	ErrNotEnoughBalance = &APIError{
		Status:    http.StatusBadRequest,
		ErrorCode: "ErrNotEnoughBalance",
		Message:   "Not enough balance",
	}

	ErrInternalApiKeyInvalid = &APIError{
		Status:    http.StatusUnauthorized,
		ErrorCode: "ErrInternalApiKeyInvalid",
		Message:   "Internal api key invalid",
	}

	ErrAccessTokenInvalid = &APIError{
		Status:    http.StatusUnauthorized,
		ErrorCode: "ErrAccessTokenInvalid",
		Message:   "Access token invalid",
	}

	ErrSessionInvalid = &APIError{
		Status:    http.StatusUnauthorized,
		ErrorCode: "ErrSessionInvalid",
		Message:   "Session invalid",
	}

	ErrPlayerInactive = &APIError{
		Status:    http.StatusUnauthorized,
		ErrorCode: "ErrPlayerInactive",
		Message:   "Player inactive",
	}

	ErrCurrencyInvalid = &APIError{
		Status:    http.StatusBadRequest,
		ErrorCode: "ErrCurrencyInvalid",
		Message:   "Currency invalid",
	}

	ErrSystemMaintenance = &APIError{
		Status:    http.StatusServiceUnavailable,
		ErrorCode: "ErrSystemMaintenance",
		Message:   "System maintenance",
	}
)

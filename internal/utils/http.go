package utils

import (
	"io"
	"net/http"
	"net/url"
	"time"
)

// HttpClient struct to hold request details, headers, and query params
type HttpClient struct {
	url     string
	method  string
	reqBody io.Reader
	headers map[string]string
	params  url.Values
}

// NewHttpClient creates a new HttpClient instance with default values
func NewHttpClient(reqUrl, method string) *HttpClient {
	return &HttpClient{
		url:     reqUrl,
		method:  method,
		headers: map[string]string{}, // Initialize an empty header map
		params:  url.Values{},        // Initialize an empty param map
	}
}

// SetBody sets the request body
func (client *HttpClient) SetBody(body io.Reader) *HttpClient {
	client.reqBody = body
	return client
}

// SetHeader adds a custom header to the request
func (client *HttpClient) SetHeader(key, value string) *HttpClient {
	client.headers[key] = value
	return client
}

// SetParam adds a query parameter to the request URL
func (client *HttpClient) SetParam(key, value string) *HttpClient {
	client.params.Add(key, value)
	return client
}

// SetDefaultHeaders adds default headers if they are not already set
func (client *HttpClient) SetDefaultHeaders() *HttpClient {
	// Define default headers
	defaultHeaders := map[string]string{
		"accept":       "application/json",
		"Content-Type": "application/json",
	}

	// Add default headers if not present
	for key, value := range defaultHeaders {
		if _, exists := client.headers[key]; !exists {
			client.headers[key] = value
		}
	}
	return client
}

// Send sends the HTTP request with headers and params and returns the response body
func (client *HttpClient) Send() (body []byte, err error) {
	client.SetDefaultHeaders()
	// Add query parameters to the URL
	fullUrl, err := url.Parse(client.url)
	if err != nil {
		return nil, err
	}
	fullUrl.RawQuery = client.params.Encode()
	// Create the request
	req, err := http.NewRequest(client.method, fullUrl.String(), client.reqBody)
	if err != nil {
		return nil, err
	}

	// Add headers to the request
	for key, value := range client.headers {
		req.Header.Add(key, value)
	}

	// Set HTTP client with timeout
	httpClient := &http.Client{
		Timeout: 20 * time.Second,
	}

	// Send the request
	res, err := httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	// Read the response body
	body, err = io.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}

	return body, nil
}

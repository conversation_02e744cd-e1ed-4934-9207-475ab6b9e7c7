package utils

import (
	"encoding/json"
	"github.com/jinzhu/copier"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"strings"
)

func Translate[T any](from any) *T {
	var t T
	err := copier.Copy(&t, from)

	if err != nil {
		global.GVA_LOG.Error(err.Error())
	}
	return &t
}

func TranslateByJSON[T any](from any) *T {
	var t T

	b, err := json.Marshal(from)
	if err != nil {
		global.GVA_LOG.Error("Marshal error: " + err.Error())
		return nil
	}

	err = json.Unmarshal(b, &t)
	if err != nil {
		global.GVA_LOG.Error("Unmarshal error: " + err.Error())
		return nil
	}

	return &t
}

func JsonDecode[T any](data interface{}, v *T) error {
	marshalData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	err = json.Unmarshal(marshalData, v)
	if err != nil {
		return err
	}

	return nil
}

func ParseStringArray(arrayString string) []string {
	if arrayString == "[]" {
		return []string{} // Return an empty slice for "[]"
	}

	arrayString = strings.Trim(arrayString, "[]")
	elements := strings.Split(arrayString, ",")
	result := make([]string, 0, len(elements)) // Pre-allocate for efficiency

	for _, element := range elements {
		trimmedElement := strings.TrimSpace(element)
		// Remove quotes, if any
		trimmedElement = strings.ReplaceAll(trimmedElement, "'", "")
		trimmedElement = strings.ReplaceAll(trimmedElement, "\"", "")
		if trimmedElement != "" { // added check to prevent empty strings from being added
			result = append(result, trimmedElement)
		}
	}
	return result
}

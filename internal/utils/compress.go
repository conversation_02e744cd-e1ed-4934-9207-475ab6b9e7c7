package utils

import (
	"bytes"
	"github.com/andybalholm/brotli"
	"io"
)

func Compress(value []byte) ([]byte, error) {
	var buf bytes.Buffer
	gz := brotli.NewWriterLevel(&buf, 2)
	if _, err := gz.Write(value); err != nil {
		return nil, err
	}
	if err := gz.Close(); err != nil {
		return nil, err
	}
	compressed := buf.Bytes()
	return compressed, nil
}

func Decompress(data []byte) ([]byte, error) {
	var buf bytes.Buffer
	buf.Write(data)
	finalResult := brotli.NewReader(&buf)
	decompressedData, err := io.ReadAll(finalResult)
	if err != nil {
		return nil, err
	}
	return decompressedData, nil
}

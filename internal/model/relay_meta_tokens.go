package model

type RelayMetaTokens struct {
	BaseModel
	ChainIdHex string `gorm:"chain_id_hex;uniqueIndex:idx_chain_addr_token"`
	TokenId    string `gorm:"token_id;uniqueIndex:idx_chain_addr_token"` // relay token id
	Address    string `gorm:"address;uniqueIndex:idx_chain_addr_token"`
	ChainId    string `gorm:"chain_id;index"`
	Symbol     string `gorm:"symbol; index"`
	IsNative   bool   `gorm:"is_native"` // is native token of the chain

	Name             string `gorm:"name"`
	Decimals         int    `gorm:"decimals"`
	SupportsBridging bool   `gorm:"supports_bridging"`
	WithdrawalFee    int    `gorm:"withdrawal_fee"`
	DepositFee       int    `gorm:"deposit_fee"`
	SurgeEnabled     bool   `gorm:"surge_enabled"`
	SupportsPermit   bool   `gorm:"supports_permit,omitempty"`
	UsdPrice         string `gorm:"usd_price"` // request from relay api
	LogoURI          string `gorm:"logo_uri"`  // request from relay api
	IsActive         bool   `gorm:"is_active"`
}

func (t *RelayMetaTokens) Table() string {
	return "relay_meta_tokens"
}

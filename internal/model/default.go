/*
 * Copyright © 2023 Xbit Protocol
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package model

import (
	"errors"
	"reflect"

	"gorm.io/gorm"
)

type (
	DefaultModel interface {
		CreateTable(model interface{}) error
		DeleteTable(model interface{}) error
		DropTable() error
		Create(entity interface{}) error
		CreateBatch(entities interface{}) error
	}

	DefaultModelImpl struct {
		Table string
		DB    *gorm.DB
	}
)

func NewDefaultModel(table string, db *gorm.DB) DefaultModel {
	return &DefaultModelImpl{
		Table: table,
		DB:    db,
	}
}

func (m *DefaultModelImpl) CreateTable(model interface{}) error {
	return m.DB.AutoMigrate(model)
}

func (m *DefaultModelImpl) DeleteTable(model interface{}) error {
	return m.DB.Session(&gorm.Session{AllowGlobalUpdate: true}).Unscoped().Delete(&model).Error
}

func (m *DefaultModelImpl) DropTable() error {
	return m.DB.Migrator().DropTable(m.Table)
}

func (m *DefaultModelImpl) Create(entity interface{}) error {
	dbTx := m.DB.Create(entity)
	if dbTx.Error != nil {
		return dbTx.Error
	}
	return nil
}

func (m *DefaultModelImpl) CreateBatch(entities interface{}) error {
	if reflect.TypeOf(entities).Kind() != reflect.Slice {
		return errors.New("not array input")
	}
	dbTx := m.DB.CreateInBatches(entities, reflect.ValueOf(entities).Len())
	if dbTx.Error != nil {
		return dbTx.Error
	}
	return nil
}

func HandleDBTxError(dbTx *gorm.DB) error {
	if dbTx.Error != nil {
		return dbTx.Error
	}
	if dbTx.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}

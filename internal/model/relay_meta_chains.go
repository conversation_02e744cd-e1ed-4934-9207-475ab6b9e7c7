package model

type RelayMetaChains struct {
	BaseModel
	ChainIdHex             string  `gorm:"chain_id_hex; uniqueIndex"`
	ChainId                string  `gorm:"chain_id;index"`
	Name                   string  `gorm:"name; index"`
	DisplayName            string  `gorm:"display_name; index"`
	HttpRpcUrl             string  `gorm:"http_rpc_url"`
	WsRpcUrl               string  `gorm:"ws_rpc_url"`
	ExplorerUrl            string  `gorm:"explorer_url"`
	ExplorerName           string  `gorm:"explorer_name"`
	DepositEnabled         bool    `gorm:"deposit_enabled"`
	TokenSupport           string  `gorm:"token_support"`
	Disabled               bool    `gorm:"disabled"`
	PartialDisableLimit    int     `gorm:"partial_disable_limit"`
	BlockProductionLagging bool    `gorm:"block_production_lagging"`
	WithdrawalFee          int     `gorm:"withdrawal_fee"`
	DepositFee             int     `gorm:"deposit_fee"`
	SurgeEnabled           bool    `gorm:"surge_enabled"`
	IconUrl                string  `gorm:"icon_url"`
	Contracts              []byte  `gorm:"contracts"`
	VmType                 string  `gorm:"vm_type"`
	BaseChainId            int     `gorm:"base_chain_id"`
	SolverAddresses        string  `gorm:"solver_addresses"`
	Tags                   string  `gorm:"tags"`
	LogoUrl                string  `gorm:"logo_url"`
	BrandColor             string  `gorm:"brand_color"`
	ExplorerPaths          *string `gorm:"explorer_paths"`
	IsActive               bool    `gorm:"is_active"`
}

func (RelayMetaChains) TableName() string {
	return "relay_meta_chains"
}

type ExplorerPaths struct {
	Transaction string `json:"transaction"`
}
type ChainCurrency struct {
	Id               string `json:"id"`
	Symbol           string `json:"symbol"`
	Name             string `json:"name"`
	Address          string `json:"address"`
	Decimals         int    `json:"decimals"`
	SupportsBridging bool   `json:"supports-bridging"`
}
type ChainContracts struct {
	Multicall3           string `json:"multicall3"`
	Multicaller          string `json:"multicaller"`
	OnlyOwnerMulticaller string `json:"only_owner_multicaller"`
	RelayReceiver        string `json:"relay_receiver"`
	Erc20Router          string `json:"erc20_router"`
	ApprovalProxy        string `json:"approval_proxy"`
}

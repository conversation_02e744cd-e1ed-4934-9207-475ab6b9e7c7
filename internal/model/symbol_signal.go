package model

import "time"

type SymbolSignal struct {
	ID                 int       `gorm:"primaryKey;column:id;type:int4;not null;"`
	Username           string    `gorm:"column:username;type:varchar;not null"`
	UUID               string    `gorm:"column:uuid;type:varchar;not null"`
	SignalType         string    `gorm:"column:signal_type;type:varchar;not null"`
	SignalName         string    `gorm:"column:signal_name;type:varchar;not null"`
	SignalPnl          float64   `gorm:"column:signal_pnl;type:float8;not null"`
	InitialCapital     float64   `gorm:"column:initial_capital;type:float8;not null"`
	LatestAssets       float64   `gorm:"column:latest_assets;type:float8;not null"`
	RunningStatus      string    `gorm:"column:running_status;type:varchar;not null"`
	ConsecutiveWins    int       `gorm:"column:consecutive_wins;type:int4;not null"`
	Subscribers        int       `gorm:"column:subscribers;type:int4;not null"`
	OperationDirection string    `gorm:"column:operation_direction;type:varchar;not null"`
	UnderlyingAsset    string    `gorm:"column:underlying_asset;type:varchar;not null"`
	MonthlyReturnRate  float64   `gorm:"column:monthly_return_rate;type:float8;not null"`
	MonthlyAlpha       float64   `gorm:"column:monthly_alpha;type:float8;not null"`
	AnnualWinRate      float64   `gorm:"column:annual_win_rate;type:float8;not null"`
	CumulativeIncome   float64   `gorm:"column:cumulative_income;type:float8;not null"`
	SharpeRatio        float64   `gorm:"column:sharpe_ratio;type:float8;not null"`
	ThreeYield         float64   `gorm:"column:three_yield;type:float8;not null"`
	SevenYield         float64   `gorm:"column:seven_yield;type:float8;not null"`
	MaxDrawdown7Days   float64   `gorm:"column:max_drawdown_7days;type:float8;not null"`
	RunningTime        float64   `gorm:"column:running_time;type:float8;not null"`
	HistoricalWinRate  float64   `gorm:"column:historical_win_rate;type:float8;not null"`
	ProfitLossCount    float64   `gorm:"column:profit_loss_count;type:float8;not null"`
	ProfitLossRatio    float64   `gorm:"column:profit_loss_ratio;type:float8;not null"`
	Confidence         string    `gorm:"column:confidence;type:varchar;not null"`
	EvaluationStatus   string    `gorm:"column:evaluation_status;type:varchar;not null"`
	Review             string    `gorm:"column:review;type:varchar;not null"`
	UserID             int       `gorm:"column:user_id;type:int4;not null"`
	CreatedDate        time.Time `gorm:"column:created_date;type:timestamptz"`
	UpdatedDate        time.Time `gorm:"column:updated_date;type:timestamptz"`
}

func (SymbolSignal) TableName() string {
	return "symbol_signal"
}

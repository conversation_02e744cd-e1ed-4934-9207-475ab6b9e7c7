package model

import (
	"time"
)

type UserBalance struct {
	WalletAddress  string    `gorm:"column:wallet_address;primary_key"`
	PrevDayBalance float64   `gorm:"column:prev_day_balance;default:0"`
	Balance        float64   `gorm:"column:balance"`
	RawUSD         float64   `gorm:"column:raw_usd"`
	UpdateAt       time.Time `gorm:"column:update_at"`
}

func (*UserBalance) TableName() string {
	return "user_balance"
}

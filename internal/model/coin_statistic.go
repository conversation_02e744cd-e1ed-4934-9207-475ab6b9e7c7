package model

import "time"

type CoinStatistic struct {
	Symbol            string    `gorm:"primaryKey;column:symbol"`
	Funding           float64   `gorm:"column:funding"`
	MarketCap         float64   `gorm:"column:market_cap"`
	TotalSupply       float64   `gorm:"column:total_supply"`
	CirculatingSupply float64   `gorm:"column:circulating_supply;default:0"`
	OpenInterest      float64   `gorm:"column:open_interest"`
	PrevDayPx         float64   `gorm:"column:prev_day_px"`
	DayNtlVlm         float64   `gorm:"column:day_ntl_vlm"`
	Premium           float64   `gorm:"column:premium"`
	OraclePx          float64   `gorm:"column:oracle_px"`
	MarkPx            float64   `gorm:"column:mark_px"`
	MidPx             float64   `gorm:"column:mid_px"`
	ImpactPxBid       float64   `gorm:"column:impact_px_bid"`
	ImpactPxAsk       float64   `gorm:"column:impact_px_ask"`
	DayBaseVlm        float64   `gorm:"column:day_base_vlm"`
	ChangePx          float64   `gorm:"column:change_px"`
	ChangePxPercent   float64   `gorm:"column:change_px_percent"`
	LastUpdated       time.Time `gorm:"column:last_updated;default:CURRENT_TIMESTAMP"`
}

// TableName sets the table name for the CoinStatistic model
func (CoinStatistic) TableName() string {
	return "coin_statistic"
}

package model

import (
	"fmt"
	"time"
)

type Coin struct {
	Symbol        string    `gorm:"primaryKey;column:symbol"`
	SzDecimals    int       `gorm:"column:sz_decimals"`
	MaxLeverage   int       `gorm:"column:max_leverage"`
	MarginTableID int       `gorm:"column:margin_table_id"`
	IsDelisted    bool      `gorm:"column:is_delisted"`
	OnlyIsolated  bool      `gorm:"column:only_isolated"`
	Address       string    `gorm:"column:address"`
	LastUpdated   time.Time `gorm:"column:last_updated;default:CURRENT_TIMESTAMP"`
	CreatedAt     time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP"`
}

// TableName sets the table name for the Coin model
func (Coin) TableName() string {
	return "coin"
}

func (c Coin) String() string {
	return fmt.Sprintf("Coin{Symbol: %s, Decimals: %d, MaxLeverage: %d, Delisted: %t}",
		c.<PERSON><PERSON>, c.<PERSON>, c<PERSON>, c<PERSON>)
}

package model

import (
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// ReferralSnapshot represents the referral_snapshots table
type ReferralSnapshot struct {
	UserID                 uuid.UUID       `gorm:"type:uuid;primary_key" json:"user_id"`
	DirectCount            int             `gorm:"default:0" json:"direct_count"`
	TotalDownlineCount     int             `gorm:"default:0" json:"total_downline_count"`
	TotalVolumeUSD         decimal.Decimal `gorm:"type:numeric(38,2);default:0" json:"total_volume_usd"`
	TotalRewardsDistributed decimal.Decimal `gorm:"type:numeric(38,6);default:0" json:"total_rewards_distributed"`

	// Relationships
	User User `gorm:"foreignKey:UserID;references:ID" json:"user"`
}

// TableName specifies the table name for ReferralSnapshot
func (ReferralSnapshot) TableName() string {
	return "referral_snapshots"
}

package model

import (
	"github.com/google/uuid"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	"time"
)

type UserNotificationSetting struct {
	ID             uuid.UUID `gorm:"primary_key;type:uuid"`
	UserID         uuid.UUID `gorm:"type:uuid;column:user_id;index:idx_user"`
	Symbol         string    `gorm:"type:text;column:symbol;index:idx_symbol"`
	Type           string    `gorm:"type:text;column:type"`
	Value          float64   `gorm:"type:float;column:value"`
	CreateAt       time.Time `gorm:"type:timestamp;column:create_at"`
	UpdateAt       time.Time `gorm:"type:timestamp;column:update_at"`
	IsActive       bool      `gorm:"type:bool;column:is_active;type:bool"`
	IsReminderOnce bool      `gorm:"type:bool;column:is_reminder_once;type:bool"`
	IsDeleted      bool      `gorm:"type:bool;column:is_deleted;type:bool;default:false"`
	LastNotifiedAt time.Time `gorm:"type:timestamp;column:last_notified_at"`
	Direction      string    `gorm:"type:text;column:direction"`
	Note           string    `gorm:"type:text;column:note"`
}

func (s *UserNotificationSetting) TableName() string {
	return "user_notification_settings"
}

var NotificationTypeMap = map[string]string{
	"priceRise":          utils.NotificationTypePrice,
	"priceFell":          utils.NotificationTypePrice,
	"percent24hIncrease": utils.NotificationTypePercent,
	"percent24hDecline":  utils.NotificationTypePercent,
}

var NotificationDirectionMap = map[string]string{
	"priceRise":          utils.NotificationDirectionUp,
	"priceFell":          utils.NotificationDirectionDown,
	"percent24hIncrease": utils.NotificationDirectionUp,
	"percent24hDecline":  utils.NotificationDirectionDown,
}

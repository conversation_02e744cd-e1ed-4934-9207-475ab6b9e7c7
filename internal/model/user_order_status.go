package model

import "time"

type UserOrderStatus struct {
	Oid       int64     `gorm:"index;column:oid"`
	CreatedAt time.Time `gorm:"column:created_at"`
	Time      string    `gorm:"time"`
	User      string    `gorm:"index;column:user"`
	Status    string    `gorm:"index;column:status"`
	UserId    *string   `gorm:"index;column:user_id"`

	Cloid            string `gorm:"index;column:cloid"`
	Coin             string `gorm:"index;column:coin"`
	Side             string `gorm:"side"`
	LimitPx          string `gorm:"limit_px"`
	Sz               string `gorm:"sz"`
	Timestamp        int64  `gorm:"timestamp"`
	TriggerCondition string `gorm:"trigger_condition"`
	IsTrigger        bool   `gorm:"is_trigger"`
	TriggerPx        string `gorm:"trigger_px"`
	Children         string `gorm:"children"`
	IsPositionTpsl   bool   `gorm:"is_position_tpsl"`
	ReduceOnly       bool   `gorm:"reduce_only"`
	OrderType        string `gorm:"order_type"`
	OrigSz           string `gorm:"orig_sz"`
	Tif              string `gorm:"tif"`
}

func (UserOrderStatus) TableName() string {
	return "user_order_status"
}

package model

type UserMiscEvent struct {
	Time                   string `gorm:"index;column:time"`
	Hash                   string `gorm:"index;column:hash"`
	Users                  string `gorm:"users"`
	CDepositUser           string `gorm:"index;column:c_deposit_user"`
	CDepositAmount         string `gorm:"c_deposit_amount"` // 充值金额
	CWithdrawalUser        string `gorm:"index;column:c_withdrawal_user"`
	CWithdrawalAmount      string `gorm:"c_withdrawal_amount"`       // 提现金额
	CWithdrawalIsFinalized bool   `gorm:"c_withdrawal_is_finalized"` // 是否已完成提现
	Type                   string `gorm:"index;column:type"`         // 变化类型（例如 deposit、withdraw 等）
	Usdc                   string `gorm:"usdc"`                      // 涉及 USDC 金额
	ToPerp                 bool   `gorm:"to_perp"`                   // 是否转入永续合约账户

	Token          string `gorm:"index;column:token"`       // 涉及的代币名称（例如 ETH, SOL 等）
	Amount         string `gorm:"amount"`                   // 代币数量
	UsdcValue      string `gorm:"usdc_value"`               // 该代币折算成 USDC 的价值
	User           string `gorm:"index;column:user"`        // 用户 address
	UserId         string `gorm:"index;column:user_id"`     // 用户 ID
	Destination    string `gorm:"index;column:destination"` // 转账目的地（可能是地址或账户名）
	Fee            string `gorm:"fee"`                      // 手续费（折算为 USDC）
	NativeTokenFee string `gorm:"native_token_fee"`         // 原生代币手续费（如 ETH 的 gas）
	Nonce          int64  `gorm:"index;column:nonce"`       // 唯一流水号或防重放随机数

}

func (UserMiscEvent) TableName() string {
	return "user_misc_events"
}

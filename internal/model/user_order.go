package model

import (
	"github.com/google/uuid"
	"time"
)

type Order struct {
	ID           uuid.UUID  `gorm:"primary_key;type:uuid;column:id"`
	CreatedAt    time.Time  `gorm:"column:created_at"`
	UpdateAt     time.Time  `gorm:"column:update_at"`
	UserID       uuid.UUID  `gorm:"column:user_id;type:uuid"`
	OID          uuid.UUID  `gorm:"column:oid;type:uuid"`
	Type         string     `gorm:"column:type;size:100"`
	Symbol       string     `gorm:"column:symbol"`
	Direction    string     `gorm:"column:direction"`
	Status       string     `gorm:"column:status"`
	Size         float64    `gorm:"column:size"`
	OriginSize   float64    `gorm:"column:origin_size"`
	OrderPx      float64    `gorm:"column:order_px"`
	TriggerPx    *float64   `gorm:"column:trigger_px;nullable"`
	ReduceOnly   bool       `gorm:"column:reduce_only"`
	VaultAddress *string    `gorm:"column:vault_address;nullable"`
	ExpiresAfter *time.Time `gorm:"column:expires_after;nullable"`
}

func (Order) TableName() string {
	return "order"
}

package model

import (
	"github.com/google/uuid"
	"time"
)

type UserWallet struct {
	ID              uuid.UUID `gorm:"primary_key;type:uuid"`
	UserID          uuid.UUID `gorm:"type:uuid;column:user_id;index:idx_user_wallet,unique"`
	WalletAddress   string    `gorm:"column:wallet_address;index:idx_user_wallet,unique"`
	CreatedAt       time.Time `gorm:"column:created_at"`
	WalletID        uuid.UUID `gorm:"column:wallet_id"`
	WalletAccountID uuid.UUID `gorm:"column:wallet_account_id"`
}

func (UserWallet) TableName() string {
	return "user_wallet"
}

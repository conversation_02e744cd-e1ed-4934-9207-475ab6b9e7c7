package model

type RangoMetaTokens struct {
	BaseModel
	Address    string `gorm:"address;uniqueIndex:idx_chain_addr_symbol"`
	<PERSON><PERSON>hain string `gorm:"block_chain;uniqueIndex:idx_chain_addr_symbol"`
	Symbol     string `gorm:"symbol;uniqueIndex:idx_chain_addr_symbol"`

	Name     string `gorm:"name;index"`
	IsNative bool   `gorm:"is_native;default:false"`

	Image             string  `gorm:"image"`
	UsdPrice          float64 `gorm:"usd_price"`
	Decimals          int64   `gorm:"decimals"`
	IsPopular         bool    `gorm:"is_popular"`
	IsSecondaryCoin   bool    `gorm:"is_secondary_coin"`
	CoinSource        string  `gorm:"coin_source"`
	CoinSourceUrl     string  `gorm:"coin_source_url"`
	SupportedSwappers string  `gorm:"supported_swappers"`
	IsActive          bool    `gorm:"is_active,default:false"`
}

func (RangoMetaTokens) TableName() string {
	return "rango_meta_tokens"
}

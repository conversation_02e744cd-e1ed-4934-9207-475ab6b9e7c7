package model

import (
	"github.com/google/uuid"
	"time"
)

type Position struct {
	ID                 uuid.UUID `gorm:"primary_key;type:uuid;column:id"`
	CreatedAt          time.Time `gorm:"column:created_at"`
	UpdateAt           time.Time `gorm:"column:update_at"`
	UserID             uuid.UUID `gorm:"column:user_id;type:uuid"`
	WalletAddress      string    `gorm:"column:wallet_address;uniqueIndex:idx_wallet_symbol"`
	Symbol             string    `gorm:"column:symbol;uniqueIndex:idx_wallet_symbol"`
	Type               string    `gorm:"column:type"`
	Side               int       `gorm:"column:side"` // long, short
	Size               float64   `gorm:"column:size"`
	EntryPx            float64   `gorm:"column:entry_px"`
	MarginMode         string    `gorm:"column:margin_mode"`
	FundingAllTime     float64   `gorm:"column:funding_all_time"`
	FundingSinceChange float64   `gorm:"column:funding_since_change"`
}

func (Position) TableName() string {
	return "position"
}

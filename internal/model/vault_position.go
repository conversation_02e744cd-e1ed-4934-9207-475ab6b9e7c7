package model

import (
	"time"
)

type VaultPosition struct {
	PositionID       int64     `gorm:"column:position_id;primaryKey;autoIncrement"`   // Primary key ID
	PairID           int64     `gorm:"column:pair_id"`                                // Trading pair ID
	StrategyID       int64     `gorm:"column:strategy_id"`                            // Strategy group ID
	Amount           float64   `gorm:"column:amount"`                                 // Position amount
	Description      string    `gorm:"column:description;size:100"`                   // Description
	EntryPrice       float64   `gorm:"column:entry_price"`                            // Entry price
	CurrentPrice     float64   `gorm:"column:current_price"`                          // Current price
	CollateralUsed   float64   `gorm:"column:collateral_used;default:0"`              // Used collateral
	UnrealizedPnlUSD float64   `gorm:"column:unrealized_pnl_usd"`                     // Unrealized PnL in USD
	RealizedPnlUSD   float64   `gorm:"column:realized_pnl_usd"`                       // Realized PnL in USD
	TpPrice          float64   `gorm:"column:tp_price"`                               // Take profit price
	SpPrice          float64   `gorm:"column:sp_price"`                               // Stop loss price
	Leverage         int32     `gorm:"column:leverage"`                               // Leverage ratio
	Direction        string    `gorm:"column:direction;size:255;not null;default:''"` // BUY/SELL
	Symbol           string    `gorm:"column:symbol;size:255"`                        // Trading pair name: ETH/USDT
	Sort             int32     `gorm:"column:sort;default:0"`                         // Sort order
	CreatedDate      time.Time `gorm:"column:created_date"`                           // Creation time
	CreatedBy        string    `gorm:"column:created_by;size:100"`                    // Creator
	UpdatedDate      time.Time `gorm:"column:updated_date;default:CURRENT_TIMESTAMP"` // Update time
	UpdatedBy        string    `gorm:"column:updated_by;size:100"`                    // Updater
	IsOpen           bool      `gorm:"column:is_open;default:false"`                  // Whether position is open
	UserID           int64     `gorm:"column:user_id"`                                // User ID
}

func (VaultPosition) TableName() string {
	return "vault_position"
}

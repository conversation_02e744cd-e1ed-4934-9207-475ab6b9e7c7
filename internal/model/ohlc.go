package model

import (
	"time"
)

// OHLC represents the klines table structure
type OHLC struct {
	TimeOpen  time.Time `gorm:"column:time_open;primaryKey"`
	TimeClose time.Time `gorm:"column:time_close"`
	Symbol    string    `gorm:"column:symbol;primaryKey"`
	Open      float64   `gorm:"column:open"`
	High      float64   `gorm:"column:high"`
	Low       float64   `gorm:"column:low"`
	Close     float64   `gorm:"column:close"`
	Volume    float64   `gorm:"column:total_volume"`
	NumTrades uint64    `gorm:"column:num_trades"`
}

// TableName specifies the table name for the Kline model
func (OHLC) TableName() string {
	return "ohlc"
}

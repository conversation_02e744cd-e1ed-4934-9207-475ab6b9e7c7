package model

import (
	"time"
)

type TradeOrder struct {
	OrderID         int64     `gorm:"column:order_id;primaryKey;autoIncrement"`
	PairID          int64     `gorm:"column:pair_id"`
	VaultID         int64     `gorm:"column:vault_id;default:0"`
	OrderType       string    `gorm:"column:order_type;size:4;not null"`        // Order type
	OrderActionType string    `gorm:"column:order_action_type;size:4;not null"` // Order action type
	OrderStatus     string    `gorm:"column:order_status;size:2;not null;default:'0'"`
	IsReduceOnly    string    `gorm:"column:is_reduce_only;size:4"`
	IsActive        bool      `gorm:"column:is_active;not null;default:false"`
	Price           float64   `gorm:"column:price;default:0"`
	Size            float64   `gorm:"column:size;default:0"`
	FilledPrice     float64   `gorm:"column:filled_price;type:numeric(20,8)"`
	Commission      float64   `gorm:"column:commission;type:numeric(20,8)"`
	Version         int64     `gorm:"column:version;not null;default:0"`
	AccountID       int64     `gorm:"column:account_id;not null;default:0"`
	UserID          int64     `gorm:"column:user_id"`
	FundTxID        int64     `gorm:"column:fund_tx_id"`
	TpPrice         float64   `gorm:"column:tp_price;type:numeric(20,8);not null;default:0"`
	SpPrice         float64   `gorm:"column:sp_price;type:numeric(20,8);not null;default:0"`
	CreatedBy       string    `gorm:"column:created_by;size:100"`
	CreatedDate     time.Time `gorm:"column:created_date"`
	UpdatedBy       string    `gorm:"column:updated_by;size:100"`
	UpdatedDate     time.Time `gorm:"column:updated_date;default:CURRENT_TIMESTAMP"`
	Symbol          string    `gorm:"column:symbol;size:20;not null"`
}

func (TradeOrder) TableName() string {
	return "trade_orders"
}

package model

import (
	"time"

	"github.com/google/uuid"
)

type UserCoin struct {
	UserID          uuid.UUID `gorm:"primaryKey;column:user_id;type:uuid"`
	Symbol          string    `gorm:"primaryKey;column:symbol;type:text"`
	IsFavorite      bool      `gorm:"column:is_favorite;type:bool" json:"is_favorite"`
	IsCross         bool      `gorm:"type:bool;default:true" json:"is_cross"`
	Leverage        *int      `gorm:"type:int;nullable" json:"leverage"`
	TPSLUnit        string    `gorm:"type:text;column:tpsl_unit;default:percentage" json:"tpsl_unit"`
	OrderUnitInBase bool      `gorm:"type:bool;column:order_unit_in_base;default:true" json:"order_unit_in_base"`
	CreatedAt       time.Time
	UpdatedAt       time.Time
}

func (UserCoin) TableName() string {
	return "user_coin"
}

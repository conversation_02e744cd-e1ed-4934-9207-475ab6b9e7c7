package model

type RangoSwaps struct {
	BaseModel
	RequestId              string `gorm:"request_id;index"`
	UserAddress            string `gorm:"user_address; index"`
	SwapperId              string `gorm:"swapper_id;index"`
	SwapperLogo            string `gorm:"swapper_logo"`
	SwapperType            string `gorm:"swapper_type; index"`
	FromAmount             string `gorm:"from_amount"`
	ToAmount               string `gorm:"to_amount"`
	Fee                    []byte `gorm:"fee"`
	EstimatedTimeInSeconds int64  `gorm:"estimated_time_in_seconds"`
	SwapChainType          string `gorm:"swap_chain_type; index"`
	MaxRequiredSign        int64  `gorm:"max_required_sign"`
	Step                   int64  `gorm:"step; index"`
	CallData               []byte `json:"call_data"`
	CallDataHash           string `json:"call_data_hash"`
	TxHash                 string `json:"tx_hash"`
}

// TableName specifies the table name for RangoHistory
func (RangoSwaps) TableName() string {
	return "rango_swaps"
}

type FeeMeta struct {
	Type     string `json:"type"`
	GasLimit string `json:"gasLimit"`
	GasPrice string `json:"gasPrice"`
}
type FeeAsset struct {
	Blockchain string `json:"blockchain"`
	Symbol     string `json:"symbol"`
	Address    string `json:"address"`
}
type Fee struct {
	RequestId   string   `json:"requestId"`
	Step        int64    `json:"step"`
	Asset       FeeAsset `json:"asset"`
	ExpenseType string   `json:"expenseType"`
	Amount      string   `json:"amount"`
	Name        string   `json:"name"`
	Meta        FeeMeta  `json:"meta"`
	Price       float64  `json:"price"`
}

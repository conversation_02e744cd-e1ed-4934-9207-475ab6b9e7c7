package model

type RangoMetaChains struct {
	BaseModel
	Name            string `gorm:"uniqueIndex" json:"name"`
	ChainId         string `gorm:"index" json:"chain_id"`
	DefaultDecimals int    `gorm:"column:default_decimals"`
	AddressPatterns string `gorm:"column:address_patterns " json:"address_patterns"`
	FeeAssets       []byte `gorm:"column:fee_assets"`
	Logo            string `gorm:"column:logo"`
	DisplayName     string `gorm:"column:display_name"`
	ShortName       string `gorm:"column:short_name"`
	Sort            int    `gorm:"column:sort"`
	Color           string `gorm:"column:color"`
	Enabled         bool   `gorm:"column:enabled"`
	Type            string `gorm:"column:type"`
	Info            []byte `gorm:"column:info"`
	IsActive        bool   `gorm:"is_active,default:false"`
}

func (RangoMetaChains) TableName() string {
	return "rango_meta_chains"
}

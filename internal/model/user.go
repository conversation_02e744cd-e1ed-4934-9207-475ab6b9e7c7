package model

import (
	"time"
)

type User struct {
	ID        int64     `gorm:"column:id;primaryKey;autoIncrement"`
	Username  string    `gorm:"column:username;size:255;not null;unique"`
	Email     string    `gorm:"column:email;size:255;not null;unique"`
	Fund      float64   `gorm:"column:fund"`
	CreatedAt time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP"`
}

func (User) TableName() string {
	return "user"
}

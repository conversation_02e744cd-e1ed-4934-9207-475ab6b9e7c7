package model

type RangoHistory struct {
	BaseModel
	RequestId                         string        `gorm:"request_id;uniqueIndex"`
	UserAddress                       string        `gorm:"user_address"`
	FromBlockchain                    string        `gorm:"from_blockchain"`
	FromSymbol                        string        `gorm:"from_symbol"`
	FromAddress                       string        `gorm:"from_address"`
	ToBlockchain                      string        `gorm:"to_blockchain"`
	ToSymbol                          string        `gorm:"to_symbol"`
	ToAddress                         string        `gorm:"to_address"`
	RequestAmount                     string        `gorm:"request_amount"`
	OutputAmount                      string        `gorm:"output_amount"`
	ResultType                        string        `gorm:"result_type"`
	ValidationStatus                  string        `gorm:"validation_status"`
	WalletNotSupportingFromBlockchain bool          `gorm:"wallet_not_supporting_from_blockchain"`
	MissingBlockchains                string        `gorm:"missing_blockchains"`
	DiagnosisMessages                 string        `gorm:"diagnosis_messages"`
	Status                            string        `gorm:"status;index"`
	Step                              int64         `gorm:"step"`
	FailReason                        string        `gorm:"fail_reason"`
	Swaps                             []*RangoSwaps `gorm:"foreignKey:RequestId;references:RequestId" json:"swaps"`
}

// TableName specifies the table name for RangoHistory
func (RangoHistory) TableName() string {
	return "rango_history"
}

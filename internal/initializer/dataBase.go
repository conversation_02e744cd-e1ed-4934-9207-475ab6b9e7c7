package initializer

import (
	"fmt"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"os"
	"time"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/config"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

var newGorm = new(_gorm)

type _gorm struct{}

func (g *_gorm) Config(prefix string, singular bool) *gorm.Config {
	var general config.GeneralDB
	switch global.GVA_CONFIG.System.DbType {
	case "pgsql":
		general = global.GVA_CONFIG.Pgsql.GeneralDB
	case "clickhouse":
		general = global.GVA_CONFIG.Clickhouse.GeneralDB
	default:
		general = global.GVA_CONFIG.Pgsql.GeneralDB
	}
	return &gorm.Config{
		Logger: logger.New(NewWriter(general), logger.Config{
			SlowThreshold: 200 * time.Millisecond,
			LogLevel:      general.LogLevel(),
			Colorful:      true,
		}),
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   prefix,
			SingularTable: singular,
		},
		DisableForeignKeyConstraintWhenMigrating: true,
	}
}

type Writer struct {
	config config.GeneralDB
	writer logger.Writer
}

func NewWriter(config config.GeneralDB) *Writer {
	return &Writer{config: config}
}

// Printf format the print log
func (c *Writer) Printf(message string, data ...any) {

	// When there is a log, you need to output it to the console
	fmt.Printf(message, data...)

	// when zap is enabled logging will be printed
	if c.config.LogZap {
		switch c.config.LogLevel() {
		case logger.Silent:
			global.GVA_LOG.Debug(fmt.Sprintf(message, data...))
		case logger.Error:
			global.GVA_LOG.Error(fmt.Sprintf(message, data...))
		case logger.Warn:
			global.GVA_LOG.Warn(fmt.Sprintf(message, data...))
		case logger.Info:
			global.GVA_LOG.Info(fmt.Sprintf(message, data...))
		default:
			global.GVA_LOG.Info(fmt.Sprintf(message, data...))
		}
		return
	}
}

func Gorm() *gorm.DB {
	switch global.GVA_CONFIG.System.DbType {
	case "pgsql":
		global.GVA_ACTIVE_DBNAME = &global.GVA_CONFIG.Pgsql.Dbname
		return GormPgSql()
	default:
		global.GVA_ACTIVE_DBNAME = &global.GVA_CONFIG.Pgsql.Dbname
		return GormPgSql()
	}
}

func Clickhouse() *gorm.DB {
	return GormClickhouse()
}

func RegisterTables() {
	db := global.GVA_DB
	err := db.AutoMigrate(
		&model.Affiliate{},
		&model.RangoSwaps{},
		&model.RangoHistory{},
		&model.RangoMetaChains{},
		&model.RangoMetaSwappers{},
		&model.RangoMetaTokens{},
	)
	if err != nil {
		global.GVA_LOG.Error("register table failed", zap.Error(err))
		os.Exit(0)
	}

	global.GVA_LOG.Info("register table success")
}

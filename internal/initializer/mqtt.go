package initializer

import (
	"fmt"
	"log"
	"sync"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
)

var messagePubHandler mqtt.MessageHandler = func(client mqtt.Client, msg mqtt.Message) {
	fmt.Printf("Received message: %s from topic: %s\n", msg.Payload(), msg.Topic())
}

var connectHandler mqtt.OnConnectHandler = func(client mqtt.Client) {
	fmt.Println("Connected")
}

var connectLostHandler mqtt.ConnectionLostHandler = func(client mqtt.Client, err error) {
	fmt.Printf("Connect lost: %v", err)
}

var mqttClient mqtt.Client
var onceInitMqtt sync.Once

func InitMqtt() {
	onceInitMqtt.Do(func() {
		opts := mqtt.NewClientOptions()
		opts.AddBroker(fmt.Sprintf("%s://%s:%s/mqtt", global.GVA_CONFIG.Mqtt.Protocol, global.GVA_CONFIG.Mqtt.Host, global.GVA_CONFIG.Mqtt.Port))
		opts.SetClientID(fmt.Sprintf("xbit-indexer-%d", time.Now().UnixMicro()))
		opts.SetUsername(global.GVA_CONFIG.Mqtt.Username)
		opts.SetPassword(global.GVA_CONFIG.Mqtt.Password)
		opts.SetDefaultPublishHandler(messagePubHandler)
		opts.OnConnect = connectHandler
		opts.OnConnectionLost = connectLostHandler
		mqttClient = mqtt.NewClient(opts)
		if token := mqttClient.Connect(); token.Wait() && token.Error() != nil {
			log.Fatal("failed to connect to mqtt server: ", token.Error())
		}
	})

}

func GetMqttClient() *mqtt.Client {
	if mqttClient == nil {
		log.Fatal("mqtt client not initialized")
	}
	return &mqttClient
}

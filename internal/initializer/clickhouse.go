package initializer

import (
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/config"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gorm.io/driver/clickhouse"
	"gorm.io/gorm"
)

// GormClickhouse Initialize GormClickhouse database
func GormClickhouse() *gorm.DB {
	p := global.GVA_CONFIG.Clickhouse
	return initClickhouseDatabase(p)
}

// GormClickhouseByConfig Initialize Postgresql database by specifying parameters
func GormClickhouseByConfig(p config.Clickhouse) *gorm.DB {
	return initClickhouseDatabase(p)
}

// initClickhouseDatabase Helper functions for initializing Clickhouse database
func initClickhouseDatabase(p config.Clickhouse) *gorm.DB {
	if p.Dbname == "" {
		return nil
	}

	clickhouseConfig := clickhouse.Config{
		DSN:  p.Dsn(),
		Conn: nil,
	}
	if db, err := gorm.Open(clickhouse.New(clickhouseConfig), newGorm.Config(p.Prefix, p.<PERSON>)); err != nil {
		panic(err)
	} else {
		sqlDB, _ := db.DB()
		sqlDB.SetMaxIdleConns(p.MaxIdleConns)
		sqlDB.SetMaxOpenConns(p.MaxOpenConns)
		return db
	}
}

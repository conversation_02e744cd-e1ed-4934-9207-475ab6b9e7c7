package initializer

import (
	"context"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	repoAsset "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/asset"
	repoSwap "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/swap"
	repoSymbol "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/symbol"
	repoTrade "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/trade"
	task2 "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/task"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/task/asset"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/task/swap"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/task/symbol"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	"go.uber.org/zap"
)

func InitTask() {
	scheduler := task2.NewTaskScheduler()
	ctx, cancel := context.WithCancel(context.Background())
	scheduler.SetCancelFunc(cancel)
	go scheduler.RunWithSignal(ctx)
	swapTask := swap.NewSwapTask(repoSwap.Swap)
	err := scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskSyncRangoMetadata].ID, global.GVA_CONFIG.CronTasks[utils.TaskSyncRangoMetadata].Cron, swapTask.SyncRangoMeta())
	if err != nil {
		global.GVA_LOG.Error("task  = ", zap.Any("err", err))
	}

	symbolTask := symbol.NewSymbolTask(repoSymbol.Symbol, global.GVA_NATS_DEX)
	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskCrawlSymbolList].ID, global.GVA_CONFIG.CronTasks[utils.TaskCrawlSymbolList].Cron, symbolTask.CrawSymbolList())
	if err != nil {
		global.GVA_LOG.Error("task  = ", zap.Any("err", err))
	}

	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskCrawlCoinMarketCap].ID, global.GVA_CONFIG.CronTasks[utils.TaskCrawlCoinMarketCap].Cron, symbolTask.CrawlMarketCap())
	if err != nil {
		global.GVA_LOG.Error("task  = ", zap.Any("err", err))
	}

	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskCrawlCategoryList].ID, global.GVA_CONFIG.CronTasks[utils.TaskCrawlCategoryList].Cron, symbolTask.CrawCategoryList())
	if err != nil {
		global.GVA_LOG.Error("task  = ", zap.Any("err", err))
	}

	assetTask := asset.NewAssetTask(repoAsset.Asset, repoTrade.Trade, global.GVA_NATS_MEME)
	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskTrackUserBalance].ID, global.GVA_CONFIG.CronTasks[utils.TaskTrackUserBalance].Cron, assetTask.TrackUserBalances(ctx))
	if err != nil {
		global.GVA_LOG.Error("task  = ", zap.Any("err", err))
	}
}

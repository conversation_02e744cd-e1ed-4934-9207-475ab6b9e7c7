package initializer

import (
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/config"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// GormPgSql Initialize Postgresql database
func GormPgSql() *gorm.DB {
	p := global.GVA_CONFIG.Pgsql
	return initPgSqlDatabase(p)
}

// GormPgSqlByConfig Initialize Postgresql database by specifying parameters
func GormPgSqlByConfig(p config.Pgsql) *gorm.DB {
	return initPgSqlDatabase(p)
}

// initPgSqlDatabase Helper functions for initializing Postgresql database
func initPgSqlDatabase(p config.Pgsql) *gorm.DB {
	if p.Dbname == "" {
		return nil
	}
	pgsqlConfig := postgres.Config{
		DSN:                  p.Dsn(), // DSN data source name
		PreferSimpleProtocol: false,
	}
	if db, err := gorm.Open(postgres.New(pgsqlConfig), newGorm.Config(p.Prefix, p.Singular)); err != nil {
		panic(err)
	} else {
		sqlDB, _ := db.DB()
		sqlDB.SetMaxIdleConns(p.MaxIdleConns)
		sqlDB.SetMaxOpenConns(p.MaxOpenConns)
		return db
	}
}

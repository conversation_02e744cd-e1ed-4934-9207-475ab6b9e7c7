package initializer

import (
	"fmt"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/config"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// GormPgSql Initialize Postgresql database
func GormPgSql() *gorm.DB {
	p := global.GVA_CONFIG.Pgsql
	return initPgSqlDatabase(p)
}

// GormPgSqlByConfig Initialize Postgresql database by specifying parameters
func GormPgSqlByConfig(p config.Pgsql) *gorm.DB {
	return initPgSqlDatabase(p)
}

func initPgSqlDatabase(p config.Pgsql) *gorm.DB {
	if p.Dbname == "" {
		fmt.Printf("Database name is empty, skipping database initialization\n")
		return nil
	}

	dsn := p.Dsn()
	fmt.Printf("Attempting to connect to database with DSN: %s\n", dsn)

	pgsqlConfig := postgres.Config{
		DSN:                  dsn,
		PreferSimpleProtocol: false,
	}
	if db, err := gorm.Open(postgres.New(pgsqlConfig), gormConfig(p.Prefix, p.Singular)); err != nil {
		fmt.Printf("Failed to connect to database: %v\n", err)
		return nil
	} else {
		fmt.Printf("Successfully connected to database: %s\n", p.Dbname)
		db.InstanceSet("gorm:table_options", "ENGINE="+p.Engine)
		sqlDB, _ := db.DB()
		sqlDB.SetMaxIdleConns(p.MaxIdleConns)
		sqlDB.SetMaxOpenConns(p.MaxOpenConns)
		return db
	}
}

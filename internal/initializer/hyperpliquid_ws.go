package initializer

import (
	"net/url"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"go.uber.org/zap"
)

const (
	wsURL = "wss://api.hyperliquid.xyz/ws"
)

type HyperLiquidWebsocket struct {
	ws *websocket.Conn
	mu sync.Mutex
}

func NewHyperLiquidWSConnection() (*HyperLiquidWebsocket, error) {
	u, _ := url.Parse(wsURL)

	ws, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		global.GVA_LOG.Error("WebSocket connection error", zap.Error(err))
	}

	global.GVA_LOG.Info("Connected to HyperLiquid WebSocket")

	hyperLiquidWebsocket := &HyperLiquidWebsocket{
		ws: ws,
		mu: sync.Mutex{},
	}

	go func() {
		for {
			err := hyperLiquidWebsocket.WriteJSON(map[string]string{"method": "ping"})
			if err != nil {
				global.GVA_LOG.Error("Error sending ping", zap.Error(err))
				return
			}
			global.GVA_LOG.Info("Ping sent to HyperLiquid WebSocket")
			time.Sleep(50 * time.Second)
		}
	}()

	return hyperLiquidWebsocket, nil
}

func (h *HyperLiquidWebsocket) WriteMessage(messageType int, data []byte) error {
	h.mu.Lock()
	defer h.mu.Unlock()
	return h.ws.WriteMessage(messageType, data)
}

func (h *HyperLiquidWebsocket) WriteJSON(msg interface{}) error {
	h.mu.Lock()
	defer h.mu.Unlock()
	return h.ws.WriteJSON(msg)
}

func (h *HyperLiquidWebsocket) Close() error {
	h.mu.Lock()
	defer h.mu.Unlock()
	return h.ws.Close()
}

func (h *HyperLiquidWebsocket) ReadMessage() (int, []byte, error) {
	return h.ws.ReadMessage()
}

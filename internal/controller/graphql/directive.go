package graphql

import (
	"context"
	"fmt"

	"github.com/99designs/gqlgen/graphql"
)

func AuthDirective(ctx context.Context, obj interface{}, next graphql.Resolver) (interface{}, error) {
	// For now, we'll implement a simple auth check
	// In a real implementation, you would check JWT tokens, session, etc.
	
	// Get user from context (this would be set by middleware)
	user := ctx.Value("user")
	if user == nil {
		return nil, fmt.Errorf("access denied: user not authenticated")
	}
	
	return next(ctx)
}

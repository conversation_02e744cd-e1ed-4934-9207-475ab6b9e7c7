package resolvers

import (
	"context"
	"fmt"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/controller/graphql/gql_model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/response"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	user_signing "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/protogen/user/v1"
)

func (r *SwapResolver) GetExchangeMetaV2() (*gql_model.ExchangeMetaResponse, error) {
	var (
		respExchangeMeta response.RespExchangeMetaV2
	)
	rgChains, rgTokens, _, err := r.s.GetRangoMetaV2()
	if err != nil {
		return nil, err
	}
	rlTokens, rlChains, err := r.s.GetRelayMeta()
	if err != nil {
		return nil, err
	}
	err = respExchangeMeta.Assign(rlTokens, rlChains, rgTokens, rgChains)
	if err != nil {
		return nil, err
	}
	return utils.Translate[gql_model.ExchangeMetaResponse](respExchangeMeta), nil
}

func (r *SwapResolver) GetQuoteV2(input gql_model.QuoteRequest) (*gql_model.QuoteResponse, error) {
	req := request.ReqGetQuote{
		User:          input.User,
		OriginId:      input.OriginID,
		DestinationId: input.DestinationID,
		Recipient:     input.Recipient,
		Amount:        input.Amount,
		Type:          input.Type,
	}
	var (
		resp *response.RespQuote
		err  error
	)
	if err := req.ValidateBasic(); err != nil {
		return nil, fmt.Errorf("invalid request parameters: %v", err)
	}
	if req.Type == "relay" {
		resp, err = r.s.GetRelayQuote(req)
		if err != nil {
			return nil, fmt.Errorf("failed to get best route: %v", err)
		}
	}
	if req.Type == "rango" {
		resp, err = r.s.GetBestRouteV2(req)
		if err != nil {
			return nil, fmt.Errorf("failed to get best route: %v", err)
		}
	}
	return utils.Translate[gql_model.QuoteResponse](resp), nil
}

func (r *SwapResolver) SignUserTxV2(input gql_model.SignUserTransactionRequest, ctx context.Context) (*gql_model.SignUserTransactionResponse, error) {
	req := request.ReqSignTx{
		RequestId: input.RequestID,
		Step:      int64(input.Step),
	}

	if err := utils.Verify(req, utils.CreateTxVerify); err != nil {
		return nil, fmt.Errorf("validation error: %w", err)
	}

	chainType := user_signing.ChainType_EVM
	switch input.Chain {
	case "Ethereum":
		chainType = user_signing.ChainType_EVM
	case "BSC":
		chainType = user_signing.ChainType_BSC
	case "Polygon":
		chainType = user_signing.ChainType_POLYGON
	case "Arbitrum":
		chainType = user_signing.ChainType_ARB
	case "Solana":
		chainType = user_signing.ChainType_SOLANA
		signedTx, err := r.userService.SignTransaction(
			context.Background(),
			&user_signing.SignUserTransactionRequest{
				Chain:               chainType,
				UserId:              ctx.Value("userId").(string),
				Address:             input.Address,
				UnsignedTransaction: input.UnsignedTransaction,
			},
		)
		if err != nil {
			return nil, fmt.Errorf("failed to sign SignUserTransactionRequest: %w", err)
		}
		return &gql_model.SignUserTransactionResponse{
			SignedTransaction: signedTx.SignedTransaction,
		}, nil
	}
	signedTx, err := r.userService.SignEvmTransaction(
		context.Background(),
		&user_signing.SignUserTransactionEvmRequest{
			Chain:                chainType,
			UserId:               ctx.Value("userId").(string),
			To:                   input.To,
			Data:                 input.Data,
			Value:                input.Value,
			MaxFeePerGas:         input.MaxFeePerGas,
			MaxPriorityFeePerGas: input.MaxPriorityFeePerGas,
			GasLimit:             input.GasLimit,
			GasPrice:             input.GasPrice,
			From:                 input.From,
			Nonce:                input.Nonce,
		},
	)
	if err != nil {
		return nil, fmt.Errorf("failed to sign SignUserTransactionEvmRequest: %w", err)
	}
	return &gql_model.SignUserTransactionResponse{
		SignedTransaction: signedTx.SignedTransaction,
	}, nil
}

func (r *SwapResolver) CheckStatusV2(input gql_model.CheckStatusRequest) (*gql_model.CheckStatusResponse, error) {
	if err := utils.Verify(input, utils.TxStatusVerify); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}
	if isHexHash(input.RequestID) {
		resp, err := r.s.CheckRelayStatus(request.ReqCheckStatus{
			RequestId: input.RequestID,
			TxHash:    input.TxHash,
			Step:      int64(input.Step),
		})
		if err != nil {
			return nil, fmt.Errorf("failed to check relay status: %w", err)
		}
		if resp.Status == "unkonw" {
			resp.Status = "failed"
		} else if resp.Status != "success" {
			resp.Status = "running"
		}

		result := utils.TranslateByJSON[gql_model.CheckStatusResponse](resp)
		return result, nil
	}
	resp, err := r.s.CheckSwapStatus(request.ReqCheckStatus{
		RequestId: input.RequestID,
		TxHash:    input.TxHash,
		Step:      int64(input.Step),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to check rango status: %w", err)
	}
	if resp.Status == "pending" {
		resp.Status = "running"
	}
	result := utils.TranslateByJSON[gql_model.CheckStatusResponse](resp)
	return result, nil
}
func isHexHash(id string) bool {
	return len(id) == 66 && len(id) > 2 && id[:2] == "0x"
}
func (r *SwapResolver) GetHistoriesV2(input gql_model.HistoryRequest) (*gql_model.HistoryResponse, error) {
	// You might expand logic to handle pagination/time filtering here
	list, total, err := r.s.Histories(input.Blockchain, input.Address)
	if err != nil {
		return nil, fmt.Errorf("failed to get histories: %w", err)
	}

	var resp response.RespHistory
	err = resp.HistoryAssign(total, list)
	if err != nil {
		return nil, err
	}

	return utils.Translate[gql_model.HistoryResponse](resp), nil
}

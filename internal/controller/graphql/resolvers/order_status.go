package resolvers

import (
	"context"
	"fmt"
	"github.com/google/uuid"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/controller/graphql/gql_model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	repoOrderStatus "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/orderStatus"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/orderStatus"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
)

type OrderStatusResolver struct {
	o service.OrderStatusI
}

func NewOrderStatusResolver() *OrderStatusResolver {
	orderStatusService := orderStatus.NewOrderStatusService(repoOrderStatus.OrderStatus)

	return &OrderStatusResolver{
		o: orderStatusService,
	}
}

func (r *OrderStatusResolver) GetUserOrders(ctx context.Context, input gql_model.UserOrderStatusRequest) (*gql_model.UserOrderStatusResponse, error) {
	userID, err := uuid.Parse(ctx.Value("userId").(string))
	if err != nil {
		return nil, fmt.Errorf("failed to parse user id: %w", err)
	}

	resp, err := r.o.GetOrderStatus(request.ReqGetUserOrderStatus{
		UserID: userID,
		LastID: uuid.MustParse(input.LastID),
		Limit:  input.Limit,
	})
	if err != nil {
		return nil, fmt.Errorf("get open order failed: %w", err)
	}

	return utils.Translate[gql_model.UserOrderStatusResponse](resp), nil
}

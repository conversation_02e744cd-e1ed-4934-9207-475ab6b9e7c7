package resolvers

import (
	"context"
	"fmt"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/controller/graphql/gql_model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	repoAsset "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/asset"
	repoSymbol "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/symbol"
	repoTrade "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/trade"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/asset"
)

type AssetResolver struct {
	s service.AssetI
	v *validator.Validate
}

func NewAssetResolver() *AssetResolver {
	assetService := asset.NewAssetService(repoAsset.Asset, repoSymbol.Symbol, repoTrade.Trade, global.GVA_NATS_MEME)

	return &AssetResolver{
		s: assetService,
		v: validator.New(validator.WithRequiredStructEnabled()),
	}
}

func (r *AssetResolver) CallbackUserBalance(ctx context.Context, input gql_model.CallbackUserBalanceRequest) (*gql_model.CallbackUserBalanceResponse, error) {
	userID, err := uuid.Parse(ctx.Value("userId").(string))
	if err != nil {
		return nil, fmt.Errorf("failed to parse user id: %w", err)
	}

	err = r.s.CallbackUserBalance(request.ReqCallbackUserBalance{
		UserID:        userID,
		WalletAddress: input.WalletAddress,
	})

	if err != nil {
		return nil, fmt.Errorf("failed to call CallbackUserBalance: %w", err)
	}

	return &gql_model.CallbackUserBalanceResponse{
		Status: "success",
	}, nil
}

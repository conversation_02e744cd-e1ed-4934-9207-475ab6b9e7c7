package resolvers

import (
	"context"
	"fmt"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/controller/graphql/gql_model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	repoAsset "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/asset"
	repoSymbol "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/symbol"
	repoTrading "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/trade"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/trade"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
)

type TradingResolver struct {
	s service.TradeI
	v *validator.Validate
}

func NewTradingResolver() *TradingResolver {
	tradingService := trade.NewTradeService(repoTrading.Trade, repoAsset.Asset, repoSymbol.Symbol)

	return &TradingResolver{
		s: tradingService,
		v: validator.New(validator.WithRequiredStructEnabled()),
	}
}

func (r *TradingResolver) GetUserOpenOrder(ctx context.Context, input gql_model.UserOpenOrderRequest) (*gql_model.UserOpenOrderResponse, error) {
	userID, err := uuid.Parse(ctx.Value("userId").(string))
	if err != nil {
		return nil, fmt.Errorf("failed to parse user id: %w", err)
	}

	resp, err := r.s.GetUserOpenOrder(request.ReqGetUserOpenOrder{
		UserID: userID,
		LastID: uuid.MustParse(input.LastID),
		Limit:  input.Limit,
	})
	if err != nil {
		return nil, fmt.Errorf("get open order failed: %w", err)
	}

	return utils.Translate[gql_model.UserOpenOrderResponse](resp), nil
}

func (r *TradingResolver) GetUserPosition(ctx context.Context, input gql_model.UserPositionRequest) (*gql_model.UserPositionResponse, error) {
	userID, err := uuid.Parse(ctx.Value("userId").(string))
	if err != nil {
		return nil, fmt.Errorf("failed to parse user id: %w", err)
	}

	resp, err := r.s.GetUserPosition(request.ReqGetUserPosition{
		UserID:        userID,
		WalletAddress: input.WalletAddress,
	})
	if err != nil {
		return nil, fmt.Errorf("get user position failed: %w", err)
	}

	return utils.Translate[gql_model.UserPositionResponse](resp), nil
}

func (r *TradingResolver) StoreTxInformation(ctx context.Context, input gql_model.StoreTxInformationRequest) (*gql_model.StoreTxInformationResponse, error) {
	userID, err := uuid.Parse(ctx.Value("userId").(string))
	if err != nil {
		return nil, fmt.Errorf("failed to parse user id: %w", err)
	}

	id, err := uuid.NewV7()
	if err != nil {
		return nil, err
	}

	resp, err := r.s.StoreTxInformation(request.ReqStoreTxInformation{
		ID:           id,
		UserID:       userID,
		Symbol:       input.Symbol,
		OID:          uuid.MustParse(input.Oid),
		Type:         input.OrderType,
		Direction:    input.Direction,
		Status:       input.Status,
		Size:         input.Size,
		OriginSize:   input.Size,
		OrderPx:      input.Price,
		ReduceOnly:   input.ReduceOnly,
		VaultAddress: input.VaultAddress,
	})
	if err != nil {
		return nil, fmt.Errorf("store tx information failed: %w", err)
	}

	return utils.Translate[gql_model.StoreTxInformationResponse](resp), nil
}

func (r *TradingResolver) GetUserTradeHistory(ctx context.Context, input gql_model.UserTradeHistoryRequest) (*gql_model.UserTradeHistoryResponse, error) {
	userID, err := uuid.Parse(ctx.Value("userId").(string))
	if err != nil {
		return nil, fmt.Errorf("failed to parse user id: %w", err)
	}

	resp, err := r.s.GetUserTradeHistory(request.ReqGetUserTradeHistory{
		UserID:        userID,
		WalletAddress: input.WalletAddress,
	})
	if err != nil {
		return nil, fmt.Errorf("get user trade history failed: %w", err)
	}

	return utils.Translate[gql_model.UserTradeHistoryResponse](resp), nil
}

func (r *TradingResolver) GetUserPrevDayBalance(ctx context.Context, input gql_model.UserPrevDayBalanceRequest) (*gql_model.UserPrevDayBalanceResponse, error) {
	userID, err := uuid.Parse(ctx.Value("userId").(string))
	if err != nil {
		return nil, fmt.Errorf("failed to parse user id: %w", err)
	}

	resp, err := r.s.GetUserPrevDayBalance(request.ReqGetUserPrevDayBalance{
		UserID:        userID,
		WalletAddress: input.WalletAddress,
	})

	return utils.Translate[gql_model.UserPrevDayBalanceResponse](resp), nil
}

package resolvers

import (
	"context"
	"fmt"
	"github.com/google/uuid"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/controller/graphql/gql_model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	repoMiscEvent "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/miscEvent"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/miscEvents"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
)

type MiscEventResolver struct {
	e service.MiscEventI
}

func NewMiscEventResolver() *MiscEventResolver {
	miscEventService := miscEvents.NewMiscEventService(repoMiscEvent.MiscEvent)

	return &MiscEventResolver{
		e: miscEventService,
	}
}

func (r *MiscEventResolver) GetUserEvents(ctx context.Context, input gql_model.UserMiscEventRequest) (*gql_model.UserMiscEventResponse, error) {
	userID, err := uuid.Parse(ctx.Value("userId").(string))
	if err != nil {
		return nil, fmt.Errorf("failed to parse user id: %w", err)
	}

	resp, err := r.e.GetUserMiscEvents(request.ReqGetUserMiscEvents{
		UserID: userID,
		LastID: uuid.MustParse(input.LastID),
		Limit:  input.Limit,
	})
	if err != nil {
		return nil, fmt.Errorf("get open order failed: %w", err)
	}

	return utils.Translate[gql_model.UserMiscEventResponse](resp), nil
}

package resolvers

import (
	"context"
	"fmt"
	"regexp"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/controller/graphql/gql_model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	repoSymbol "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/symbol"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/symbol"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	"go.uber.org/zap"
)

type SymbolResolver struct {
	s service.SymbolI
	v *validator.Validate
}

func NewSymbolResolver() *SymbolResolver {
	symbolService := symbol.NewSymbolService(repoSymbol.Symbol)

	return &SymbolResolver{
		s: symbolService,
		v: validator.New(validator.WithRequiredStructEnabled()),
	}
}

// GetOHLC
func (r *SymbolResolver) GetOHLC(input gql_model.OHLCRequest) (*gql_model.OHLCResponse, error) {
	interval, ok := utils.IntervalMap[input.Interval.String()]
	if !ok {
		return nil, fmt.Errorf("interval '%s' is not valid", input.Interval)
	}

	req := request.ReqOHLC{
		Symbol:    input.Symbol,
		Interval:  interval,
		Timestamp: int64(input.Timestamp),
		IsForward: input.IsForward,
		Limit:     input.Limit,
	}

	if req.Limit > 10000 {
		req.Limit = 10000 // Limit, might move to config
	}

	resp, err := r.s.GetOHLC(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get OHLC data: %w", err)
	}

	return utils.Translate[gql_model.OHLCResponse](resp), nil
}

func (r *SymbolResolver) GetSymbolDetail(input gql_model.SymbolDetailRequest) (*gql_model.SymbolDetailResponse, error) {
	req := request.ReqGetSymbolDetail{
		Symbol: input.Symbol,
	}
	resp, err := r.s.GetSymbolDetail(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get symbol detail data: %w", err)
	}

	return utils.Translate[gql_model.SymbolDetailResponse](resp), nil
}

func (r *SymbolResolver) GetSymbolList(input gql_model.SymbolListRequest) (*gql_model.SymbolListResponse, error) {
	req := request.ReqSymbolList{
		Condition: string(input.Condition),
		Category:  utils.StringPointerToString(input.Category),
	}

	if err := req.ValidateBasic(); err != nil {
		return nil, err
	}

	resp, err := r.s.GetSymbolList(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get symbol list: %w", err)
	}

	return utils.Translate[gql_model.SymbolListResponse](resp), nil
}

func (r *SymbolResolver) UpsertFavoriteSymbol(ctx context.Context, input gql_model.UpsertFavoriteSymbolRequest) (*gql_model.UpsertFavoriteSymbolResponse, error) {
	userID, err := uuid.Parse(ctx.Value("userId").(string))
	if err != nil {
		return nil, fmt.Errorf("failed to parse user id: %w", err)
	}

	resp, err := r.s.UpsertFavoriteSymbol(request.ReqUpsertFavoriteSymbol{
		Symbol:     input.Symbol,
		IsFavorite: input.IsFavorite,
		UserID:     userID,
	})
	if err != nil {
		return nil, fmt.Errorf("get status failed: %w", err)
	}
	return utils.Translate[gql_model.UpsertFavoriteSymbolResponse](resp), nil
}

func (r *SymbolResolver) GetFavoriteSymbols(ctx context.Context) (*gql_model.FavoriteSymbolsResponse, error) {
	userID, err := uuid.Parse(ctx.Value("userId").(string))
	if err != nil {
		return nil, fmt.Errorf("failed to parse user id: %w", err)
	}

	resp, err := r.s.GetFavoriteSymbols(request.ReqGetFavoriteSymbols{
		UserID: userID,
	})
	if err != nil {
		return nil, fmt.Errorf("get favorite symbols failed: %w", err)
	}

	return utils.Translate[gql_model.FavoriteSymbolsResponse](resp), nil
}

func (r *SymbolResolver) GetCategory() (*gql_model.CategoryResponse, error) {
	resp, err := r.s.GetCategory()
	if err != nil {
		return nil, fmt.Errorf("failed to get category: %w", err)
	}

	return utils.Translate[gql_model.CategoryResponse](resp), nil
}

func (r *SymbolResolver) GetUserSymbolPreference(ctx context.Context, input gql_model.UserSymbolPreferenceRequest) (*gql_model.UserSymbolPreferenceResponse, error) {
	userID, err := uuid.Parse(ctx.Value("userId").(string))

	if err != nil {
		global.GVA_LOG.Error("failed to parse user id", zap.Error(err))
		return nil, fmt.Errorf("failed to parse user id")
	}

	resp, err := r.s.GetUserSymbolPreference(request.GetUserSymbolPreferenceDto{
		UserID: userID,
		Symbol: input.Symbol,
	})

	if err != nil {
		return nil, fmt.Errorf("get user symbol preference failed: %w", err)
	}

	return utils.Translate[gql_model.UserSymbolPreferenceResponse](resp), nil
}

func (r *SymbolResolver) UpdateUserSymbolPreference(ctx context.Context, input gql_model.UpdateUserSymbolPreferenceRequest) (*gql_model.UserSymbolPreferenceResponse, error) {
	userID, err := uuid.Parse(ctx.Value("userId").(string))

	if err != nil {
		global.GVA_LOG.Error("failed to parse user id", zap.Error(err))
		return nil, fmt.Errorf("failed to parse user id")
	}

	resp, err := r.s.UpsertUserSymbolPreference(request.UpsertUserSymbolPreferenceDto{
		UserID:   userID,
		Symbol:   input.Symbol,
		IsCross:  input.IsCross,
		Leverage: input.Leverage,
	})

	if err != nil {
		return nil, fmt.Errorf("get user symbol preference failed: %w", err)
	}

	return utils.Translate[gql_model.UserSymbolPreferenceResponse](resp), nil
}

func (r *SymbolResolver) GetPopularSymbol(input gql_model.PopularSymbolRequest) (*gql_model.PopularSymbolResponse, error) {
	resp, err := r.s.GetPopularSymbol(input.Number)
	if err != nil {
		return nil, fmt.Errorf("failed to get popular symbol: %w", err)
	}

	return utils.Translate[gql_model.PopularSymbolResponse](resp), nil
}

func (r *SymbolResolver) GetNewSymbol() (*gql_model.NewSymbolResponse, error) {
	resp, err := r.s.GetNewSymbol()
	if err != nil {
		return nil, fmt.Errorf("failed to get new symbol: %w", err)
	}

	return utils.Translate[gql_model.NewSymbolResponse](resp), nil
}

func (r *SymbolResolver) SearchSymbol(input gql_model.SearchSymbolRequest) (*gql_model.SearchSymbolResponse, error) {
	filter := input.Filter
	re, err := regexp.Compile(`[^a-zA-Z0-9]`)
	if err != nil {
		return nil, fmt.Errorf("failed to check input: %w", err)
	}

	if re.MatchString(filter) {
		return &gql_model.SearchSymbolResponse{}, nil
	}

	resp, err := r.s.SearchSymbol(filter)
	if err != nil {
		return nil, fmt.Errorf("failed to search symbol: %w", err)
	}

	return utils.Translate[gql_model.SearchSymbolResponse](resp), nil
}

func (r *SymbolResolver) GetAlertSymbolsSetting(ctx context.Context) (*gql_model.GetAlertSymbolSettingResponse, error) {
	userID, err := uuid.Parse(ctx.Value("userId").(string))

	if err != nil {
		global.GVA_LOG.Error("failed to parse user id", zap.Error(err))
		return nil, fmt.Errorf("failed to parse user id")
	}

	resp, err := r.s.GetAlertSymbolsSetting(request.ReqGetAlertSymbolsSetting{
		UserID: userID,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get alert symbols setting: %w", err)
	}

	return utils.Translate[gql_model.GetAlertSymbolSettingResponse](resp), nil
}

func (r *SymbolResolver) CreateAlertSymbolSetting(ctx context.Context, input gql_model.CreateAlertSymbolSettingRequest) (*gql_model.CreateAlertSymbolSettingResponse, error) {
	userID, err := uuid.Parse(ctx.Value("userId").(string))

	if err != nil {
		global.GVA_LOG.Error("failed to parse user id", zap.Error(err))
		return nil, fmt.Errorf("failed to parse user id")
	}

	settingID, err := r.s.CreateAlertSymbolSetting(request.ReqCreateAlertSymbolSetting{
		UserID:         userID,
		Symbol:         input.Symbol,
		Type:           input.Type.String(),
		IsReminderOnce: input.IsReminderOnce,
		Value:          input.Value,
		IsActivated:    true,
		Note:           utils.StringPointerToString(input.Note),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create alert symbol setting: %w", err)
	}

	return &gql_model.CreateAlertSymbolSettingResponse{
		Status:    "success",
		SettingID: settingID,
	}, nil
}

func (r *SymbolResolver) UpdateAlertSymbolSetting(ctx context.Context, input gql_model.UpdateAlertSymbolSettingRequest) (*gql_model.UpdateAlertSymbolSettingResponse, error) {
	userID, err := uuid.Parse(ctx.Value("userId").(string))

	if err != nil {
		global.GVA_LOG.Error("failed to parse user id", zap.Error(err))
		return nil, fmt.Errorf("failed to parse user id")
	}

	err = r.s.UpdateAlertSymbolSetting(request.ReqUpdateAlertSymbolSetting{
		UserID:      userID,
		SettingID:   uuid.MustParse(input.SettingID),
		IsActivated: input.IsActivated,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to update alert symbol setting: %w", err)
	}

	return &gql_model.UpdateAlertSymbolSettingResponse{
		Status: "success",
	}, nil
}

func (r *SymbolResolver) DeleteAlertSymbolSetting(ctx context.Context, input gql_model.DeleteAlertSymbolSettingRequest) (*gql_model.DeleteAlertSymbolSettingResponse, error) {
	settings := make([]uuid.UUID, 0)
	for _, setting := range input.SettingIds {
		settings = append(settings, uuid.MustParse(setting))
	}

	err := r.s.DeleteAlertSymbolSetting(request.ReqDeleteAlertSymbolSetting{
		SettingsID: settings,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to update alert symbol setting: %w", err)
	}

	return &gql_model.DeleteAlertSymbolSettingResponse{
		Status: "success",
	}, nil
}

func (r *SymbolResolver) GetSignals(ctx context.Context, input gql_model.SignalRequest) (*gql_model.SignalResponse, error) {

	resp, err := r.s.GetSymbolSignal(request.ReqSignals{
		User:   *input.User,
		Offset: *input.Skip,
		Limit:  *input.Limit,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to update alert symbol setting: %w", err)
	}

	return utils.Translate[gql_model.SignalResponse](resp), nil
}

package gql_error

import (
	"errors"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils/app_error"
)

/*
New
Example GQL response if using GqlError:

	{
	  "errors": [
	    {
	      "message": "not implemented",
	      "path": [
	        "createOrder"
	      ],
	      "extensions": {
	        "code": "NotImplemented"
	      }
	    }
	  ],
	  "data": null
	}

extensions.code will be used for error handling.
using standard error will not have `extensions`.
*/
func New(code, message string) *app_error.AppError {
	return app_error.New(code, message)
}
func From(code string, e error) *app_error.AppError {
	return app_error.New(code, e.Error())
}
func FromAppError(e app_error.AppError) *app_error.AppError {
	return &e
}
func NewWithMeta(code, message string, meta map[string]any) *app_error.AppError {
	return app_error.NewWithMeta(code, message, meta)
}
func FromWithMeta(e error, meta map[string]any) *app_error.AppError {
	var ae *app_error.AppError
	if errors.As(e, &ae) {
		return ae
	}
	return app_error.NewWithMeta(app_error.Unknown, e.<PERSON>(), meta)
}

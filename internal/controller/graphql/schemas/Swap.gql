type Token {
  address: String!
  blockChain: String!
  symbol: String!
  name: String!
  image: String
  usdPrice: Float
  decimals: Int
  isPopular: Boolean
  isSecondaryCoin: Boolean
  coinSource: String
  coinSourceUrl: String
  supportedSwappers: [String!]
}

type RangoMetaTokens {
  id: ID!
  address: String!
  blockChain: String!
  symbol: String!
  name: String
  image: String
  usdPrice: Float
  decimals: Int
  isPopular: Boolean
  isSecondaryCoin: Boolean
  coinSource: String
  coinSourceUrl: String
  supportedSwappers: [String]
}

type RangoMetaTokensWithChain {
  id: ID!
  address: String!
  blockChain: String!
  symbol: String!
  name: String
  image: String
  usdPrice: Float
  decimals: Int
  isPopular: Boolean
  isSecondaryCoin: Boolean
  coinSource: String
  coinSourceUrl: String
  supportedSwappers: [String]
  chainDetail: RangoMetaChains
}

type RangoMetaSwappers {
  id: ID!
  swapperId: String!
  title: String
  logo: String
  swapperGroup: String
  types: String
  enabled: Boolean
}

type RangoMetaChains {
  id: ID!
  name: String!
  chainId: String
  defaultDecimals: Int
  addressPatterns: String
  feeAssets: String
  logo: String
  displayName: String
  shortName: String
  sort: Int
  color: String
  enabled: Boolean
  type: String
  info: String
}

type RangoMetaResponse {
  tokens: [RangoMetaTokensWithChain]
  swappers: [RangoMetaSwappers]
  blockchains: [RangoMetaChains]
  popularTokens: [RangoMetaTokens]
}

type RangoSwaps {
  id: ID!
  requestId: String!
  userAddress: String!
  swapperId: String!
  swapperLogo: String
  swapperType: String!
  fromAmount: String!
  toAmount: String!
  fee: String
  estimatedTimeInSeconds: Int!
  swapChainType: String!
  maxRequiredSign: Int!
  step: Int!
  callData: String
  callDataHash: String
  txHash: String
}

type RangoHistory {
  id: ID!
  requestId: String!
  userAddress: String!
  fromBlockchain: String!
  fromSymbol: String!
  fromAddress: String!
  toBlockchain: String!
  toSymbol: String!
  toAddress: String!
  requestAmount: String!
  outputAmount: String!
  resultType: String!
  validationStatus: String!
  walletNotSupportingFromBlockchain: Boolean!
  missingBlockchains: String
  diagnosisMessages: String
  status: String!
  step: Int!
  failReason: String
  swaps: [RangoSwaps!]
}

input RangoHistoryRequest {
  address: String!
  blockchain: String!
  page: Int!
  pageSize: Int!
  startTime: Int
  endTime: Int
  status: String
}

type RangoHistoryResponse {
  total: Int!
  list: [RangoHistory!]!
}

input CheckStatusRequest {
  requestId: String!
  txHash: String!
  step: Int!
}

type ExplorerInfo {
  url: String!
  description: String!
}

type Referral {
  amount: String!
  blockChain: String!
  symbol: String!
  address: String
  decimals: Int!
  type: String!
}

type CheckStatusTx {
  data: String!
  to: String!
  value: String!
  gasLimit: String!
  gasPrice: String!
  nonce: String!
}

type OutputToken {
  blockchain: String!
  symbol: String!
  image: String!
  address: String!
  usdPrice: Float!
  decimals: Int!
  name: String
  isPopular: Boolean!
  isSecondaryCoin: Boolean!
  coinSource: String
  coinSourceUrl: String
  supportedSwappers: [String!]!
}

type BridgeExtra {
  requireRefundAction: Boolean!
  srcTx: String!
  destTx: String!
}

type CheckStatusResponse {
  status: String!
  extraMessage: String
  failedType: String
  timestamp: Int!
  outputAmount: String!
  explorerUrl: [ExplorerInfo]
  referrals: [Referral]
  newTx: CheckStatusTx
  diagnosisUrl: String
  steps: JSON
  outputToken: OutputToken!
  outputType: String!
  bridgeExtra: BridgeExtra!
  error: String
  errorCode: Int
  traceId: Int
}

input CallBackRequest {
  requestId: String!
  step: Int!
  txHash: String!
}

type CallBackResponse {
  success: String!
}

input CheckApprovalRequest {
  requestId: String!
  txHash: String!
}

type CheckApprovalResponse {
  isApproved: Boolean!
  txStatus: String!
  currentApprovedAmount: String!
  requiredApprovedAmount: String!
}

input ValidationsRequest {
  balance: Boolean!
  fee: Boolean!
  approve: Boolean!
}

input UserSettingsRequest {
  slippage: Float!
  infiniteApprove: Boolean!
}

input CreateTxRequest {
  requestId: String!
  step: Int!               # int64 as String
  userSettings: UserSettingsRequest!
  validations: ValidationsRequest!
}

type Transaction {
  type: String!
  blockChain: String!
  isApprovalTx: Boolean!
  from: String!
  to: String!
  spender: String!
  data: String!
  value: String!
  gasLimit: String!
  gasPrice: String!
  maxPriorityFeePerGas: String!
  maxFeePerGas: String!
  nonce: String!
  identifier: String!
  instructions: [String!]!
  recentBlockhash: String!
  signatures: [String!]!
  serializedMessage: [Int!]!
  txType: String!
}

type CreateTxResponse {
  ok: Boolean!
  error: String
  errorCode: Int!
  traceId: Int!
  signUserTransactionEvm: SignUserTransactionEvmResponse!
  transaction: Transaction
}
enum ChainType {
  EVM
  SOLANA
  TRON
  ARB
  BSC
  POLYGON
}
type SignUserTransactionEvmResponse {
  user_id: String!
  to: String!
  data: String!
  value: String
  chain: ChainType!
  maxFeePerGas: String
  maxPriorityFeePerGas: String
  gasLimit: String
  gasPrice: String
  from: String!
  signed_transaction: String!
}

input SelectedWallet {
  blockchain: String!
  address: String!
}

input ConfirmRouteRequest {
  selectedWallets: [SelectedWallet!]!
  destination: String!
  requestId: String!
}

type ConfirmRouteResponse {
  requestId: String!
  status: String!
  routeErr: String
}

input GetBestRouteRequest {
  fromBlockChain: String!
  fromSymbol: String!
  fromAddress: String!
  fromTokenAddress: String
  toBlockChain: String!
  toSymbol: String!
  toTokenAddress: String
  toAddress: String!
  amount: String!
}

type GetBestRouteResponse {
  from: FromTo!
  to: FromTo!
  requestAmount: String!
  requestId: String!
  result: Result!
  validationStatus: String!
  walletNotSupportingFromBlockchain: Boolean!
  missingBlockchains: [String!]!
  diagnosisMessages: [String!]!
  error: String
  errorCode: String
  traceId: String
}

type FromTo {
  blockchain: String!
  symbol: String!
  address: String!
}

type Result {
  requestId: String!
  outputAmount: String!
  swaps: [Swap!]!
  resultType: String!
  scores: [Score!]!
  tags: [Tag!]!
  walletNotSupportingFromBlockchain: Boolean!
  missingBlockchains: [String!]!
  priceImpactUsd: String!
  priceImpactUsdPercent: String!
}

type Swap {
  swapperId: String!
  swapperLogo: String!
  swapperType: String!
  from: TokenInfo!
  to: TokenInfo!
  fromAmount: String!
  fromAmountPrecision: String!
  fromAmountMinValue: String
  fromAmountMaxValue: String
  fromAmountRestrictionType: String
  toAmount: String!
  fee: [Fee!]!
  estimatedTimeInSeconds: Int!
  swapChainType: String!
  routes: [Route!]!
  recommendedSlippage: RecommendedSlippage!
  warnings: [String!]!
  timeStat: TimeStat!
  includesDestinationTx: Boolean!
  internalSwaps: [InternalSwaps!]!
  maxRequiredSign: Int!
}

type TokenInfo {
  symbol: String!
  logo: String!
  blockchainLogo: String!
  address: String!
  blockchain: String!
  decimals: Int!
  usdPrice: Float!
}

type Fee {
  asset: Asset!
  expenseType: String!
  amount: String!
  name: String!
  meta: Meta
  price: Float!
}

type Asset {
  blockchain: String!
  symbol: String!
  address: String!
}

type Meta {
  type: String!
  gasLimit: String!
  gasPrice: String!
}

type Route {
  nodes: [Nodes!]!
}

type Nodes {
  nodes: [Node!]!
  from: String!
  fromLogo: String!
  fromAddress: String!
  fromBlockchain: String!
  to: String!
  toLogo: String!
  toAddress: String!
  toBlockchain: String!
}

type Node {
  inputAmount: String!
  marketId: String!
  marketName: String!
  outputAmount: String!
  percent: Float!
  pools: [String!]!
}

type RecommendedSlippage {
  error: Boolean!
  slippage: String!
}

type TimeStat {
  min: Int!
  avg: Int!
  max: Int!
}

type InternalSwaps {
  swapperId: String!
  swapperLogo: String!
  swapperType: String!
  from: TokenInfo!
  to: TokenInfo!
  fromAmount: String!
  fromAmountPrecision: String!
  fromAmountMinValue: String
  fromAmountMaxValue: String
  fromAmountRestrictionType: String
  toAmount: String!
  fee: [Fee!]!
  estimatedTimeInSeconds: Int!
  swapChainType: String!
  routes: [Route!]!
  recommendedSlippage: String!
  warnings: String!
  timeStat: String!
  includesDestinationTx: Boolean!
  internalSwaps: [InternalSwaps!]!
  maxRequiredSign: Int!
}

type Score {
  preferenceType: String!
  score: Int!
}

type Tag {
  label: String!
  value: String!
}

input GetAllPossibleRoutesRequest {
  fromBlockchain: String!
  fromSymbol: String!
  fromTokenAddress: String
  toBlockchain: String!
  toSymbol: String!
  toTokenAddress: String
  amount: String!
  slippage: String!
}

type GetAllPossibleRoutesResponse {
  from: FromTo!
  to: FromTo!
  requestAmount: String!
  routeId: String!
  results: [Result!]!
  diagnosisMessages: [String!]!
  error: String
  errorCode: Int!
  traceId: Int!
}

type SignTxResponse {
  signedTransaction: String!
  from: String!
  to: String!
  data: String!
  value: String!
  gasLimit: String!
  gasPrice: String!
  maxPriorityFeePerGas: String!
  maxFeePerGas: String!
  nonce: String!
  blockChain: String!
}

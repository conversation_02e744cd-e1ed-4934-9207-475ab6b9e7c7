type User {
  id: ID!
  email: String
  isFirstLogin: Boolean!
  isExportedWallet: Boolean!
  invitationCode: String
  createdAt: Time!
  updatedAt: Time!
  wallets: [UserWallet!]!
  referral: Referral
  referralSnapshot: ReferralSnapshot
  referrals: [Referral!]!
}

type UserWallet {
  id: ID!
  userId: ID!
  chain: String!
  name: String
  walletAddress: String!
  walletId: ID
  walletAccountId: ID
  walletType: WalletType
  createdAt: Time!
  updatedAt: Time!
  user: User!
}

type Referral {
  id: Int!
  userId: ID!
  referrerId: ID
  depth: Int!
  createdAt: Time!
  user: User!
  referrer: User
}

type ReferralSnapshot {
  userId: ID!
  directCount: Int!
  totalDownlineCount: Int!
  totalVolumeUsd: Float!
  totalRewardsDistributed: Float!
  user: User!
}

enum WalletType {
  EMBEDDED
  MANAGED
}

input CreateUserInput {
  email: String!
  invitationCode: String
  referrerCode: String
}

input CreateUserWalletInput {
  userId: ID!
  chain: String!
  name: String
  walletAddress: String!
  walletId: ID
  walletAccountId: ID
  walletType: WalletType!
}

input LoginInput {
  email: String!
  walletAddress: String
  signature: String
  referrerInvitationCode: String  # The invitation code of who referred this user
}

type LoginResponse {
  user: User!
  token: String!
  isFirstLogin: Boolean!
  message: String!
}

type CreateUserResponse {
  user: User!
  success: Boolean!
  message: String!
}

type CreateUserWalletResponse {
  wallet: UserWallet!
  success: Boolean!
  message: String!
}

type Query {
  # Get user by ID
  user(id: ID!): User

  # Get user by email
  userByEmail(email: String!): User

  # Get user wallets
  userWallets(userId: ID!): [UserWallet!]!

  # Get referral information
  referralInfo(userId: ID!): Referral

  # Get referral snapshot
  referralSnapshot(userId: ID!): ReferralSnapshot

  # Get all downlines (recursive)
  downlines(userId: ID!): [Referral!]!

  # Get user's invitation code
  getUserInvitationCode(userId: ID!): String
}

input UpdateInvitationCodeInput {
  userId: ID!
  invitationCode: String!
}

type UpdateInvitationCodeResponse {
  user: User!
  success: Boolean!
  message: String!
}

type GenerateInvitationCodeResponse {
  user: User!
  success: Boolean!
  message: String!
}

type Mutation {
  # Create or login user
  login(input: LoginInput!): LoginResponse!

  # Create new user
  createUser(input: CreateUserInput!): CreateUserResponse!

  # Create user wallet
  createUserWallet(input: CreateUserWalletInput!): CreateUserWalletResponse!

  # Update user first login status
  updateFirstLoginStatus(userId: ID!): User!

  # Update wallet export status
  updateWalletExportStatus(userId: ID!): User!

  # Generate invitation code for user
  generateInvitationCode(userId: ID!): GenerateInvitationCodeResponse!

  # Update user's invitation code
  updateInvitationCode(input: UpdateInvitationCodeInput!): UpdateInvitationCodeResponse!
}

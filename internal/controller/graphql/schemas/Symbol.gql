enum SymbolConditionEnum {
    volume
    gainer
    loser
    trend
    marketCap
    openInterest
    category
}

enum OHLCIntervalEnum {
    ONE_DAY
    ONE_HOUR
    ONE_MONTH
    ONE_MINUTE
    ONE_WEEK
    TWO_HOURS
    THREE_MINUTES
    THREE_DAYS
    THREE_MONTHS
    FOUR_HOURS
    FIVE_MINUTES
    EIGHT_HOURS
    TWELVE_HOURS
    FIFTEEN_MINUTES
    THIRTY_MINUTES
}

input OHLCRequest {
    symbol: String!
    interval: OHLCIntervalEnum!    # e.g., "1m", "5m", etc.
    timestamp: Int!
    isForward: Boolean!
    limit: Int!
}

type OHLC {
    timestamp: Int!
    open: Float!
    close: Float!
    high: Float!
    low: Float!
    volume: Float!
}

type OHLCResponse {
    symbol: String!
    data: [OHLC!]!
}

input SymbolDetailRequest {
    symbol: String!
}

input UserSymbolPreferenceRequest {
    symbol: String!
}

input SymbolListRequest {
    condition: SymbolConditionEnum!
    category: String
}

type Symbol {
    symbol: String!
    maxLeverage: Int!
    marketCap: Float!
    volume: Float!
    changPxPercent: Float!
    openInterest: Float!
    currentPrice: Float!
}

type SymbolDetailResponse {
    symbol: String!
    maxLeverage: Int!
    onlyIsolated: Boolean!
    sizeDecimals: Int!
    marginTableID: Int!
    marketCap: Float!
    high: Float!
    low: Float!
    volume: Float!
    changPxPercent: Float!
    currentPrice: Float!
}

type SymbolListResponse {
    list: [Symbol]!
}

input UpsertFavoriteSymbolRequest {
    symbol: [String!]!
    isFavorite: Boolean!
}

type UpsertFavoriteSymbolResponse {
    error: String
    status: String!
}

type FavoriteSymbolsResponse {
    list: [Symbol]!
}

type CategoryResponse {
    categories: [String]!
}

type UserSymbolPreferenceResponse {
    isFavorite: Boolean!
    leverage: Int!
    isCross: Boolean!
    tpslUnit: String!
    orderUnitInBase: Boolean!
}

input UpdateUserSymbolPreferenceRequest {
    leverage: Int
    isCross: Boolean
    orderUnitInBase: Boolean
    tpslUnit: String
    symbol: String!
}

input PopularSymbolRequest {
    number: Int!
}

input SearchSymbolRequest {
    filter: String!
}

type PopularSymbolResponse {
    list: [Symbol]!
}

type SearchSymbolResponse {
    list: [Symbol]!
}

type NewSymbolResponse {
    list: [String]!
}

enum  NotificationTypeEnum {
    priceRise
    priceFell
    percent24hIncrease
    percent24hDecline
}

input CreateAlertSymbolSettingRequest {
    symbol: String!
    type: NotificationTypeEnum!
    value: Float!
    isReminderOnce: Boolean!
    note: String
}

type CreateAlertSymbolSettingResponse {
    error: String
    status: String!
    settingId: String!
}

input UpdateAlertSymbolSettingRequest {
    settingId: String!
    isActivated: Boolean!
}

type UpdateAlertSymbolSettingResponse {
    error: String
    status: String!
}

type AlertSymbolSetting {
    settingId: String!
    symbol: String!
    type: NotificationTypeEnum!
    triggerValue: Float!
    isReminderOnce: Boolean!
    isActivated: Boolean!
}

type GetAlertSymbolSettingResponse {
    settings: [AlertSymbolSetting]!
}

input DeleteAlertSymbolSettingRequest {
    settingIds: [String!]!
}

type DeleteAlertSymbolSettingResponse {
    error: String
    status: String!
}

type Signal {
    username: String!
    uuid: String!
    signalType: String!
    signalName: String!
    signalPnl: Float!
    initialCapital: Float!
    latestAssets: Float!
    runningStatus: String!
    consecutiveWins: Int!
    subscribers: Int!
    operationDirection: String!
    underlyingAsset: String!
    monthlyReturnRate: Float!
    monthlyAlpha: Float!
    annualWinRate: Float!
    cumulativeIncome: Float!
    sharpeRatio: Float!
    threeYield: Float!
    sevenYield: Float!
    maxDrawdown7Days: Float!
    runningTime: Float!
    historicalWinRate: Float!
    profitLossCount: Float!
    profitLossRatio: Float!
    confidence: String!
    evaluationStatus: String!
    review: String!
    userId: Int!
    createdDate: String!
    updatedDate: String!
}
type SignalResponse {
    total: Int!
    signals:  [Signal]!
}

input SignalRequest {
    limit: Int
    skip: Int
    user: String
}

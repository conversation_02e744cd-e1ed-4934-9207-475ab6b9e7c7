input UserOpenOrderRequest {
    lastID: String!
    limit: Int!
}

type OpenOrder {
    time: Int!
    type: String!
    symbol: String!
    direction: String!
    size: Float!
    originSize: Float!
    orderPx: Float!
    triggerPx: Float!
    reduceOnly: Boolean!
}

type UserOpenOrderResponse {
    openOrders: [OpenOrder]!
}

type Leverage {
    type: String!
    value: Int!
}

type Position {
    symbol: String!
    size: Float!
    leverage: Leverage!
    side: Int!
    entryPx: Float!
    fundingFee: Float!
}

type UserPositionResponse {
    rawUSD: Float!
    positions: [Position]!
}

input StoreTxInformationRequest {
    txType: String!
    symbol: String!
    isBuy: Boolean!
    price: Float!
    size: Float!
    reduceOnly: Boolean!
    orderType: String!
    nonce: Int! # Time milli sec
    vaultAddress: String
    expiresAfter: Int
    oid: String!
    avgPx: Float
    direction: String!
    status: String!
}

type StoreTxInformationResponse {
    error: String
    status: String!
}

type TradeHistory {
    symbol: String!
    time: Int!
    pnl: Float!
    pnlPercent: Float!
    dir: String!
    hash: String!
    oid: Int!
    px: Float!
    startPosition: String!
    sz: Float!
    fee: Float!
    feeToken: String!
    tid: Int!
}

type UserTradeHistoryResponse {
    histories: [TradeHistory!]!
}

type UserPrevDayBalanceResponse {
    Balance: Float!
}

input UserPositionRequest {
    walletAddress: String!
}

input UserTradeHistoryRequest {
    walletAddress: String!
}

input UserPrevDayBalanceRequest {
    walletAddress: String!
}
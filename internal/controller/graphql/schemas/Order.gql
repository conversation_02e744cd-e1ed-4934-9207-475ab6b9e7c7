input UserOrderStatusRequest {
  lastID: String!
  limit: Int!
}


type UserOrderStatus {
  oid: Int
  createdAt: String
  time: String
  user: String
  status: String
  userId: String
  cloid: String
  coin: String
  side: String
  limitPx: String
  sz: String
  timestamp: Int
  triggerCondition: String
  isTrigger: Boolean
  triggerPx: String
  children: String
  isPositionTpsl: Boolean
  reduceOnly: Boolean
  orderType: String
  origSz: String
  tif: String
}

type UserOrderStatusResponse {
  statuses: [UserOrderStatus]!
}

type UserMiscEventResponse {
  events: [UserMiscEvent]!
}

input UserMiscEventRequest {
  lastID: String!
  limit: Int!
}

type UserMiscEvent {
  time: String
  hash: String
  users: String
  cDepositUser: String
  cDepositAmount: String
  cWithdrawalUser: String
  cWithdrawalAmount: String
  cWithdrawalIsFinalized: Boolean
  type: String
  usdc: String
  toPerp: Boolean
  token: String
  amount: String
  usdcValue: String
  user: String
  userId: String
  destination: String
  fee: String
  nativeTokenFee: String
  nonce: Int
}

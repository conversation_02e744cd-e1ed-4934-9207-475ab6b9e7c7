package graphql

import (
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// Convert model.User to gql_model.User
func ModelUserToGQL(user *model.User) *gql_model.User {
	if user == nil {
		return nil
	}

	gqlUser := &gql_model.User{
		ID:               user.ID.String(),
		Email:            &user.Email,
		IsFirstLogin:     user.IsFirstLogin,
		IsExportedWallet: user.IsExportedWallet,
		InvitationCode:   user.InvitationCode,
		CreatedAt:        user.CreatedAt,
		UpdatedAt:        user.UpdatedAt,
	}

	// Convert wallets
	for _, wallet := range user.Wallets {
		gqlUser.Wallets = append(gqlUser.Wallets, ModelUserWalletToGQL(&wallet))
	}

	// Convert referral
	if user.Referral != nil {
		gqlUser.Referral = ModelReferralToGQL(user.Referral)
	}

	// Convert referral snapshot
	if user.ReferralSnapshot != nil {
		gqlUser.ReferralSnapshot = ModelReferralSnapshotToGQL(user.ReferralSnapshot)
	}

	// Convert referrals (users referred by this user)
	for _, referral := range user.Referrals {
		gqlUser.Referrals = append(gqlUser.Referrals, ModelReferralToGQL(&referral))
	}

	return gqlUser
}

// Convert model.UserWallet to gql_model.UserWallet
func ModelUserWalletToGQL(wallet *model.UserWallet) *gql_model.UserWallet {
	if wallet == nil {
		return nil
	}

	gqlWallet := &gql_model.UserWallet{
		ID:            wallet.ID.String(),
		UserID:        wallet.UserID.String(),
		Chain:         wallet.Chain,
		Name:          &wallet.Name,
		WalletAddress: wallet.WalletAddress,
		CreatedAt:     wallet.CreatedAt,
		UpdatedAt:     wallet.UpdatedAt,
		User:          ModelUserToGQL(&wallet.User),
	}

	if wallet.WalletID != nil {
		walletIDStr := wallet.WalletID.String()
		gqlWallet.WalletID = &walletIDStr
	}

	if wallet.WalletAccountID != nil {
		walletAccountIDStr := wallet.WalletAccountID.String()
		gqlWallet.WalletAccountID = &walletAccountIDStr
	}

	if wallet.WalletType != "" {
		switch wallet.WalletType {
		case "embedded":
			walletType := gql_model.WalletTypeEmbedded
			gqlWallet.WalletType = &walletType
		case "managed":
			walletType := gql_model.WalletTypeManaged
			gqlWallet.WalletType = &walletType
		}
	}

	return gqlWallet
}

// Convert model.Referral to gql_model.Referral
func ModelReferralToGQL(referral *model.Referral) *gql_model.Referral {
	if referral == nil {
		return nil
	}

	gqlReferral := &gql_model.Referral{
		ID:        int(referral.ID),
		UserID:    referral.UserID.String(),
		Depth:     referral.Depth,
		CreatedAt: referral.CreatedAt,
		User:      ModelUserToGQL(&referral.User),
	}

	if referral.ReferrerID != nil {
		referrerIDStr := referral.ReferrerID.String()
		gqlReferral.ReferrerID = &referrerIDStr
	}

	if referral.Referrer != nil {
		gqlReferral.Referrer = ModelUserToGQL(referral.Referrer)
	}

	return gqlReferral
}

// Convert model.ReferralSnapshot to gql_model.ReferralSnapshot
func ModelReferralSnapshotToGQL(snapshot *model.ReferralSnapshot) *gql_model.ReferralSnapshot {
	if snapshot == nil {
		return nil
	}

	totalVolumeUsd, _ := snapshot.TotalVolumeUSD.Float64()
	totalRewardsDistributed, _ := snapshot.TotalRewardsDistributed.Float64()

	return &gql_model.ReferralSnapshot{
		UserID:                  snapshot.UserID.String(),
		DirectCount:             snapshot.DirectCount,
		TotalDownlineCount:      snapshot.TotalDownlineCount,
		TotalVolumeUsd:          totalVolumeUsd,
		TotalRewardsDistributed: totalRewardsDistributed,
		User:                    ModelUserToGQL(&snapshot.User),
	}
}

// Convert gql_model.WalletType to string
func GQLWalletTypeToString(walletType gql_model.WalletType) string {
	switch walletType {
	case gql_model.WalletTypeEmbedded:
		return "embedded"
	case gql_model.WalletTypeManaged:
		return "managed"
	default:
		return ""
	}
}

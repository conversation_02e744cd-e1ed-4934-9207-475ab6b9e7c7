package orderStatus

import (
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/response"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service"
	"time"
)

type OrderStatusService struct {
	orderStatusRepo repo.OrderStatusRepo
}

func NewOrderStatusService(orderStatusRepo repo.OrderStatusRepo) service.OrderStatusI {
	return &OrderStatusService{
		orderStatusRepo: orderStatusRepo,
	}
}

func (s *OrderStatusService) GetOrderStatus(req request.ReqGetUserOrderStatus) (*response.RespGetUserOrderStatus, error) {
	orders, err := s.orderStatusRepo.GetUserOrderStatus(req.UserID, req.LastID, req.Limit)
	if err != nil {
		return nil, err
	}

	respOpenOrders := make([]response.OrderStatus, 0)
	for _, order := range orders {
		respOpenOrders = append(respOpenOrders, response.OrderStatus{
			Oid:              order.Oid,
			CreatedAt:        time.Time{},
			Time:             "",
			User:             "",
			Status:           "",
			UserId:           nil,
			Cloid:            "",
			Coin:             "",
			Side:             "",
			LimitPx:          "",
			Sz:               "",
			Timestamp:        0,
			TriggerCondition: "",
			IsTrigger:        false,
			TriggerPx:        "",
			Children:         "",
			IsPositionTpsl:   false,
			ReduceOnly:       false,
			OrderType:        "",
			OrigSz:           "",
			Tif:              "",
		})
	}

	return &response.RespGetUserOrderStatus{}, nil
}

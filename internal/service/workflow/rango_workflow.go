package workflow

import (
	"context"
	"fmt"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo"
	repoSwap "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/swap"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/rango"
	"time"

	"go.temporal.io/sdk/client"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/worker"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
)

const queue = "rango_queue"

type RangoParams struct {
	RequestId string
	TxHash    string
	Step      int64
}
type RangoWorker struct {
	c                client.Client
	repo             repo.SwapRepo
	worker           worker.Worker
	namespace, queue string
}

// NewRangoWorker creates a new instance of Rango worker
func NewRangoWorker() *RangoWorker {
	if global.GVA_CONFIG.Temporal.HostPort == "" {
		global.GVA_LOG.Error("workflow HostPort not configured")
	}
	c, err := client.Dial(client.Options{
		HostPort:  global.GVA_CONFIG.Temporal.HostPort,
		Namespace: global.GVA_CONFIG.Temporal.Namespace,
	})
	if err != nil {
		global.GVA_LOG.Error("Unable to create RangoWorker client", zap.Error(err))
	}
	w := worker.New(c, queue, worker.Options{})
	s := RangoWorker{
		c:         c,
		namespace: global.GVA_CONFIG.Temporal.Namespace,
		queue:     queue,
		repo:      repoSwap.Swap,
		worker:    w,
	}
	return &s
}

// RegisterWorker registers the worker with temporal to implement specific Rango logic
func (s *RangoWorker) RegisterWorker() {
	s.worker.RegisterActivity(s.CheckStatus)
	s.worker.RegisterWorkflow(s.ExecuteRangoWorkflow)
}
func (s *RangoWorker) RunWorker() error {
	return s.worker.Run(worker.InterruptCh())
}

// ExecuteRangoWorkflow implements the Rango workflow logic
func (s *RangoWorker) ExecuteRangoWorkflow(ctx workflow.Context, params RangoParams) error {
	logger := workflow.GetLogger(ctx)

	logger.Info("Starting Rango workflow", zap.Any("params", params))

	// Set workflow timeout and retry policy
	ctx = workflow.WithActivityOptions(ctx, workflow.ActivityOptions{
		StartToCloseTimeout: time.Hour * 1,
		RetryPolicy: &temporal.RetryPolicy{ //最长重试总等待时间：约 1 小时 17 分钟 36 秒
			InitialInterval:    time.Second,     // 初始重试间隔 1 秒
			BackoffCoefficient: 5.0,             // 每次间隔 * 5
			MaximumInterval:    time.Minute * 3, // 单次最大间隔：3 分钟
			MaximumAttempts:    30,              // 最多尝试 30 次（含第一次）
		},
	})
	// Execute Rango activity
	var result error
	err := workflow.ExecuteActivity(ctx, s.CheckStatus, params).Get(ctx, &result)
	if err != nil {
		logger.Error("Rango workflow failed", zap.Error(err))
		return err
	}
	return nil
}
func (s *RangoWorker) CheckStatus(ctx context.Context, params RangoParams) error {
	history, err := s.repo.GetHistoryById(params.RequestId)
	if err != nil {
		return fmt.Errorf("failed to get history: %w", err)
	}
	if history == nil {
		return fmt.Errorf("history is nil")
	}
	resp, err := rango.CheckStatus(params.RequestId, params.TxHash, params.Step)
	if err != nil {
		return err
	}
	err = s.repo.CacheStatus(params.RequestId, params.Step, resp.Status)
	if err != nil {
		return err
	}
	if resp.Error != "" {
		return fmt.Errorf(resp.Error)
	}
	if resp.Status != "success" {
		return fmt.Errorf("retry workflow for requestId: %s, step: %d status:%s", params.RequestId, params.Step, resp.Status)
	}
	return nil
}

func (s *RangoWorker) StartRangoWorkflow(requestId, txHash string, step int64) error {
	_, err := s.c.ExecuteWorkflow(context.Background(), client.StartWorkflowOptions{
		ID:        fmt.Sprintf("rango-workflow-%s-%d", requestId, step),
		TaskQueue: queue,
	}, "ExecuteRangoWorkflow", RangoParams{
		RequestId: requestId,
		TxHash:    txHash,
		Step:      step,
	})
	return err
}

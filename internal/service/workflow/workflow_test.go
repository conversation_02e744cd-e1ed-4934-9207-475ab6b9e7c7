package workflow_test

import (
	"context"
	"fmt"
	"github.com/stretchr/testify/assert"
	clientT "go.temporal.io/sdk/client"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/worker"
	"go.temporal.io/sdk/workflow"
	"testing"
	"time"
)

func TestExecuteWorkflow(t *testing.T) {
	client, err := clientT.Dial(clientT.Options{
		HostPort:  "127.0.0.1:7233",
		Namespace: "dex",
	})
	if err != nil {
		t.Fatal(err)
	}
	defer client.Close()

	// 2. worker
	w := worker.New(client, "my_task_queue", worker.Options{})
	w.RegisterActivity(ActivityOne)
	w.RegisterActivity(ActivityTwo)
	w.RegisterWorkflow(MyWorkflow)

	err = w.Run(worker.InterruptCh())
	if err != nil {
		t.Errorf("Worker failed: %v", err)
	}
	fmt.Println("TestExecuteWorkflow Over!!!")
}

func TestExecuteWorkflowActivity(t *testing.T) {

	client, err := clientT.Dial(clientT.Options{
		HostPort:  "127.0.0.1:7233",
		Namespace: "dex",
	})
	if err != nil {
		t.Fatal(err)
	}
	defer client.Close()

	workflowOptions := clientT.StartWorkflowOptions{
		ID:        fmt.Sprintf("test-workflow-%v", time.Now().UnixNano()),
		TaskQueue: "my_task_queue",
	}

	we, err := client.ExecuteWorkflow(context.Background(), workflowOptions, "MyWorkflow")
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("Started workflow: %v", we.GetID())

	var result string
	err = we.Get(context.Background(), &result)
	if err != nil {
		t.Fatal(err)
	}

	expected := "Hello from ActivityOne and Hello from ActivityTwo"
	assert.Equal(t, expected, result)
	t.Logf("Temporal result: %v", result)
}

func ActivityOne(ctx context.Context) (string, error) {
	fmt.Println("ActivityOne")
	return "Hello from ActivityOne", nil
}

func ActivityTwo(ctx context.Context, input string) (string, error) {
	fmt.Println("ActivityTwo")
	return input + " and Hello from ActivityTwo", nil
}

func MyWorkflow(ctx workflow.Context) (string, error) {
	fmt.Println("MyWorkflow")
	activityOptions := workflow.ActivityOptions{
		StartToCloseTimeout:    time.Minute, // Activity execution timeout time
		ScheduleToCloseTimeout: time.Minute, // The activity scheduling timeout period
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Second,
			BackoffCoefficient: 2.0,
			MaximumInterval:    time.Minute,
			MaximumAttempts:    3,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, activityOptions)

	var resultOne string
	err := workflow.ExecuteActivity(ctx, ActivityOne).Get(ctx, &resultOne)
	if err != nil {
		return "", err
	}
	fmt.Println("resultOne xxx", resultOne)
	var resultTwo string
	err = workflow.ExecuteActivity(ctx, ActivityTwo, resultOne).Get(ctx, &resultTwo)
	if err != nil {
		return "", err
	}
	fmt.Println("resultTwo xxx", resultTwo)
	return resultTwo, nil
}

package symbol

import (
	"fmt"
	"github.com/google/uuid"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/response"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"time"
)

func (s *SymbolService) GetAlertSymbolsSetting(req request.ReqGetAlertSymbolsSetting) (*response.RespGetAlertSymbolsSetting, error) {
	resp, err := s.symbolRepo.GetAlertSymbolsSetting(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get notification symbols setting: %v", err)
	}

	return &response.RespGetAlertSymbolsSetting{
		Settings: s.AssignSymbolSetting(resp),
	}, nil
}

func (s *SymbolService) AssignSymbolSetting(resp []*model.UserNotificationSetting) []response.AlertSymbolSetting {
	settings := make([]response.AlertSymbolSetting, 0)
	for _, setting := range resp {
		settings = append(settings, response.AlertSymbolSetting{
			SettingID:      setting.ID.String(),
			Symbol:         setting.Symbol,
			Type:           setting.Type,
			TriggerValue:   setting.Value,
			IsReminderOnce: setting.IsReminderOnce,
			IsActive:       setting.IsActive,
		})
	}

	return settings
}

func (s *SymbolService) CreateAlertSymbolSetting(req request.ReqCreateAlertSymbolSetting) (string, error) {
	settinngID, err := uuid.NewV7()
	if err != nil {
		return "", err
	}

	err = s.symbolRepo.InsertAlertSymbolSetting(model.UserNotificationSetting{
		ID:             settinngID,
		UserID:         req.UserID,
		Symbol:         req.Symbol,
		Type:           model.NotificationTypeMap[req.Type],
		Direction:      model.NotificationDirectionMap[req.Type],
		Value:          req.Value,
		IsReminderOnce: req.IsReminderOnce,
		IsDeleted:      false,
		Note:           req.Note,
		CreateAt:       time.Now(),
		UpdateAt:       time.Now(),
		LastNotifiedAt: time.Date(1970, 1, 1, 0, 0, 0, 0, time.UTC), // Fixed time for first time notify
		IsActive:       req.IsActivated,
	})
	if err != nil {
		return "", err
	}

	return settinngID.String(), nil
}

func (s *SymbolService) UpdateAlertSymbolSetting(req request.ReqUpdateAlertSymbolSetting) error {
	err := s.symbolRepo.UpdateAlertSymbolSetting(model.UserNotificationSetting{
		ID:       req.SettingID,
		UserID:   req.UserID,
		IsActive: req.IsActivated,
	})
	if err != nil {
		return err
	}

	return nil
}

func (s *SymbolService) DeleteAlertSymbolSetting(req request.ReqDeleteAlertSymbolSetting) error {
	err := s.symbolRepo.DeleteAlertSymbolSetting(req.SettingsID)
	if err != nil {
		return err
	}

	return nil
}

package trade

import (
	"encoding/json"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	"github.com/gorilla/websocket"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/initializer"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/hyperliquid"
	"go.uber.org/zap"
)

type WsTradeService struct {
	ws   *initializer.HyperLiquidWebsocket
	mqtt *mqtt.Client
}

func NewWssTradeHandlerService(ws *initializer.HyperLiquidWebsocket, mqtt *mqtt.Client) *WsTradeService {
	return &WsTradeService{
		ws:   ws,
		mqtt: mqtt,
	}
}

func (s *WsTradeService) SubscribeTradeSocket() error {
	// todo: get list coin from redis or db
	coins := []string{}
	err := global.GVA_DB.Model(&model.Coin{}).
		Select("symbol").
		Find(&coins).Error

	if err != nil {
		global.GVA_LOG.Error("Failed to get coin list from database", zap.Error(err))
		return err
	}

	for _, coin := range coins {
		subscribeMsg := `{
			"method": "subscribe",
			"subscription": {
				"type": "trades",
				"coin": "` + coin + `"
			}
		}`

		if err := s.ws.WriteMessage(websocket.TextMessage, []byte(subscribeMsg)); err != nil {
			global.GVA_LOG.Error("Failed to subscribe to trade socket", zap.Error(err))
		}
	}

	return nil
}

func (s *WsTradeService) SubscribeL2BookSocket() error {
	// todo: get list coin from redis or db
	coins := []string{}
	err := global.GVA_DB.Model(&model.Coin{}).
		Select("symbol").
		Find(&coins).Error

	if err != nil {
		global.GVA_LOG.Error("Failed to get coin list from database", zap.Error(err))
		return err
	}

	for _, coin := range coins {
		subscribeMsg := `{
			"method": "subscribe",
			"subscription": {
				"type": "l2Book",
				"coin": "` + coin + `"
			}
		}`

		if err := s.ws.WriteMessage(websocket.TextMessage, []byte(subscribeMsg)); err != nil {
			global.GVA_LOG.Error("Failed to subscribe to l2book socket", zap.Error(err))
		}
	}

	return nil
}

func (s *WsTradeService) HandleTradeMessage(msg *hyperliquid.WsMessageList[hyperliquid.WsTrade]) error {
	if msg == nil {
		return nil
	}

	if msg.Data == nil {
		return nil
	}

	coin := msg.Data[0].Coin

	global.GVA_LOG.Info("Received trade message", zap.String("coin", coin))
	rawData, err := json.Marshal(msg.Data)
	if err != nil {
		global.GVA_LOG.Error("Failed to marshal trade data", zap.Error(err))
	}

	token := (*s.mqtt).Publish("public/last_trade/"+coin, 0, false, rawData)
	token.Wait()

	if token.Error() != nil {
		global.GVA_LOG.Error("MQTT publish error", zap.Error(token.Error()))
	}

	return nil
}

func (s *WsTradeService) HandleL2BookMessage(msg *hyperliquid.WsMessageObject[hyperliquid.WsBook]) error {
	if msg == nil {
		return nil
	}
	coin := msg.Data.Coin

	global.GVA_LOG.Info("Received l2book message", zap.String("coin", coin))
	rawData, err := json.Marshal(msg.Data)
	if err != nil {
		global.GVA_LOG.Error("Failed to marshal trade data", zap.Error(err))
	}

	token := (*s.mqtt).Publish("public/l2book/"+coin, 0, false, rawData)
	token.Wait()

	if token.Error() != nil {
		global.GVA_LOG.Error("MQTT publish error", zap.Error(token.Error()))
	}

	return nil
}

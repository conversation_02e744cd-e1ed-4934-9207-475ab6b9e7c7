package trade

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/cache"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/response"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/hyperliquid"
	"time"
)

type TradeService struct {
	tradingRepo repo.TradeRepo
	assetRepo   repo.AssetRepo
	symbolRepo  repo.SymbolRepo
}

func NewTradeService(tradingRepo repo.TradeRepo, assetRepo repo.AssetRepo, symbolRepo repo.SymbolRepo) service.TradeI {
	return &TradeService{
		tradingRepo: tradingRepo,
		assetRepo:   assetRepo,
		symbolRepo:  symbolRepo,
	}
}

func (s *TradeService) GetUserOpenOrder(req request.ReqGetUserOpenOrder) (*response.RespGetUserOpenOrder, error) {
	operOrders, err := s.tradingRepo.GetUserOpenOrder(req.UserID, req.LastID, req.Limit)
	if err != nil {
		return nil, err
	}

	respOpenOrders := make([]response.OpenOrder, 0)
	for _, order := range operOrders {
		respOpenOrders = append(respOpenOrders, response.OpenOrder{
			Symbol:     order.Symbol,
			Direction:  order.Direction,
			OrderPx:    order.OrderPx,
			OriginSize: order.OriginSize,
			Size:       order.Size,
			Time:       order.CreatedAt.UnixMilli(),
			ReduceOnly: order.ReduceOnly,
			Type:       order.Type,
		})
	}

	return &response.RespGetUserOpenOrder{
		OpenOrders: respOpenOrders,
	}, nil
}

func (s *TradeService) GetUserPosition(req request.ReqGetUserPosition) (*response.RespGetUserPosition, error) {
	positions, err := s.tradingRepo.GetUserPosition(req.WalletAddress)
	if err != nil {
		return nil, err
	}

	var respPosition = make([]response.Position, 0)
	for _, position := range positions {
		leverage, err := s.getUserSymbolLeverage(req.UserID, position.Symbol)
		if err != nil {
			return nil, err
		}

		respPosition = append(respPosition, response.Position{
			Symbol: position.Symbol,
			Size:   position.Size,
			Leverage: response.Leverage{
				Type:  position.MarginMode,
				Value: leverage,
			},
			FundingFee: position.FundingAllTime,
			EntryPx:    position.EntryPx,
			Side:       position.Side,
		})
	}

	userBalance, err := s.assetRepo.GetUserBalance(req.WalletAddress)
	if err != nil {
		return nil, err
	}

	return &response.RespGetUserPosition{
		RawUsd:    userBalance.RawUSD,
		Positions: respPosition,
	}, nil
}

func (s *TradeService) getUserSymbolLeverage(userID uuid.UUID, symbol string) (int, error) {
	userSymbolPreference, err := s.symbolRepo.GetUserSymbolPreference(userID, symbol)
	if err == nil && userSymbolPreference.Leverage != nil {
		return *userSymbolPreference.Leverage, nil
	}

	var (
		cacheKey   = cache.KeySymbolOverallData() + symbol
		symbolData *model.Coin
	)

	data, err := global.GVA_REDIS.Get(context.Background(), cacheKey).Result()
	if err == nil && data != "" {
		if err = json.Unmarshal([]byte(data), &symbolData); err == nil {
			return symbolData.MaxLeverage, nil
		} else {
			symbolData, err = s.symbolRepo.GetSymbolOverallData(symbol)
			if err != nil {
				return 0, err
			}
		}
	}

	return symbolData.MaxLeverage, nil
}

func (s *TradeService) StoreTxInformation(req request.ReqStoreTxInformation) (*response.RespStoreTxInformation, error) {
	if err := s.tradingRepo.StoreTxInformation(&model.Order{
		ID:         req.ID,
		Symbol:     req.Symbol,
		Direction:  req.Direction,
		OrderPx:    req.OrderPx,
		OriginSize: req.OriginSize,
		Size:       req.Size,
		Type:       req.Type,
		OID:        req.OID,
		UserID:     req.UserID,
		Status:     req.Status,
		TriggerPx:  req.TriggerPx,
		ReduceOnly: req.ReduceOnly,
		UpdateAt:   time.Now(),
	}); err != nil {
		return nil, err
	}
	return &response.RespStoreTxInformation{
		Status: "Success",
	}, nil
}

func (s *TradeService) GetUserTradeHistory(req request.ReqGetUserTradeHistory) (*response.RespGetUserTradeHistory, error) {
	tradeHistories, err := hyperliquid.GetUserTradeHistoryFromAPI(req.WalletAddress)
	if err != nil {
		return nil, fmt.Errorf("cannot get positions from hyperliquid: %w", err)
	}

	respHistories, err := s.assignTradeHistory(tradeHistories)
	if err != nil {
		return nil, fmt.Errorf("cannot assign trade history: %w", err)
	}

	return &response.RespGetUserTradeHistory{
		Histories: respHistories,
	}, nil
}

func (s *TradeService) assignTradeHistory(tradeHistories []hyperliquid.TradeHistory) ([]response.TradeHistory, error) {
	respHistories := make([]response.TradeHistory, 0)

	for _, history := range tradeHistories {
		// Todo: Add local cache for symbol perp list, check if it exist first
		var statisticData model.CoinStatistic
		cacheKey := fmt.Sprintf("%s%s", cache.KeySymbolStatisticData(), history.Coin)
		str, err := global.GVA_REDIS.Get(context.Background(), cacheKey).Result()
		if err == nil && str != "" {
			if err = json.Unmarshal([]byte(str), &statisticData); err != nil {
				return nil, fmt.Errorf("failed to unmarshal symbol detail data: %w", err)
			}
		} else {
			// Todo: If not exist in cache, get it from DB
			continue
		}

		orderPx := utils.StringToFloat(history.Px)
		respHistories = append(respHistories, response.TradeHistory{
			Symbol:        history.Coin,
			Time:          history.Time,
			Dir:           history.Dir,
			Oid:           history.Oid,
			Hash:          history.Hash,
			Px:            orderPx,
			StartPosition: history.StartPosition,
			Sz:            utils.StringToFloat(history.Sz),
			Fee:           utils.StringToFloat(history.Fee),
			FeeToken:      history.FeeToken,
			Tid:           history.Tid,
			Pnl:           statisticData.MarkPx - orderPx,
			PnlPercent:    (statisticData.MarkPx - orderPx) / orderPx * 100,
		})
	}

	return respHistories, nil
}

func (s *TradeService) GetUserPrevDayBalance(req request.ReqGetUserPrevDayBalance) (*response.RespGetUserPrevDayBalance, error) {
	userBalance, err := s.assetRepo.GetUserBalance(req.WalletAddress)
	if err != nil {
		return nil, err
	}

	return &response.RespGetUserPrevDayBalance{
		Balance: userBalance.PrevDayBalance,
	}, nil
}

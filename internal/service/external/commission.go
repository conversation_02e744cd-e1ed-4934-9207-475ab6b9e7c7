package external

import (
	"encoding/json"

	"github.com/nats-io/nats.go"
)

const (
	CommissionSubject = "commission.commit"
	PerpetualEntity   = "perpetual_transaction"
)

type TransactionDataMsg struct {
	Id     string  `json:"id"`
	Volume float64 `json:"volume"`
}

type TransactionEventMsg struct {
	Entity string             `json:"entity"`
	UserId string             `json:"user_id"`
	Data   TransactionDataMsg `json:"data"`
}

type CommissionService struct {
	natsConn *nats.JetStreamContext
}

func NewCommissionService(natsConn *nats.JetStreamContext) *CommissionService {
	return &CommissionService{
		natsConn: natsConn,
	}
}

func (c *CommissionService) SendCommissionMessage(subject string, userId string, transactionId string, volume float64) error {
	msg := &TransactionEventMsg{
		Entity: PerpetualEntity,
		UserId: userId,
		Data: TransactionDataMsg{
			Id:     transactionId,
			Volume: volume,
		},
	}

	msgBytes, err := json.Marshal(msg)
	if err != nil {
		return err
	}

	_, err = (*c.natsConn).Publish(CommissionSubject, msgBytes)

	if err != nil {
		return err
	}

	return nil
}

package miscEvents

import (
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/response"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service"
)

type MiscEventService struct {
	miscEventRepo repo.MiscEventRepo
}

func NewMiscEventService(miscEventRepo repo.MiscEventRepo) service.MiscEventI {
	return &MiscEventService{
		miscEventRepo: miscEventRepo,
	}
}

func (s *MiscEventService) GetUserMiscEvents(req request.ReqGetUserMiscEvents) (*response.RespGetUserEvents, error) {
	operOrders, err := s.miscEventRepo.GetUserMiscEvents(req.UserID, req.LastID, req.Limit)
	if err != nil {
		return nil, err
	}

	respOpenOrders := make([]response.MiscEvent, 0)
	for _, event := range operOrders {
		respOpenOrders = append(respOpenOrders, response.MiscEvent{
			Time:                   event.Time,
			Hash:                   "",
			Users:                  "",
			CDepositUser:           "",
			CDepositAmount:         "",
			CWithdrawalUser:        "",
			CWithdrawalAmount:      "",
			CWithdrawalIsFinalized: false,
			Type:                   "",
			Usdc:                   "",
			ToPerp:                 false,
			Token:                  "",
			Amount:                 "",
			UsdcValue:              "",
			User:                   "",
			UserId:                 "",
			Destination:            "",
			Fee:                    "",
			NativeTokenFee:         "",
			Nonce:                  0,
		})
	}

	return &response.RespGetUserEvents{}, nil
}

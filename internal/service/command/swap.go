package command

import (
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service"
)

type swapCommand struct {
	repo repo.SwapRepo
}

func NewSwapCommand(repo repo.SwapRepo) service.CommandI {
	return &swapCommand{
		repo: repo,
	}
}

func (c *swapCommand) UpdateRangoActiveStatus() error {
	if err := c.repo.UpdateRangoChainActiveStatus(); err != nil {
		return err
	}

	if err := c.repo.UpdateRangoTokenActiveStatus(); err != nil {
		return err
	}

	return nil
}

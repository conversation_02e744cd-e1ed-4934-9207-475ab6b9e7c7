package swap

import (
	"errors"
	"fmt"
	"github.com/redis/go-redis/v9"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/response"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/workflow"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/rango"
	"gorm.io/gorm"
	"math"
	"math/big"
	"strconv"
	"strings"
)

type Swap struct {
	repo   repo.SwapRepo
	worker *workflow.RangoWorker
}

func NewSwapService(r repo.SwapRepo) service.SwapI {
	//w := workflow.NewRangoWorker()
	return &Swap{
		repo: r,
		//worker: w,
	}
}

func (s *Swap) GetRangoMeta() ([]*model.RangoMetaChains, []*model.RangoMetaTokens, []*model.RangoMetaSwappers, error) {
	chains, err := s.repo.GetRangoChains()
	if err != nil {
		return nil, nil, nil, err
	}

	tokens, err := s.repo.GetSupportTokens()
	if err != nil {
		return nil, nil, nil, err
	}
	return chains, tokens, []*model.RangoMetaSwappers{}, nil
}

func (s *Swap) GetAllPossibleRoutes(req request.ReqGetAllPossibleRoutes) (*rango.AllPossibleRoutes, error) {
	// Check if token and chain is active
	isActive, err := s.repo.GetRangoChainActiveStatus(req.FromBlockchain)
	if err != nil {
		return nil, err
	}
	if !isActive {
		return nil, fmt.Errorf("not active chain %s", req.FromBlockchain)
	}

	isActive, err = s.repo.GetRangoTokenActiveStatus(req.FromBlockchain, req.FromSymbol, req.FromTokenAddress)
	if err != nil {
		return nil, err
	}
	if !isActive {
		return nil, fmt.Errorf("not active token with chain %s, symbol %s, address %s", req.FromBlockchain, req.FromSymbol, req.FromTokenAddress)
	}

	isActive, err = s.repo.GetRangoChainActiveStatus(req.ToBlockchain)
	if err != nil {
		return nil, err
	}
	if !isActive {
		return nil, fmt.Errorf("not active chain %s", req.ToBlockchain)
	}

	isActive, err = s.repo.GetRangoTokenActiveStatus(req.ToBlockchain, req.ToSymbol, req.ToTokenAddress)
	if err != nil {
		return nil, err
	}
	if !isActive {
		return nil, fmt.Errorf("not active token with chain %s, symbol %s, address %s", req.ToBlockchain, req.ToSymbol, req.ToTokenAddress)
	}

	affiliate, err := s.repo.GetChainByName(req.FromBlockchain)
	if err != nil {
		return nil, err
	}
	percent := "0"
	affiliates := make(map[string]string, 0)
	if affiliate != nil {
		affiliates[affiliate.Chain] = affiliate.Wallet
		percent = affiliate.Percent
	}

	resp, err := rango.GetAllPossibleRoutes(
		req.FromBlockchain, req.FromSymbol, req.FromTokenAddress, req.ToBlockchain, req.ToSymbol, req.ToTokenAddress, req.Amount, req.Slippage,
		percent, affiliates,
	)
	if err != nil {
		return nil, err
	}
	if resp.Error != "" {
		return nil, errors.New(resp.Error)
	}
	return resp, nil
}

func (s *Swap) GetBestRoute(req request.ReqGetBestRoute) (*rango.RespBestRoute, error) {
	// Check if token and chain is active
	//isActive, err := s.repo.GetRangoChainActiveStatus(req.FromBlockChain)
	//if err != nil {
	//	return nil, err
	//}
	//if !isActive {
	//	return nil, fmt.Errorf("not active chain %s", req.FromBlockChain)
	//}
	//
	//isActive, err = s.repo.GetRangoTokenActiveStatus(req.FromBlockChain, req.FromSymbol, req.FromTokenAddress)
	//if err != nil {
	//	return nil, err
	//}
	//if !isActive {
	//	return nil, fmt.Errorf("not active token with chain %s, symbol %s, address %s", req.FromBlockChain, req.FromSymbol, req.FromTokenAddress)
	//}
	//
	//isActive, err = s.repo.GetRangoChainActiveStatus(req.ToBlockChain)
	//if err != nil {
	//	return nil, err
	//}
	//if !isActive {
	//	return nil, fmt.Errorf("not active chain %s", req.ToBlockChain)
	//}
	//
	//isActive, err = s.repo.GetRangoTokenActiveStatus(req.ToBlockChain, req.ToSymbol, req.ToTokenAddress)
	//if err != nil {
	//	return nil, err
	//}
	//if !isActive {
	//	return nil, fmt.Errorf("not active token with chain %s, symbol %s, address %s", req.ToBlockChain, req.ToSymbol, req.ToTokenAddress)
	//}

	affiliate, err := s.repo.GetChainByName(req.FromBlockChain)
	if err != nil {
		return nil, err
	}
	percent := "0"
	affiliates := make(map[string]string, 0)
	if affiliate != nil {
		affiliates[affiliate.Chain] = affiliate.Wallet
		percent = affiliate.Percent
	}

	resp, err := rango.GetBestRoute(
		req.FromBlockChain,
		req.FromSymbol,
		req.FromTokenAddress,
		req.FromAddress,
		req.ToBlockChain,
		req.ToSymbol,
		req.ToTokenAddress,
		req.ToAddress,
		req.Amount,
		percent,
		affiliates,
	)
	if err != nil {
		return nil, err
	}
	if resp.Error != "" {
		return nil, errors.New(resp.Error)
	}
	return resp, nil
}
func (s *Swap) GetBestRouteV2(req request.ReqGetQuote) (*response.RespQuote, error) {
	// 查找 origin token/chain
	originToken, err := s.repo.GetRangoTokenByUUID(req.OriginId)
	if err != nil {
		return nil, fmt.Errorf("failed to get relay quote: %v", err)
	}
	originChain, err := s.repo.GetRangoChainByName(originToken.BlockChain)
	if err != nil {
		return nil, fmt.Errorf("failed to get relay chain: %v", err)
	}
	// 查找 destination token/chain
	destinationToken, err := s.repo.GetRangoTokenByUUID(req.DestinationId)
	if err != nil {
		return nil, fmt.Errorf("failed to get relay quote token: %v", err)
	}
	destinationChain, err := s.repo.GetRangoChainByName(destinationToken.BlockChain)
	if err != nil {
		return nil, fmt.Errorf("failed to get relay chain: %v", err)
	}
	amountBigInt, _ := big.NewInt(0).SetString(req.Amount, 10)
	// 处理 Amount 字段
	decimals := int(originToken.Decimals) // 假设精度为18，可以根据实际情况调整
	f := new(big.Float).SetInt(amountBigInt)
	divisor := new(big.Float).SetFloat64(math.Pow10(decimals))
	f.Quo(f, divisor)
	amountStr := f.Text('f', decimals) // 保留所有精度
	// 组装参数
	bestRouteReq := request.ReqGetBestRoute{
		FromBlockChain:   originChain.Name,
		FromSymbol:       originToken.Symbol,
		FromAddress:      req.User, // 假设 req.User 是地址
		FromTokenAddress: originToken.Address,
		ToBlockChain:     destinationChain.Name,
		ToSymbol:         destinationToken.Symbol,
		ToTokenAddress:   destinationToken.Address,
		ToAddress:        req.Recipient,
		Amount:           amountStr,
	}

	respBestRoute, err := s.GetBestRoute(bestRouteReq)
	if err != nil {
		return nil, fmt.Errorf("failed to get best route: %v", err)
	}

	_, err = s.ConfirmRoute(request.ReqConfirmRoute{
		SelectedWallets: []request.SelectedWallet{
			{
				Blockchain: originChain.Name,
				Address:    req.User,
			},
			{
				Blockchain: destinationChain.Name,
				Address:    req.Recipient,
			},
		},
		Destination: req.Recipient,
		RequestId:   respBestRoute.RequestId,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to confirm route: %v", err)
	}
	createTx, err := s.CreatedTx(request.ReqCreateTx{
		UserSettings: request.UserSettings{
			Slippage:        3,
			InfiniteApprove: false,
		},
		Validations: request.Validations{
			Balance: true,
			Fee:     true,
			Approve: true,
		},
		Step:      1, // Assuming step 1 for the first transaction
		RequestId: respBestRoute.RequestId,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to confirm route: %v", err)
	}
	if len(respBestRoute.Result.Swaps) > 1 {
		return nil, fmt.Errorf("rango not have this route")
	}
	var stepItem []response.StepItem
	for _, swap := range respBestRoute.Result.Swaps {
		chainId := originChain.ChainId
		chainId = HexToDecimalString(chainId)
		if originChain.Name == "SOLANA" { //solana
			chainId = "501424"
		}
		tx := createTx.Transaction
		stepItem = append(stepItem, response.StepItem{
			From:                   tx.From,
			To:                     tx.To,
			Data:                   tx.Data,
			Value:                  tx.Value,
			ChainId:                chainId,
			Gas:                    tx.GasLimit,
			MaxFeePerGas:           tx.MaxFeePerGas,
			MaxPriorityFeePerGas:   tx.MaxPriorityFeePerGas,
			Nonce:                  tx.Nonce,
			BlockHash:              tx.RecentBlockhash, // 如有需要可补充
			SerializedMessage:      tx.SerializedMessage,
			TransactionType:        tx.Type,
			EstimatedTimeInSeconds: swap.EstimatedTimeInSeconds,
			ValueAmountUsd:         fmt.Sprintf("%f", amountUsd(swap.To.Decimals, swap.To.UsdPrice, respBestRoute.Result.OutputAmount)),
			ValueAmountFormatted:   respBestRoute.Result.OutputAmount,
		})
	}

	return &response.RespQuote{
		Items:                 stepItem,
		RequestId:             respBestRoute.RequestId,
		OutPutAmountFormatted: respBestRoute.Result.OutputAmount,
	}, nil
}
func HexToDecimalString(hexStr string) string {
	s := strings.TrimPrefix(hexStr, "0x")
	if s == "" {
		return ""
	}
	v, err := strconv.ParseInt(s, 16, 64)
	if err != nil {
		return hexStr
	}
	return strconv.FormatInt(v, 10)
}

// 计算金额对应的美元价值
func amountUsd(decimals int64,
	usdPrice float64,
	amount string) float64 {
	// 将 Amount 转为 *big.Float
	amountInt, ok := new(big.Int).SetString(amount, 10)
	if !ok {
		return 0
	}
	amountFloat := new(big.Float).SetInt(amountInt)
	// 处理精度
	_decimals := new(big.Float).SetFloat64(float64Pow(10, decimals))
	realAmount := new(big.Float).Quo(amountFloat, _decimals)
	// 乘以美元价格
	_usdPrice := new(big.Float).SetFloat64(usdPrice)
	usdValue := new(big.Float).Mul(realAmount, _usdPrice)
	// 转 float64 返回
	result, _ := usdValue.Float64()
	return result
}

// 辅助函数：float64 的幂
func float64Pow(a float64, b int64) float64 {
	result := 1.0
	for i := int64(0); i < b; i++ {
		result *= a
	}
	return result
}
func (s *Swap) Histories(blockchain, address string) ([]*model.RangoHistory, int64, error) {
	return s.repo.Histories(blockchain, address)
}

func (s *Swap) ConfirmRoute(req request.ReqConfirmRoute) (*response.RespConfirmRoute, error) {
	// Convert selected wallets to map
	selectedWallets := make(map[string]string)
	for _, wallet := range req.SelectedWallets {
		selectedWallets[wallet.Blockchain] = wallet.Address
	}

	// Call Rango API to confirm route
	result, err := rango.ConfirmRoute(selectedWallets, req.Destination, req.RequestId)
	if err != nil {
		return nil, fmt.Errorf("confirm route failed: %v", err)
	}

	err = s.repo.CacheConfirm(result.Result)
	if err != nil {
		return nil, fmt.Errorf("failed to cached confirm route: %v", err)
	}

	// Convert response
	resp := &response.RespConfirmRoute{
		RequestId: result.Result.RequestId,
		Status:    "success",
	}

	if !result.Ok {
		if result.Error != "" {
			resp.Error = result.Error
		} else {
			resp.Error = "Unknown error"
		}
	}
	if resp.Error != "" {
		return nil, errors.New(resp.Error)
	}
	return resp, nil
}

func (s *Swap) CheckApproval(req request.ReqCheckApproval) (*rango.RespCheckApproval, error) {
	// Call Rango API to check approval status
	result, err := rango.CheckApproval(req.RequestId, req.TxHash)
	if err != nil {
		return nil, fmt.Errorf("check approval failed: %v", err)
	}
	return result, nil
}

func (s *Swap) CreateOrUpdateTx(req request.ReqCreateTx) (*rango.RespCreateTx, error) {
	if req.UserSettings.Slippage == 0 {
		req.UserSettings.Slippage = 0.5 //set default slippage
	}
	// Convert selected wallets to map
	userSettings := rango.UserSettings{
		Slippage:        req.UserSettings.Slippage,
		InfiniteApprove: req.UserSettings.InfiniteApprove,
	}
	validations := rango.Validations{
		Balance: req.Validations.Balance,
		Fee:     req.Validations.Fee,
		Approve: req.Validations.Approve,
	}
	// Call Rango API to create transaction
	result, err := rango.CreateTx(
		req.RequestId,
		userSettings,
		validations,
		req.Step,
	)
	if err != nil {
		return nil, fmt.Errorf("create transaction CreateTx failed: %v", err)
	}
	history, err := s.repo.GetHistoryById(req.RequestId)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("create transaction GetByRequestId failed: %v", err)
	}

	if history != nil {
		step := req.Step
		step--
		if step > int64(len(history.Swaps)) {
			return nil, fmt.Errorf("create transaction GetByRequestId failed: step out of range")
		}
		swapRepo := history.Swaps[step]
		callData, hash, err := s.TxData(result.Transaction)
		if err != nil {
			return nil, fmt.Errorf("create transaction AssignCallData failed: %v", err)
		}
		swapRepo.CallData = callData
		swapRepo.TxHash = hash
		err = s.repo.UpdateSwapStep(history, swapRepo)
		if err != nil {
			return nil, fmt.Errorf("create transaction RangoUpdateHistory failed: %v", err)
		}
		return result, nil
	}

	confirmedData, err := s.repo.LoadConfirmed(req.RequestId)
	if err != nil {
		return nil, fmt.Errorf("create transaction LoadConfirmed failed: %v", err)
	}
	global.GVA_LOG.Info(fmt.Sprintf("[CreateOrUpdateTx] result.Transaction  %v ", result.Transaction))

	if result.Transaction != nil {
		if result.Transaction.GasPrice == "" || result.Transaction.GasPrice == "0x0" {
			for _, fee := range confirmedData.Result.Swaps[0].Fee {
				global.GVA_LOG.Info(fmt.Sprintf("[CreateOrUpdateTx]get gas price from fee %s gasPrice %s", fee.Name, fee.Meta.GasPrice))
				if fee.Name == "Network Fee" {
					result.Transaction.GasPrice = fee.Meta.GasPrice
					break
				}
			}
		}
	}

	historyRepo, err := s.HistoryAssign(result, confirmedData, req.Step)
	if err != nil {
		return nil, fmt.Errorf("create transaction HistoryAssign failed: %v", err)
	}
	if historyRepo == nil {
		return nil, fmt.Errorf("create transaction GetHistoryRepo failed: %v", err)
	}

	err = s.repo.CreateOrUpdateHistory(historyRepo)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (s *Swap) SendCallBack(req request.ReqCallBack) (*response.RespCallBack, error) {
	history, err := s.repo.GetHistoryById(req.RequestId)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("create transaction GetByRequestId failed: %v", err)
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return &response.RespCallBack{Success: "fail"},
			fmt.Errorf("not found the request id route in table %v", err)
	}
	step := req.Step
	step--
	if step >= int64(len(history.Swaps)) {
		return nil, fmt.Errorf("create transaction GetByRequestId failed: step out of range")
	}
	history.Step = req.Step
	swapRepo := history.Swaps[step]
	swapRepo.TxHash = req.TxHash
	err = s.repo.UpdateSwapStep(history, swapRepo)
	if err != nil {
		return nil, fmt.Errorf("create transaction RangoUpdateHistory failed: %v", err)
	}
	return &response.RespCallBack{Success: "success"}, nil
}

func (s *Swap) CheckSwapStatus(req request.ReqCheckStatus) (*rango.RespCheckStatus, error) {
	history, err := s.repo.GetHistoryById(req.RequestId)
	if err != nil {
		return nil, fmt.Errorf("failed to get history: %w", err)
	}
	if history == nil {
		return nil, errors.New("history is nil")
	}
	if req.Step > int64(len(history.Swaps)) || req.Step <= 0 {
		return nil, fmt.Errorf("create transaction GetByRequestId failed: step out of range")
	}
	if history.Status == "success" {
		return &rango.RespCheckStatus{
			Status: "success",
		}, nil
	}
	if history.Step == req.Step {
		return &rango.RespCheckStatus{
			Status: "success",
		}, nil
	}
	loadStatus, err := s.repo.LoadStatus(req.RequestId, req.Step)
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, fmt.Errorf("failed to load status: %w", err)
	}
	if loadStatus == "" {
		resp, err := rango.CheckStatus(req.RequestId, req.TxHash, req.Step)
		if err != nil {
			return nil, err
		}
		loadStatus = resp.Status
		if resp.Error != "" {
			return nil, errors.New(resp.Error)
		}
		if resp.Status != "success" {
			err = s.worker.StartRangoWorkflow(req.RequestId, req.TxHash, req.Step)
			if err != nil {
				return nil, fmt.Errorf("failed to start Rango workflow: %w", err)
			}
		}
	}
	if loadStatus == "success" {
		if req.Step == int64(len(history.Swaps)) {
			history.Status = "success"
		}
		history.Step = req.Step
		history.Swaps[req.Step-1].TxHash = req.TxHash
		err = s.repo.CreateOrUpdateHistory(history)
		if err != nil {
			return nil, fmt.Errorf("failed to update history status: %w", err)
		}
	}

	return &rango.RespCheckStatus{
		Status: loadStatus,
	}, nil
}

func (s *Swap) CreatedTx(req request.ReqCreateTx) (*rango.RespCreateTx, error) {
	// Convert selected wallets to map
	userSettings := rango.UserSettings{
		Slippage:        req.UserSettings.Slippage,
		InfiniteApprove: req.UserSettings.InfiniteApprove,
	}
	validations := rango.Validations{
		Balance: req.Validations.Balance,
		Fee:     req.Validations.Fee,
		Approve: req.Validations.Approve,
	}
	// Call Rango API to create transaction
	result, err := rango.CreateTx(
		req.RequestId,
		userSettings,
		validations,
		req.Step,
	)
	if err != nil {
		return nil, fmt.Errorf("create transaction CreateTx failed: %v", err)
	}

	return result, nil
}

package swap

import (
	"errors"
	"fmt"
	"math/big"
	"strconv"

	"github.com/gagliardetto/solana-go"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/response"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/chainHelper"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/rango"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/relay"
)

func (s *Swap) GetRelayMeta() (map[string][]*model.RelayMetaTokens, []*model.RelayMetaChains, error) {
	tokens, err := s.repo.GetRelayTokens()
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get relay meta tokens: %v", err)
	}
	chains, err := s.repo.GetRelayChains()
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get relay meta chains: %v", err)
	}
	var tokenMap = make(map[string][]*model.RelayMetaTokens, len(tokens))
	for _, token := range tokens {
		key := token.ChainIdHex
		tokenMap[key] = append(tokenMap[key], token)
	}
	return tokenMap, chains, nil
}

// GetRelayQuote 通过 uuid 查询 token/chain 信息后组装 relay.QuoteRequest
func (s *Swap) GetRelayQuote(req request.ReqGetQuote) (*response.RespQuote, error) {
	// 查找 origin token/chain
	originToken, err := s.repo.GetRelayTokenByUUID(req.OriginId)
	if err != nil {
		return nil, fmt.Errorf("failed to get relay quote: %v", err)
	}
	originChain, err := s.repo.GetRelayChainById(originToken.ChainId)
	if err != nil {
		return nil, fmt.Errorf("failed to get relay chain: %v", err)
	}
	// 查找 destination token/chain
	destinationToken, err := s.repo.GetRelayTokenByUUID(req.DestinationId)
	if err != nil {
		return nil, fmt.Errorf("failed to get relay quote token: %v", err)
	}
	destinationChain, err := s.repo.GetRelayChainById(destinationToken.ChainId)
	if err != nil {
		return nil, fmt.Errorf("failed to get relay chain: %v", err)
	}
	// 组装参数
	originChainIdInt, err := strconv.ParseInt(originChain.ChainId, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("failed to parse origin chain id: %v", err)
	}
	destinationChainIdInt, err := strconv.ParseInt(destinationChain.ChainId, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("failed to parse destination chain id: %v", err)
	}

	solverAndReceiver, err := relay.GetConfig(destinationChainIdInt, destinationChainIdInt, req.Recipient, destinationToken.TokenId)
	if err != nil {
		return nil, fmt.Errorf("failed to get relay config: %v", err)
	}
	topupGas := false
	if len(solverAndReceiver.User.Balance) < 5 {
		topupGas = true
	}

	quote, err := relay.GetRelayQuote(&relay.QuoteRequest{
		User:                 req.User,
		OriginChainId:        int(originChainIdInt),
		DestinationChainId:   int(destinationChainIdInt),
		OriginCurrency:       originToken.Address,
		DestinationCurrency:  destinationToken.Address,
		Recipient:            req.Recipient,
		Amount:               req.Amount,
		TradeType:            "EXACT_INPUT",
		Referrer:             "relay.link",
		UseExternalLiquidity: false,
		UseDepositAddress:    false,
		TopupGas:             topupGas,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get relay quote: %v", err)
	}
	var (
		resp       response.RespQuote
		solanaHash solana.Hash
		nonce      uint64
	)

	if len(quote.Steps) > 0 && len(quote.Steps[0].Items) > 0 {
		ok, err := CompareValueWithCapacity(quote.Details.CurrencyOut.Amount, solverAndReceiver.Solver.CapacityPerRequest)
		if err != nil {
			return nil, fmt.Errorf("failed to compare value: %v", err)
		}
		if ok {
			return &response.RespQuote{
				Type:        "rango",
				Description: fmt.Sprintf("To prevent exchange failure, please ensure each transaction remains below $%s.", "3000000"), //todo adjust
			}, nil
		}
	}

	if originChain.Name == "solana" {
		solanaHash, err = chainHelper.GetSolBlockHash()
		if err != nil {
			return nil, fmt.Errorf("failed to get Solana block hash: %v", err)
		}
	} else {
		nonceStr, err := chainHelper.GetEthUserNonce(originToken.ChainIdHex, req.User)
		if err != nil {
			return nil, fmt.Errorf("failed to get user nonce: %v", err)
		}
		_nonce, err := strconv.ParseInt(nonceStr, 0, 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse nonce: %v", err)
		}
		nonce = uint64(_nonce)
	}

	err = resp.RlAssign(quote, nonce, &solanaHash, originChain)
	if err != nil {
		return nil, err
	}
	return &resp, nil
}

func CompareValueWithCapacity(quoteValueStr, capacityStr string) (bool, error) {
	value := new(big.Int)
	capacity := new(big.Int)

	// 字符串转 big.Int
	if _, ok := value.SetString(quoteValueStr, 10); !ok {
		return false, errors.New("invalid quote value")
	}
	if _, ok := capacity.SetString(capacityStr, 10); !ok {
		return false, errors.New("invalid capacity value")
	}

	// 计算 capacity * 0.6
	sixty := big.NewRat(6, 10) // 0.6
	capacityRat := new(big.Rat).SetInt(capacity)
	thresholdRat := new(big.Rat).Mul(capacityRat, sixty)
	valueRat := new(big.Rat).SetInt(value)

	// 比较 value > thresholdInt
	if valueRat.Cmp(thresholdRat) == 1 {
		return true, nil
	}
	return false, nil
}
func (s *Swap) GetRangoMetaV2() ([]*model.RangoMetaChains, map[string][]*model.RangoMetaTokens, []*model.RangoMetaSwappers, error) {
	chains, err := s.repo.GetRangoChains()
	if err != nil {
		return nil, nil, nil, err
	}

	tokens, err := s.repo.GetSupportTokens()
	if err != nil {
		return nil, nil, nil, err
	}
	var (
		tokensMap = make(map[string][]*model.RangoMetaTokens)
		chainsMap = make(map[string]*model.RangoMetaChains, len(chains))
	)
	for i, chain := range chains {
		chainsMap[chain.Name] = chains[i]
	}
	for _, token := range tokens {
		chainName := token.BlockChain
		key := chainsMap[chainName].ChainId
		tokensMap[key] = append(tokensMap[key], token)
	}
	return chains, tokensMap, []*model.RangoMetaSwappers{}, nil
}

func (s *Swap) CheckRelayStatus(req request.ReqCheckStatus) (*rango.RespCheckStatus, error) {
	resp1, err := relay.GetStatus(req.RequestId)
	if err != nil {
		return nil, fmt.Errorf("failed to get relay status: %v", err)
	}
	return &rango.RespCheckStatus{
		Status: resp1.Status,
	}, nil
}

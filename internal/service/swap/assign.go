package swap

import (
	"encoding/json"
	"fmt"
	"github.com/status-im/keycard-go/hexutils"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/rango"
	"strings"
)

func (s *Swap) HistoryAssign(resp *rango.RespCreateTx, confirmed rango.ConfirmRouteResult, step int64) (*model.RangoHistory, error) {
	if resp == nil {
		return nil, nil
	}

	var swaps []*model.RangoSwaps
	var validationStatus []byte

	for i, _ := range confirmed.Result.Swaps {
		var tx *rango.Transaction
		if int64(i+1) == step {
			tx = resp.Transaction
		}
		swap, err := s.HistorySwappers(confirmed.Result.Swaps[i], int64(i+1),
			confirmed.SelectedWallets[confirmed.From.Blockchain], confirmed.RequestId, tx)
		if err != nil {
			return nil, fmt.Errorf("swap assign fail: %v", err)
		}
		swaps = append(swaps, swap)
	}
	if len(confirmed.ValidationStatus) > 0 {
		validationStatusList := make([]string, 0, len(confirmed.ValidationStatus))
		for _, validation := range confirmed.ValidationStatus {
			status := fmt.Sprintf("%s: %v", validation.Blockchain, validation.Wallets)
			validationStatusList = append(validationStatusList, status)
		}
		validationStatusTeam, err := utils.ForceJsonMarshal(validationStatusList)
		if err != nil {
			return nil, fmt.Errorf("assign validation fail: %v", err)
		}
		validationStatus = validationStatusTeam
	}

	missingBlockchains, err := utils.ForceJsonMarshal(confirmed.MissingBlockchains)
	if err != nil {
		return nil, fmt.Errorf("swap assign fail: %v", err)
	}
	diagnosisMessages, err := utils.ForceJsonMarshal(confirmed.DiagnosisMessages)
	if err != nil {
		return nil, fmt.Errorf("swap assign fail: %v", err)
	}

	repoEntity := &model.RangoHistory{
		RequestId:                         confirmed.RequestId,
		UserAddress:                       confirmed.SelectedWallets[confirmed.From.Blockchain],
		FromBlockchain:                    confirmed.From.Blockchain,
		FromSymbol:                        confirmed.From.Symbol,
		FromAddress:                       confirmed.From.Address,
		ToBlockchain:                      confirmed.To.Blockchain,
		ToSymbol:                          confirmed.To.Symbol,
		ToAddress:                         confirmed.SelectedWallets[confirmed.To.Blockchain],
		RequestAmount:                     confirmed.RequestAmount,
		OutputAmount:                      confirmed.Result.OutputAmount,
		ResultType:                        confirmed.Result.ResultType,
		ValidationStatus:                  string(validationStatus),
		WalletNotSupportingFromBlockchain: confirmed.WalletNotSupportingFromBlockchain,
		MissingBlockchains:                string(missingBlockchains),
		DiagnosisMessages:                 string(diagnosisMessages),
		Status:                            "pending",
		Step:                              0, //step is 0 for pending swaps
		FailReason:                        resp.Error,
		Swaps:                             swaps,
	}

	if !resp.Ok {
		repoEntity.Status = "failed"
	}

	return repoEntity, nil
}
func (s *Swap) HistorySwappers(swap *rango.ConfirmRouteSwap, step int64, userAddress, requestId string,
	transaction *rango.Transaction) (*model.RangoSwaps, error) {
	var messageBytes = []byte("none")
	if transaction != nil {
		if transaction.Data != "" {
			messageBytes = hexutils.HexToBytes(strings.TrimPrefix(transaction.Data, "0x"))
		}
		if len(transaction.SerializedMessage) != 0 {
			messageBytes = make([]byte, len(transaction.SerializedMessage))
			for i, v := range transaction.SerializedMessage {
				messageBytes[i] = byte(v)
			}
		}
	}

	var fees []*model.Fee
	for _, fee := range swap.Fee {
		fees = append(fees, &model.Fee{
			RequestId: requestId,
			Step:      step,
			Asset: model.FeeAsset{
				Blockchain: fee.Asset.Blockchain,
				Symbol:     fee.Asset.Symbol,
				Address:    fee.Asset.Address,
			},
			ExpenseType: fee.ExpenseType,
			Amount:      fee.Amount,
			Name:        fee.Name,
			Meta: model.FeeMeta{
				Type:     fee.Meta.Type,
				GasLimit: fee.Meta.GasLimit,
				GasPrice: fee.Meta.GasPrice,
			},
			Price: fee.Price,
		})
	}
	feesBytes, err := CompressFee(fees)
	if err != nil {
		return nil, fmt.Errorf("compress fee failed: %v", err)
	}
	callDataHash, err := utils.PackedBytesToHash(messageBytes)
	if err != nil {
		return nil, fmt.Errorf("pack call data hash error: %v", err)
	}
	return &model.RangoSwaps{
		RequestId:              requestId,
		UserAddress:            userAddress,
		SwapperId:              swap.SwapperId,
		SwapperLogo:            swap.SwapperLogo,
		SwapperType:            swap.SwapperType,
		FromAmount:             swap.FromAmount,
		ToAmount:               swap.ToAmount,
		Fee:                    feesBytes,
		EstimatedTimeInSeconds: swap.EstimatedTimeInSeconds,
		SwapChainType:          swap.SwapChainType,
		MaxRequiredSign:        swap.MaxRequiredSign,
		Step:                   step,
		CallDataHash:           callDataHash,
		CallData:               messageBytes,
	}, nil
}

func CompressFee(entities []*model.Fee) ([]byte, error) {
	jsonData, err := json.Marshal(entities)
	if err != nil {
		return nil, err
	}
	compressed, err := utils.Compress(jsonData)
	if err != nil {
		return nil, err
	}
	return compressed, nil
}

func (s *Swap) TxData(transaction *rango.Transaction) ([]byte, string, error) {
	var messageBytes []byte
	if transaction.Data != "" {
		messageBytes = hexutils.HexToBytes(strings.TrimPrefix(transaction.Data, "0x"))
	}
	if len(transaction.SerializedMessage) != 0 {
		messageBytes = make([]byte, len(transaction.SerializedMessage))
		for i, v := range transaction.SerializedMessage {
			messageBytes[i] = byte(v)
		}
	}
	callDataHash, err := utils.PackedBytesToHash(messageBytes)
	if err != nil {
		return nil, "", fmt.Errorf("pack call data hash error: %v", err)
	}
	return messageBytes, callDataHash, nil
}

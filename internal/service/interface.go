// Package usecase implements application business logic. Each logic group in own file.
package service

import (
	"context"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/response"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/rango"
)

//go:generate mockgen -source=interfaces.go -destination=./mocks_usecase_test.go -package=usecase_test

type (
	SwapI interface {
		//relay
		GetRelayQuote(req request.ReqGetQuote) (*response.RespQuote, error)
		GetRelayMeta() (map[string][]*model.RelayMetaTokens, []*model.RelayMetaChains, error)
		CheckRelayStatus(req request.ReqCheckStatus) (*rango.RespCheckStatus, error)
		//rango
		GetBestRoute(req request.ReqGetBestRoute) (*rango.RespBestRoute, error)
		GetBestRouteV2(req request.ReqGetQuote) (*response.RespQuote, error)
		GetAllPossibleRoutes(req request.ReqGetAllPossibleRoutes) (*rango.AllPossibleRoutes, error)
		GetRangoMeta() ([]*model.RangoMetaChains, []*model.RangoMetaTokens, []*model.RangoMetaSwappers, error)
		GetRangoMetaV2() ([]*model.RangoMetaChains, map[string][]*model.RangoMetaTokens, []*model.RangoMetaSwappers, error)
		Histories(blockchain, address string) ([]*model.RangoHistory, int64, error)
		ConfirmRoute(req request.ReqConfirmRoute) (*response.RespConfirmRoute, error)
		CheckApproval(req request.ReqCheckApproval) (*rango.RespCheckApproval, error)
		CreateOrUpdateTx(req request.ReqCreateTx) (*rango.RespCreateTx, error)
		SendCallBack(req request.ReqCallBack) (*response.RespCallBack, error)
		CheckSwapStatus(req request.ReqCheckStatus) (*rango.RespCheckStatus, error)
		CreatedTx(req request.ReqCreateTx) (*rango.RespCreateTx, error)
	}

	SwapTaskI interface {
		SyncRangoMeta() func()
	}

	SymbolTaskI interface {
		CrawSymbolList() func()
		CrawlMarketCap() func()
		CrawCategoryList() func()
	}

	SymbolI interface {
		GetOHLC(req request.ReqOHLC) (*response.RespOHLC, error)
		GetSymbolDetail(req request.ReqGetSymbolDetail) (*response.RespGetSymbolDetail, error)
		GetSymbolList(req request.ReqSymbolList) (*response.RespSymbolList, error)
		UpsertFavoriteSymbol(req request.ReqUpsertFavoriteSymbol) (*response.RespUpsertFavoriteSymbol, error)
		GetFavoriteSymbols(req request.ReqGetFavoriteSymbols) (*response.RespGetFavoriteSymbols, error)
		GetCategory() (*response.RespGetCategory, error)
		GetUserSymbolPreference(req request.GetUserSymbolPreferenceDto) (*response.RespUserSymbolPreference, error)
		UpsertUserSymbolPreference(req request.UpsertUserSymbolPreferenceDto) (*response.RespUserSymbolPreference, error)
		SearchSymbol(filter string) (*response.RespSearchSymbol, error)
		GetPopularSymbol(number int) (*response.RespGetPopularSymbol, error)
		GetNewSymbol() (*response.RespGetNewSymbol, error)
		GetSymbolSignal(req request.ReqSignals) (*response.RespSignals, error)
		GetAlertSymbolsSetting(req request.ReqGetAlertSymbolsSetting) (*response.RespGetAlertSymbolsSetting, error)
		CreateAlertSymbolSetting(req request.ReqCreateAlertSymbolSetting) (string, error)
		UpdateAlertSymbolSetting(request.ReqUpdateAlertSymbolSetting) error
		DeleteAlertSymbolSetting(req request.ReqDeleteAlertSymbolSetting) error
	}

	TradeI interface {
		GetUserOpenOrder(req request.ReqGetUserOpenOrder) (*response.RespGetUserOpenOrder, error)
		GetUserPosition(req request.ReqGetUserPosition) (*response.RespGetUserPosition, error)
		GetUserTradeHistory(req request.ReqGetUserTradeHistory) (*response.RespGetUserTradeHistory, error)
		StoreTxInformation(req request.ReqStoreTxInformation) (*response.RespStoreTxInformation, error)
		GetUserPrevDayBalance(req request.ReqGetUserPrevDayBalance) (*response.RespGetUserPrevDayBalance, error)
	}

	OrderStatusI interface {
		GetOrderStatus(req request.ReqGetUserOrderStatus) (*response.RespGetUserOrderStatus, error)
	}
	MiscEventI interface {
		GetUserMiscEvents(req request.ReqGetUserMiscEvents) (*response.RespGetUserEvents, error)
	}
	CommandI interface {
		UpdateRangoActiveStatus() error
	}

	AssetI interface {
		CallbackUserBalance(req request.ReqCallbackUserBalance) error
	}

	UserI interface {
		AddUserWallet(ctx context.Context) error
	}

	AssetTaskI interface {
		TrackUserBalances(ctx context.Context) func()
	}
)

package asset

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/cache"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/nats"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/hyperliquid"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"time"
)

type Asset struct {
	assetRepo  repo.AssetRepo
	symbolRepo repo.SymbolRepo
	tradeRepo  repo.TradeRepo
	natsClient *nats.NATSClient
}

func NewAssetService(assetRepo repo.AssetRepo, symbolRepo repo.SymbolRepo, tradeRepo repo.TradeRepo, natsClient *nats.NATSClient) service.AssetI {
	return &Asset{
		assetRepo:  assetRepo,
		symbolRepo: symbolRepo,
		tradeRepo:  tradeRepo,
		natsClient: natsClient,
	}
}

func (s *Asset) CallbackUserBalance(req request.ReqCallbackUserBalance) error {
	accountSummary, err := hyperliquid.GetAccountSummaryFromAPI(req.WalletAddress)
	if err != nil {
		return err
	}

	userPositions := AssignUserPosition(req.UserID, req.WalletAddress, accountSummary)
	if err = s.tradeRepo.UpdateUserPosition(userPositions); err != nil {
		return err
	}

	accountBalance := utils.StringToFloat(accountSummary.CrossMarginSummary.AccountValue)
	rawUSD := utils.StringToFloat(accountSummary.CrossMarginSummary.TotalRawUsd)
	if err = s.updateUserBalance(req.WalletAddress, accountBalance, rawUSD, time.Now()); err != nil {
		return err
	}

	userBalanceEvent, err := AssignUserBalanceToMsg(req.UserID, req.WalletAddress, accountBalance)

	data, err := json.Marshal(userBalanceEvent)
	if err != nil {
		global.GVA_LOG.Error("[task] TrackUserBalances ", zap.Any("err", err))
	}

	_, err = s.natsClient.PublishJS(utils.WalletBalanceSubject, data)
	if err != nil {
		global.GVA_LOG.Error("[task] TrackUserBalances ", zap.Any("err", err))
	}

	return nil
}

func (s *Asset) assignUserBalanceToMsg(userID uuid.UUID, walletAddress string, balance float64) (nats.UserBalanceEventMsg, error) {
	var (
		ARBStatisticData model.CoinStatistic
		cacheKey         = fmt.Sprintf("%s%s", cache.KeySymbolStatisticData(), utils.NativeToken)
	)

	str, err := global.GVA_REDIS.Get(context.Background(), cacheKey).Result()
	if err == nil && str != "" {
		if err = json.Unmarshal([]byte(str), &ARBStatisticData); err != nil {
			return nats.UserBalanceEventMsg{}, fmt.Errorf("failed to unmarshal symbol detail data: %w", err)
		}
	}

	ts := time.Now()

	userBalanceDataMsg := make([]nats.UserBalanceData, 0)
	userBalanceDataMsg = append(userBalanceDataMsg, nats.UserBalanceData{
		UserID:             userID.String(),
		Timestamp:          ts.UnixMilli(),
		WalletAddress:      walletAddress,
		WalletType:         utils.WalletType,
		NativeTokenSymbol:  utils.NativeToken,
		NativeTokenAddress: utils.NativeTokenAddress,
		NativeTokenBalance: balance / ARBStatisticData.MarkPx,
		USDBalance:         balance,
	})

	return nats.UserBalanceEventMsg{
		Events: userBalanceDataMsg,
	}, nil
}

func (s *Asset) updateUserBalance(walletAddress string, balance float64, rawUSD float64, ts time.Time) error {
	hour := ts.Hour()
	minute := ts.Minute()
	// If ts in range 23:55 : 00:04, update prev day balance equal last balance
	currentUserBalance, err := s.assetRepo.GetUserBalance(walletAddress)
	var prevDayBalance float64
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if (hour >= 23 && minute >= 55) || (hour >= 00 && minute <= 04) {
		prevDayBalance = currentUserBalance.Balance
	} else {
		prevDayBalance = currentUserBalance.PrevDayBalance
	}

	return s.assetRepo.SaveUserBalance(model.UserBalance{
		WalletAddress:  walletAddress,
		Balance:        balance,
		PrevDayBalance: prevDayBalance,
		RawUSD:         rawUSD,
		UpdateAt:       ts,
	})
}

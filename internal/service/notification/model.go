package notification

import "time"

type SymbolPriceRaw struct {
	Timestamp        time.Time `json:"ts"`
	Symbol           string    `json:"symbol"`
	Price            float64   `json:"price"`
	ChangePercent24h float64   `json:"changePercent24h"`
}

type SymbolPriceSignificantChange struct {
	Timestamp        time.Time `json:"ts"`
	Symbol           string    `json:"symbol"`
	CurrentPrice     float64   `json:"currentPrice"`
	PreviousPrice    float64   `json:"previousPrice"`
	ChangeAmount     float64   `json:"changeAmount"`
	ChangePercent    float64   `json:"changePercent"`
	ChangePercent24h float64   `json:"changePercent24h"`
	Direction        string    `json:"direction"`
}

type SymbolAlertEvent struct {
	NotificationSettingID string    `json:"notificationSettingId"`
	UserID                string    `json:"userId"`
	Symbol                string    `json:"symbol"`
	Type                  string    `json:"type"`
	Timestamp             time.Time `json:"ts"`
	CurrentPrice          float64   `json:"currentPrice"`
	PreviousPrice         float64   `json:"previousPrice"`
	ChangePercent24h      float64   `json:"changePercent24h"`
	ChangeDirection       string    `json:"changeDirection"`
}

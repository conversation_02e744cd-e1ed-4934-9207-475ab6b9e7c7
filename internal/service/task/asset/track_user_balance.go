package asset

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/cache"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/nats"
	natsClient "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/nats"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"sync"
	"time"
)

const (
	QueueBufferSize = 1000
	MaxRetries      = 3
	BatchSize       = 1000
)

type WorkItem struct {
	UserID        uuid.UUID `json:"userId"`
	WalletAddress string    `json:"walletAddress"`
	Retries       int
}

type UserBalance struct {
	UserID        uuid.UUID `json:"userId"`
	WalletAddress string    `json:"walletAddress"`
	Balance       float64   `json:"balance"`
	LastUpdated   time.Time `json:"lastUpdated"`
}

type assetTask struct {
	wg             sync.WaitGroup
	workerPoolSize int
	AssetRepo      repo.AssetRepo
	TradeRepo      repo.TradeRepo
	NatsClient     *natsClient.NATSClient
	workQueue      chan WorkItem
	resultQueue    chan UserBalance
}

func NewAssetTask(assetRepo repo.AssetRepo, tradeRepo repo.TradeRepo, client *natsClient.NATSClient) service.AssetTaskI {
	return &assetTask{
		AssetRepo:      assetRepo,
		TradeRepo:      tradeRepo,
		NatsClient:     client,
		workerPoolSize: 3,
		workQueue:      make(chan WorkItem, QueueBufferSize),
		resultQueue:    make(chan UserBalance, QueueBufferSize),
	}
}

func (t *assetTask) TrackUserBalances(ctx context.Context) func() {
	return func() {
		t.startWorkerPool(ctx)

		// Start result processor
		go t.processResults(ctx)

		// Queue all users for processing
		if err := t.queueUsers(ctx); err != nil {
			global.GVA_LOG.Error("[task] TrackUserBalances ", zap.Any("err", err))
		}

		// Wait for all workers to complete
		t.wg.Wait()

		select {
		case <-ctx.Done():
			close(t.resultQueue)
			close(t.workQueue)
		}
	}
}

func (t *assetTask) startWorkerPool(ctx context.Context) {
	for i := 0; i < t.workerPoolSize; i++ {
		t.wg.Add(1)
		go t.worker(ctx, i)
	}
}

func (t *assetTask) worker(ctx context.Context, workerID int) {
	defer t.wg.Done()

	for {
		select {
		case <-ctx.Done():
			return
		case work, ok := <-t.workQueue:
			if !ok {
				return
			}
			t.processUser(ctx, work, workerID)
		}
	}
}

// User processing logic
func (t *assetTask) processUser(ctx context.Context, work WorkItem, workerID int) {
	// Get from DB user balance instead of API
	balance, err := t.getUserBalance(work.UserID, work.WalletAddress)
	if err != nil {
		// If max retries reached, mark as failed
		if work.Retries >= MaxRetries {
			balance = UserBalance{
				UserID:        work.UserID,
				WalletAddress: work.WalletAddress,
				Balance:       0,
				LastUpdated:   time.Now(),
			}
		} else {
			select {
			case t.workQueue <- WorkItem{UserID: work.UserID, WalletAddress: work.WalletAddress, Retries: work.Retries + 1}:
			case <-ctx.Done():
				return
			}
			return
		}
	}

	// Send to result queue
	select {
	case t.resultQueue <- balance:
	case <-ctx.Done():
		return
	}
}

func (t *assetTask) processResults(ctx context.Context) {
	batch := make([]UserBalance, 0, BatchSize)
	batchNum := 0

	ticker := time.NewTicker(5 * time.Second) // Force send every 5 seconds
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			if len(batch) > 0 {
				t.publishBatch(batch, batchNum)
			}
			return

		case result, ok := <-t.resultQueue:
			if !ok {
				// Channel closed, send remaining batch
				if len(batch) > 0 {
					t.publishBatch(batch, batchNum)
				}
				return
			}

			// Save User balance
			if err := t.updateUserBalance(result); err != nil {
				global.GVA_LOG.Error("[task] TrackUserBalances", zap.Any("err", err))
				continue
			}
			batch = append(batch, result)

			if len(batch) >= BatchSize {
				t.publishBatch(batch, batchNum)
				batch = make([]UserBalance, 0, BatchSize)
				batchNum++
			}

		case <-ticker.C:
			// Periodic flush
			if len(batch) > 0 {
				t.publishBatch(batch, batchNum)
				batch = make([]UserBalance, 0, BatchSize)
				batchNum++
			}
		}
	}
}

func (t *assetTask) getUserBalance(userID uuid.UUID, walletAddress string) (UserBalance, error) {
	balance, err := t.AssetRepo.GetUserBalance(walletAddress)
	if err != nil {
		return UserBalance{}, err
	}

	positions, err := t.TradeRepo.GetUserPosition(walletAddress)
	if err != nil {
		return UserBalance{}, err
	}

	var (
		unrealizePnl       float64
		totalPositionValue float64
	)

	for _, position := range positions {
		var (
			statisticData *model.CoinStatistic
			cacheKey      = fmt.Sprintf("%s%s", cache.KeySymbolStatisticData(), position.Symbol)
		)

		data, err := global.GVA_REDIS.Get(context.Background(), cacheKey).Result()
		if err == nil && data != "" {
			if err = json.Unmarshal([]byte(data), &statisticData); err != nil {
				return UserBalance{}, fmt.Errorf("failed to unmarshal symbol detail data: %w", err)
			}
		}

		unrealizePnl += position.Size * (statisticData.MarkPx - position.EntryPx)
		totalPositionValue += position.Size * statisticData.MarkPx
	}

	return UserBalance{
		Balance:       balance.RawUSD + totalPositionValue,
		UserID:        userID,
		WalletAddress: walletAddress,
		LastUpdated:   time.Now(),
	}, nil
}

func (t *assetTask) assignUserBalanceToMsg(userBalances []UserBalance) (nats.UserBalanceEventMsg, error) {
	var (
		ARBStatisticData model.CoinStatistic
		cacheKey         = fmt.Sprintf("%s%s", cache.KeySymbolStatisticData(), utils.NativeToken)
	)

	str, err := global.GVA_REDIS.Get(context.Background(), cacheKey).Result()
	if err == nil && str != "" {
		if err = json.Unmarshal([]byte(str), &ARBStatisticData); err != nil {
			return nats.UserBalanceEventMsg{}, fmt.Errorf("failed to unmarshal symbol detail data: %w", err)
		}
	}

	ts := time.Now()
	userBalanceDataMsg := make([]nats.UserBalanceData, 0)
	for _, userBalance := range userBalances {
		userBalanceDataMsg = append(userBalanceDataMsg, nats.UserBalanceData{
			UserID:             userBalance.UserID.String(),
			Timestamp:          ts.UnixMilli(),
			WalletAddress:      userBalance.WalletAddress,
			WalletType:         utils.WalletType,
			NativeTokenSymbol:  utils.NativeToken,
			NativeTokenAddress: utils.NativeTokenAddress,
			NativeTokenBalance: userBalance.Balance / ARBStatisticData.MarkPx,
			USDBalance:         userBalance.Balance,
		})
	}

	return nats.UserBalanceEventMsg{
		Events: userBalanceDataMsg,
	}, nil
}

func (t *assetTask) queueUsers(ctx context.Context) error {
	const pageSize = 1000
	offset := 0

	for {
		addresses, err := t.AssetRepo.GetAllUserWallets(offset, pageSize)
		if err != nil {
			return err
		}

		if len(addresses) == 0 {
			break
		}

		// Queue users for processing
		for _, address := range addresses {
			select {
			case t.workQueue <- WorkItem{UserID: address.UserID, WalletAddress: address.WalletAddress, Retries: 0}:
			case <-ctx.Done():
				return ctx.Err()
			}
		}

		offset += pageSize

		// Break if we got less than pageSize (last page)
		if len(addresses) < pageSize {
			break
		}
	}

	return nil
}

func (t *assetTask) publishBatch(batch []UserBalance, batchNum int) {
	userBalanceEvent, err := t.assignUserBalanceToMsg(batch)

	data, err := json.Marshal(userBalanceEvent)
	if err != nil {
		global.GVA_LOG.Error("[task] TrackUserBalances ", zap.Any("err", err))
	}

	_, err = t.NatsClient.PublishJS(utils.WalletBalanceSubject, data)
	if err != nil {
		global.GVA_LOG.Error("[task] TrackUserBalances ", zap.Any("err", err))
	}
}

func (t *assetTask) updateUserBalance(balance UserBalance) error {
	hour := balance.LastUpdated.Hour()
	minute := balance.LastUpdated.Minute()
	// If ts in range 23:55 : 00:04, update prev day balance equal last balance
	currentUserBalance, err := t.AssetRepo.GetUserBalance(balance.WalletAddress)
	var prevDayBalance float64
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if (hour >= 23 && minute >= 55) || (hour >= 00 && minute <= 04) {
		prevDayBalance = currentUserBalance.Balance
	} else {
		prevDayBalance = currentUserBalance.PrevDayBalance
	}

	return t.AssetRepo.SaveUserBalance(model.UserBalance{
		WalletAddress:  balance.WalletAddress,
		Balance:        balance.Balance,
		PrevDayBalance: prevDayBalance,
		RawUSD:         currentUserBalance.RawUSD,
		UpdateAt:       balance.LastUpdated,
	})
}

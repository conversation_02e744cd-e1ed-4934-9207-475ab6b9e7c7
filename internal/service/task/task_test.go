package task

import (
	"context"
	"log"
	"testing"
	"time"
)

func TestNewTaskScheduler(t *testing.T) {
	scheduler := NewTaskScheduler()
	ctx, cancel := context.WithCancel(context.Background())
	scheduler.cancelFunc = cancel
	//Start the task scheduler
	go scheduler.RunWithSignal(ctx)
	err := scheduler.Register("task_every_2s", "*/2 * * * * *", exampleTask("task_every_2s"))
	if err != nil {
		t.Error("task_every_2s error = ", err)
	}
	_ = scheduler.Register("task_every_5s", "0 */1 * * * *", exampleTask("task_every_5s"))
	go func() {
		time.Sleep(5 * time.Second)
		scheduler.Remove("task_every_2s")
	}()
	select {}
}

func exampleTask(name string) func() {
	return func() {
		log.Printf("🕒 [%s] run task time：%s\n", name, time.Now().Format(time.RFC3339))
	}
}

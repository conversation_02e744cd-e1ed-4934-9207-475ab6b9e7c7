package symbol

import (
	"cmp"
	"encoding/json"
	"fmt"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/cache"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/constant"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	natsClient "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/nats"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/notification"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/coinmarketcap"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/hyperliquid"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/symbol"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
	"reflect"
	"slices"
	"strings"
	"time"
)

type symbolTask struct {
	repo       repo.SymbolRepo
	natsClient *natsClient.NATSClient
}

func NewSymbolTask(repo repo.SymbolRepo, client *natsClient.NATSClient) service.SymbolTaskI {
	return &symbolTask{
		repo:       repo,
		natsClient: client,
	}
}

func (s *symbolTask) CrawSymbolList() func() {
	return func() {
		if s.repo == nil {
			global.GVA_LOG.Error("task	swap is nil")
			return
		}

		var coinList []model.Coin
		var coinStatistic []model.CoinStatistic
		coinList, coinStatistic, err := s.getSymbolData()
		if err != nil {
			global.GVA_LOG.Error("[task] CrawSymbolList ", zap.Any("err", err))
			return
		}

		if len(coinList) == 0 {
			global.GVA_LOG.Error("[task] CrawSymbolList ", zap.Any("err", "symbol list is empty"))
			return
		}

		if err = s.repo.UpdateSymbolData(coinList, coinStatistic); err != nil {
			global.GVA_LOG.Error("[task] CrawSymbolList ", zap.Any("err", err))
			return
		}

		var symbolStatistic []symbol.SymbolStatistic
		if symbolStatistic, err = s.sortSymbolList(coinList, coinStatistic); err != nil {
			global.GVA_LOG.Error("[task] CrawSymbolList ", zap.Any("err", err))
			return
		}

		// Push data to mqtt
		if err = s.publishMessageToMQTT(symbolStatistic, "public/symbol/list"); err != nil {
			global.GVA_LOG.Error("[task] CrawSymbolList ", zap.Any("err", err))
			return
		}

		// Publish price data to nat js
		if err = s.publishMessageToNATS(symbolStatistic); err != nil {
			global.GVA_LOG.Error("[task] CrawSymbolList ", zap.Any("err", err))
		}

		var symbolCategoryList symbol.CategoryDetail
		if symbolCategoryList, err = s.getSymbolCategoryList(); err != nil {
			global.GVA_LOG.Error("[task] CrawSymbolList ", zap.Any("err", err))
			return
		}

		if err = s.cacheSymbolCategoryList(symbolCategoryList); err != nil {
			global.GVA_LOG.Error("[task] CrawSymbolList ", zap.Any("err", err))
			return
		}

		if err = s.cacheSymbolDetailData(coinList, coinStatistic); err != nil {
			global.GVA_LOG.Error("[task] CrawSymbolList ", zap.Any("err", err))
		}
	}
}

func (s *symbolTask) CrawlMarketCap() func() {
	return func() {
		if s.repo == nil {
			global.GVA_LOG.Error("task	swap is nil")
			return
		}

		var symbolList []string
		symbolList, err := s.getOriginSymbolList()
		if err != nil {
			global.GVA_LOG.Error("[task] CrawlMarketCap ", zap.Any("err", err))
			return
		}

		if len(symbolList) == 0 {
			global.GVA_LOG.Error("[task] CrawlMarketCap ", zap.Any("err", "symbol list is empty"))
			return
		}

		// Call to CoinMarketCap API
		var (
			symbolStatistic   []model.CoinStatistic
			symbolOverallData []model.Coin
		)
		symbolStatistic, symbolOverallData, err = s.getMarketCapData("symbol", strings.Join(symbolList, ","), symbolStatistic, symbolOverallData)
		if err != nil {
			global.GVA_LOG.Error("[task] CrawlMarketCap ", zap.Any("err", err))
			return
		}

		symbolStatistic, symbolOverallData, err = s.getMarketCapData("id", coinmarketcap.IDList, symbolStatistic, symbolOverallData)
		if err != nil {
			global.GVA_LOG.Error("[task] CrawlMarketCap ", zap.Any("err", err))
			return
		}

		if err = s.repo.UpdateTokenAddressData(symbolOverallData); err != nil {
			global.GVA_LOG.Error("[task] CrawlMarketCap ", zap.Any("err", err))
			return
		}

		if err = s.repo.UpdateTotalSymbolData(symbolStatistic); err != nil {
			global.GVA_LOG.Error("[task] CrawlMarketCap ", zap.Any("err", err))
			return
		}
	}
}

func (s *symbolTask) getSymbolData() ([]model.Coin, []model.CoinStatistic, error) {
	universeData, statisticDataArray, err := hyperliquid.CrawSymbolListFromAPI()
	if err != nil {
		return nil, nil, err
	}

	var (
		currentTime    = time.Now()
		coinInfos      = make([]model.Coin, 0)
		coinStatistics = make([]model.CoinStatistic, 0)
	)

	for i := range universeData.Universe {
		coinInfo := model.Coin{
			Symbol:        universeData.Universe[i].Name,
			SzDecimals:    universeData.Universe[i].SzDecimals,
			MaxLeverage:   universeData.Universe[i].MaxLeverage,
			IsDelisted:    universeData.Universe[i].IsDelisted,
			OnlyIsolated:  universeData.Universe[i].OnlyIsolated,
			MarginTableID: universeData.Universe[i].MarginTableId,
			CreatedAt:     currentTime,
			LastUpdated:   currentTime,
		}

		var impactPxBid, impactPxAsk, changePx, changePxPercent float64
		if !coinInfo.IsDelisted {
			impactPxBid = utils.StringToFloat(statisticDataArray[i].ImpactPxs[0])
			impactPxAsk = utils.StringToFloat(statisticDataArray[i].ImpactPxs[1])
			changePx = utils.StringToFloat(statisticDataArray[i].MarkPx) - utils.StringToFloat(statisticDataArray[i].PrevDayPx)
			changePxPercent = changePx / utils.StringToFloat(statisticDataArray[i].PrevDayPx) * 100
		}
		coinStatistic := model.CoinStatistic{
			Symbol:          universeData.Universe[i].Name,
			Funding:         utils.StringToFloat(statisticDataArray[i].Funding),
			OpenInterest:    utils.StringToFloat(statisticDataArray[i].OpenInterest),
			PrevDayPx:       utils.StringToFloat(statisticDataArray[i].PrevDayPx),
			DayNtlVlm:       utils.StringToFloat(statisticDataArray[i].DayNtlVlm),
			Premium:         utils.StringToFloat(statisticDataArray[i].Premium),
			OraclePx:        utils.StringToFloat(statisticDataArray[i].OraclePx),
			MarkPx:          utils.StringToFloat(statisticDataArray[i].MarkPx),
			MidPx:           utils.StringToFloat(statisticDataArray[i].MidPx),
			ChangePx:        changePx,
			ChangePxPercent: changePxPercent,
			ImpactPxBid:     impactPxBid,
			ImpactPxAsk:     impactPxAsk,
			DayBaseVlm:      utils.StringToFloat(statisticDataArray[i].DayBaseVlm),
			LastUpdated:     currentTime,
		}

		coinInfos = append(coinInfos, coinInfo)
		coinStatistics = append(coinStatistics, coinStatistic)
	}
	return coinInfos, coinStatistics, nil
}

func (s *symbolTask) cacheSymbolDetailData(symbolList []model.Coin, symbolStatisticList []model.CoinStatistic) error {
	for i := range symbolList {
		cacheOverallDataKey := fmt.Sprintf("%s%s", cache.KeySymbolOverallData(), symbolList[i].Symbol)
		if err := s.repo.CacheSymbolData(symbolList[i], cacheOverallDataKey, constant.ThirtySecondCacheTime); err != nil {
			return err
		}

		cacheStatisticDataKey := fmt.Sprintf("%s%s", cache.KeySymbolStatisticData(), symbolList[i].Symbol)
		if err := s.repo.CacheSymbolData(symbolStatisticList[i], cacheStatisticDataKey, constant.ThirtySecondCacheTime); err != nil {
			return err
		}
	}

	return nil
}

func (s *symbolTask) sortSymbolList(symbolList []model.Coin, symbolStatisticList []model.CoinStatistic) ([]symbol.SymbolStatistic, error) {
	// Filter only listed symbols and convert to SymbolStatistic
	listedSymbolList := make([]symbol.SymbolStatistic, 0, len(symbolList))
	for i := range symbolList {
		if !symbolList[i].IsDelisted {
			listedSymbolList = append(listedSymbolList, symbol.SymbolStatistic{
				Symbol:            symbolList[i].Symbol,
				MaxLeverage:       symbolList[i].MaxLeverage,
				Funding:           symbolStatisticList[i].Funding,
				MarketCap:         symbolStatisticList[i].MarketCap,
				TotalSupply:       symbolStatisticList[i].TotalSupply,
				CirculatingSupply: symbolStatisticList[i].CirculatingSupply,
				OpenInterest:      symbolStatisticList[i].OpenInterest,
				PrevDayPx:         symbolStatisticList[i].PrevDayPx,
				DayNtlVlm:         symbolStatisticList[i].DayNtlVlm,
				MarkPx:            symbolStatisticList[i].MarkPx,
				ChangePx:          symbolStatisticList[i].ChangePx,
				ChangePxPercent:   symbolStatisticList[i].ChangePxPercent,
			})
		}
	}

	// Define sorting criteria and cache keys
	sortingConfig := []struct {
		sortFunc func(a, b symbol.SymbolStatistic) int
		cacheKey string
		list     *[]symbol.SymbolStatistic // Pointer to use specialized lists where needed
	}{
		{
			sortFunc: func(a, b symbol.SymbolStatistic) int { return -cmp.Compare(a.DayNtlVlm, b.DayNtlVlm) },
			cacheKey: cache.KeySymbolListByVolume(),
			list:     &listedSymbolList,
		},
		{
			sortFunc: func(a, b symbol.SymbolStatistic) int { return -cmp.Compare(a.MarketCap, b.MarketCap) },
			cacheKey: cache.KeySymbolListByMarketCap(),
			list:     &listedSymbolList,
		},
		{
			sortFunc: func(a, b symbol.SymbolStatistic) int { return -cmp.Compare(a.OpenInterest, b.OpenInterest) },
			cacheKey: cache.KeySymbolListByOpenInterest(),
			list:     &listedSymbolList,
		},
		{
			sortFunc: func(a, b symbol.SymbolStatistic) int { return cmp.Compare(a.ChangePxPercent, b.ChangePxPercent) },
			cacheKey: cache.KeySymbolListByLoser(),
			list:     &listedSymbolList,
		},
		{
			sortFunc: func(a, b symbol.SymbolStatistic) int { return -cmp.Compare(a.ChangePxPercent, b.ChangePxPercent) },
			cacheKey: cache.KeySymbolListByGainer(),
			list:     &listedSymbolList,
		},
	}

	// Create trending list separately
	trendingList := make([]symbol.SymbolStatistic, 0)
	for _, info := range listedSymbolList {
		if info.ChangePxPercent >= 50 {
			trendingList = append(trendingList, info)
		}
	}
	sortingConfig = append(sortingConfig, struct {
		sortFunc func(a, b symbol.SymbolStatistic) int
		cacheKey string
		list     *[]symbol.SymbolStatistic
	}{
		sortFunc: nil, // No sorting needed for trending list
		cacheKey: cache.KeySymbolListByTrend(),
		list:     &trendingList,
	})

	// Process all sort configurations and save to Redis
	for _, config := range sortingConfig {
		// Apply sorting if a sort function is provided
		if config.sortFunc != nil {
			slices.SortStableFunc(*config.list, config.sortFunc)
		}

		if err := s.repo.CacheSymbolData(*config.list, config.cacheKey, constant.ThirtySecondCacheTime); err != nil {
			return nil, err
		}
	}

	return listedSymbolList, nil
}

func (s *symbolTask) getOriginSymbolList() ([]string, error) {
	list, err := s.repo.GetSymbolList()
	if err != nil {
		return nil, err
	}

	for i := range list {
		if originSymbol, ok := hyperliquid.CurrentSymbolMap[list[i]]; ok {
			list[i] = originSymbol
		}

		if _, ok := coinmarketcap.SymbolIDMap[list[i]]; ok {
			list[i] = ""
		}
	}

	return list, nil
}

func (s *symbolTask) getMarketCapData(field, value string, coinStatistics []model.CoinStatistic, coinOverallData []model.Coin) ([]model.CoinStatistic, []model.Coin, error) {
	response, err := coinmarketcap.GetCoinMarketCapData(field, value)
	if err != nil {
		return nil, nil, err
	}

	for _, crypto := range response.Data {
		symbolName := crypto.Symbol
		if hyperliquildSymbol, ok := hyperliquid.OriginSymbolMap[symbolName]; ok {
			symbolName = hyperliquildSymbol
		}

		symbolID := crypto.ID
		if hyperliquildSymbol, ok := coinmarketcap.IDSymbolMap[symbolID]; ok {
			symbolName = hyperliquildSymbol
		}

		circulatingSupply := crypto.CirculatingSupply
		if circulatingSupply == 0 {
			circulatingSupply = crypto.SelfReportedCirculatingSupply
		}

		coinStatistics = append(coinStatistics, model.CoinStatistic{
			Symbol:            symbolName,
			TotalSupply:       crypto.TotalSupply,
			CirculatingSupply: circulatingSupply,
		})

		coinOverallData = append(coinOverallData, model.Coin{
			Symbol:  symbolName,
			Address: crypto.Platform.TokenAddress,
		})
	}

	return coinStatistics, coinOverallData, nil
}

func (s *symbolTask) CrawCategoryList() func() {
	return func() {
		var symbolList []string
		symbolList, err := s.repo.GetSymbolList()
		if err != nil {
			global.GVA_LOG.Error("[task] CrawlMarketCap ", zap.Any("err", err))
			return
		}

		if len(symbolList) == 0 {
			return
		}

		// Get URL
		mainPageURL, err := hyperliquid.GetMainPageURL()
		if err != nil {
			global.GVA_LOG.Error("[task] CrawCategoryList ", zap.Any("err", err))
			return
		}

		// Fetch URL content
		content, err := hyperliquid.FetchMainPageContent(mainPageURL)
		if err != nil {
			global.GVA_LOG.Error("[task] CrawCategoryList ", zap.Any("err", err))
		}

		// Extract data
		list := hyperliquid.ParseMainPageContent(content)

		// Save to DB
		err = s.SaveCategoryListToDB(symbolList, list)
		if err != nil {
			global.GVA_LOG.Error("[task] CrawCategoryList", zap.Any("err", err))
		}
	}
}

func (s *symbolTask) SaveCategoryListToDB(symbolList []string, list *hyperliquid.CategoryList) error {
	symbols := strings.Join(symbolList, ",")
	v := reflect.ValueOf(*list)
	typeOfS := v.Type()

	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		category := typeOfS.Field(i).Name

		for j := 0; j < field.Len(); j++ {
			symbolName := field.Index(j).String()
			if !strings.Contains(symbols, symbolName) {
				continue
			}

			err := global.GVA_DB.Model(&model.CategoryCoin{}).
				Clauses(clause.OnConflict{
					Columns:   []clause.Column{{Name: "category_name"}, {Name: "coin_symbol"}},
					UpdateAll: true,
				}).Create(&model.CategoryCoin{
				CategoryName: category,
				CoinSymbol:   symbolName,
				LastUpdate:   time.Now()}).Error
			if err != nil {
				return err
			}
		}

	}
	return nil
}

func (s *symbolTask) getSymbolCategoryList() (symbol.CategoryDetail, error) {
	list := symbol.CategoryDetail{
		Dex:       make([]symbol.SymbolStatistic, 0),
		Trending:  make([]symbol.SymbolStatistic, 0),
		PreLaunch: make([]symbol.SymbolStatistic, 0),
		Layer1:    make([]symbol.SymbolStatistic, 0),
		Layer2:    make([]symbol.SymbolStatistic, 0),
		Defi:      make([]symbol.SymbolStatistic, 0),
		Gaming:    make([]symbol.SymbolStatistic, 0),
		Meme:      make([]symbol.SymbolStatistic, 0),
	}

	v := reflect.ValueOf(&list).Elem()
	typeOfS := v.Type()
	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		category := typeOfS.Field(i).Name

		// Get list coin
		symbolList := []string{}
		err := global.GVA_DB.Model(&model.CategoryCoin{}).
			Select("coin_symbol").
			Where("category_name = ?", category).
			Find(&symbolList).Error

		if err != nil {
			return symbol.CategoryDetail{}, err
		}

		for i := range symbolList {
			var overallData model.Coin
			var statisticData model.CoinStatistic
			err = global.GVA_DB.Model(&model.Coin{}).
				Where("symbol = ?", symbolList[i]).
				First(&overallData).Error
			if err != nil {
				continue
			}

			if overallData.IsDelisted {
				continue
			}

			err = global.GVA_DB.Model(&model.CoinStatistic{}).
				Where("symbol = ?", symbolList[i]).
				First(&statisticData).Error
			if err != nil {
				continue
			}

			updateCategory := reflect.Append(field, reflect.ValueOf(symbol.SymbolStatistic{
				Symbol:            symbolList[i],
				MaxLeverage:       overallData.MaxLeverage,
				Funding:           statisticData.Funding,
				MarketCap:         statisticData.MarketCap,
				TotalSupply:       statisticData.TotalSupply,
				CirculatingSupply: statisticData.CirculatingSupply,
				OpenInterest:      statisticData.OpenInterest,
				PrevDayPx:         statisticData.PrevDayPx,
				DayNtlVlm:         statisticData.DayNtlVlm,
				MarkPx:            statisticData.MarkPx,
				ChangePx:          statisticData.ChangePx,
				ChangePxPercent:   statisticData.ChangePxPercent,
			}))

			field.Set(updateCategory)
		}
	}

	return list, nil
}

func (s *symbolTask) cacheSymbolCategoryList(list symbol.CategoryDetail) error {
	v := reflect.ValueOf(list)
	typeOfS := v.Type()
	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		category := typeOfS.Field(i).Name

		var cacheKey = cache.KeySymbolListByCategory() + category
		if err := s.repo.CacheSymbolData(field.Interface(), cacheKey, constant.ThirtySecondCacheTime); err != nil {
			return err
		}
	}
	return nil
}

func (s *symbolTask) publishMessageToMQTT(data any, mqttTopic string) error {
	rawData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	token := (*global.GVA_MQTT).Publish(mqttTopic, 0, false, rawData)
	token.Wait()

	if token.Error() != nil {
		return fmt.Errorf("publish message to MQTT failed: %v", token.Error())
	}
	return nil
}

func (s *symbolTask) publishMessageToNATS(symbolStatistics []symbol.SymbolStatistic) error {
	ts := time.Now()
	for _, data := range symbolStatistics {
		var rawPriceEvent = notification.SymbolPriceRaw{
			Symbol:           data.Symbol,
			Price:            data.MarkPx,
			Timestamp:        ts,
			ChangePercent24h: data.ChangePxPercent,
		}
		eventData, err := json.Marshal(rawPriceEvent)
		if err != nil {
			return err
		}

		_, err = s.natsClient.PublishJS(utils.RawPricesSubject, eventData)
		if err != nil {
			return err
		}
	}

	return nil
}

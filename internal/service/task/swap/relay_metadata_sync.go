package swap

import (
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"go.uber.org/zap"
)

func (r *swapTask) SyncRelayMeta() func() {
	return func() {
		if r.repo == nil {
			global.GVA_LOG.Error("[task] ResetRangoMetaData swap is nil")
			return
		}
		if err := r.ResetRelayMetaData(); err != nil {
			global.GVA_LOG.Error("[task] ResetRangoMetaData ", zap.Any("err", err))
		}
	}
}

func (r *swapTask) ResetRelayMetaData() error {

	return nil
}

package swap

import (
	"context"
	"fmt"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/cache"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/swap"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/rango"
	"go.uber.org/zap"
)

type swapTask struct {
	repo repo.SwapRepo
}

func NewSwapTask(repo repo.SwapRepo) service.SwapTaskI {
	return &swapTask{
		repo: repo,
	}
}

func (r *swapTask) SyncRangoMeta() func() {
	return func() {
		if r.repo == nil {
			global.GVA_LOG.Error("[task] ResetRangoMetaData swap is nil")
			return
		}
		if err := r.ResetRangoMetaData(); err != nil {
			global.GVA_LOG.Error("[task] ResetRangoMetaData ", zap.Any("err", err))
		}
	}
}

func (r *swapTask) ResetRangoMetaData() error {
	result, err := rango.GetExchangeMeta()
	if err != nil {
		return fmt.Errorf("ResetRangoMetaData get err:%v", err)
	}
	if len(result.Tokens) == 0 {
		return fmt.Errorf("ResetRangoMetaData err: token is empty")
	}
	if len(result.PopularTokens) == 0 {
		return fmt.Errorf("ResetRangoMetaData err: token is empty")
	}
	if len(result.Blockchains) == 0 {
		return fmt.Errorf("ResetRangoMetaData err: blockchain is empty")
	}
	if len(result.Swappers) == 0 {
		return fmt.Errorf("ResetRangoMetaData err: swap is empty")
	}
	chains, err := swap.ChainsAssign(result.Blockchains)
	if err != nil {
		return fmt.Errorf("QueryRango Tokens err:%v", err)
	}
	tokens, err := swap.TokenAssign(result.Tokens)
	if err != nil {
		return fmt.Errorf("QueryRango Tokens err:%v", err)
	}
	swappers, err := swap.SwappersAssign(result.Swappers)
	if err != nil {
		return fmt.Errorf("QueryRangoMeta Swappers err:%v", err)
	}
	err = r.repo.SaveRangoMetaData(chains, tokens, swappers)
	if err != nil {
		return fmt.Errorf("ResetRangoMetaData save err:%v", err)
	}
	r.repo.GetRangoChains()
	key := cache.KeyRangoMetaTokens()
	global.GVA_REDIS.Del(context.Background(), key).Result()
	r.repo.GetSupportTokens()
	return nil
}

package nats

type UserBalanceData struct {
	Timestamp          int64   `json:"ts"`
	UserID             string  `json:"userId"`
	WalletAddress      string  `json:"walletAddress"`
	WalletType         string  `json:"walletType"`
	NativeTokenAddress string  `json:"nativeTokenAddress"`
	NativeTokenSymbol  string  `json:"nativeTokenSymbol"`
	NativeTokenBalance float64 `json:"nativeTokenBalance"` // Representing Decimal as string
	USDBalance         float64 `json:"usdBalance"`
}

type UserBalanceEventMsg struct {
	Events []UserBalanceData `json:"events"`
}

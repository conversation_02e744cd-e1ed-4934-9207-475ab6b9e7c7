package constant

import "time"

const (
	// TenMinuteCacheTime default cache time
	TenSecondCacheTime    = 10 * time.Second
	ThirtySecondCacheTime = 30 * time.Second
	FiveMinuteCacheTime   = 5 * 60 * time.Second
	TenMinuteCacheTime    = 10 * 60 * time.Second
	OneHourCacheTime      = 60 * 60 * time.Second
	Prefix                = "::xbit::"
	Last50Cache           = 50 //In order to load fast, manually adjust to 50(old 1000)
	MaxQueryParams        = 30000
	QueryBatchSize        = 3000
	SmallQueryBatchSize   = 200
	MidCreateSize         = 500
)

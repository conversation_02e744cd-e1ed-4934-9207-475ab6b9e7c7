package cache

import "fmt"

const (
	KeySKeyRangoMetaChainsPrefix = "xBitDex::api::KeyRangoMetaChains::"
	KeyRangoMetaSwappersPrefix   = "xBitDex::api::KeyKeyRangoMetaSwappers::"
	KeyRangoMetaTokensPrefix     = "xBitDex::api::KeyKeyRangoMetaTokens::"
	KeyRangoConfirmedDataPrefix  = "xBitDex::api::KeyRangoConfirmedDataPrefix::"
	KeyRangoCheckStatusPrefix    = "xBitDex::api::KeyRangoStatusPrefix::"

	KeyRelayChainsPrefix = "xBitDex::api::KeyRelayMetaChainsPrefix::"
	KeyRelayTokensPrefix = "xBitDex::api::KeyRelayMetaTokensPrefix::"

	KeySymbolListByVolumePrefix        = "xBitDex::api::SymbolListOrderByVolume::"
	KeySymbolListByGainerPrefix        = "xBitDex::api::SymbolListOrderByGainer::"
	KeySymbolListByLoserPrefix         = "xBitDex::api::KeySymbolListByLoser::"
	KeySymbolListByTrendPrefix         = "xBitDex::api::KeySymbolListByTrend::"
	KeySymbolListByOpenInterrestPrefix = "xBitDex::api::SymbolListOrderByOpenInterrest::"
	KeySymbolListByMarketCapPrefix     = "xBitDex::api::SymbolListOrderByMarketCap::"
	KeySymbolListByCategoryPrefix      = "xBitDex::api::SymbolListOrderByCategory::"

	KeySymbolOverallDataPrefix   = "xBitDex::api::SymbolOverallData::"
	KeySymbolStatisticDataPrefix = "xBitDex::api::SymbolStatisticData::"

	KeyNewSymbolPrefix     = "xBitDex::api::NewSymbol::"
	KeyPopularSymbolPrefix = "xBitDex::api::PopularSymbol::"

	KeyAlertSettingBySymbolPrefix = "xBitDex::api::AlertSettingBySymbol::"
)

func KeyRelayMetaChains() string {
	return fmt.Sprintf(KeyRelayChainsPrefix)
}
func KeyRelayMetaTokens() string {
	return fmt.Sprintf(KeyRelayTokensPrefix)
}

func KeyRangoMetaChains() string {
	return fmt.Sprintf(KeySKeyRangoMetaChainsPrefix)
}
func KeyRangoMetaSwappers() string {
	return fmt.Sprintf(KeyRangoMetaSwappersPrefix)
}
func KeyRangoMetaTokens() string {
	return fmt.Sprintf(KeyRangoMetaTokensPrefix)
}
func KeyRangoConfirmedData(requireId string) string {
	return fmt.Sprintf("%s%s", KeyRangoConfirmedDataPrefix, requireId)
}

func KeyRangoCheckStatus(requestId string, step int64) string {
	return fmt.Sprintf("%s::requsetId::%sstep::%d", KeyRangoCheckStatusPrefix, requestId, step)
}
func KeySymbolListByVolume() string {
	return fmt.Sprintf(KeySymbolListByVolumePrefix)
}
func KeySymbolListByGainer() string {
	return fmt.Sprintf(KeySymbolListByGainerPrefix)
}
func KeySymbolListByLoser() string {
	return fmt.Sprintf(KeySymbolListByLoserPrefix)
}
func KeySymbolListByTrend() string {
	return fmt.Sprintf(KeySymbolListByTrendPrefix)
}

func KeySymbolListByOpenInterest() string {
	return fmt.Sprintf(KeySymbolListByOpenInterrestPrefix)
}

func KeySymbolListByMarketCap() string {
	return fmt.Sprintf(KeySymbolListByMarketCapPrefix)
}

func KeySymbolListByCategory() string {
	return fmt.Sprintf(KeySymbolListByCategoryPrefix)
}

func KeySymbolOverallData() string {
	return fmt.Sprintf(KeySymbolOverallDataPrefix)
}

func KeySymbolStatisticData() string {
	return fmt.Sprintf(KeySymbolStatisticDataPrefix)
}

func KeyNewSymbol() string {
	return fmt.Sprintf(KeyNewSymbolPrefix)
}

func KeyPopularSymbol() string {
	return fmt.Sprintf(KeyPopularSymbolPrefix)
}

func KeyAlertSettingBySymbol() string {
	return fmt.Sprintf(KeyAlertSettingBySymbolPrefix)
}

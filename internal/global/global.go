package global

import (
	"fmt"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/nats"
	"sync"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	"github.com/qiniu/qmgo"
	"github.com/redis/go-redis/v9"

	"github.com/gin-gonic/gin"

	"github.com/songzhibin97/gkit/cache/local_cache"

	"golang.org/x/sync/singleflight"

	"go.uber.org/zap"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/config"

	"github.com/spf13/viper"
	"gorm.io/gorm"
)

var (
	GVA_DB         *gorm.DB
	GVA_CLICKHOUSE *gorm.DB
	GVA_DBList     map[string]*gorm.DB
	GVA_REDIS      redis.UniversalClient
	GVA_REDISList  map[string]redis.UniversalClient
	GVA_CONFIG     config.Server
	GVA_MONGO      *qmgo.QmgoClient
	GVA_VP         *viper.Viper
	GVA_MQTT       *mqtt.Client
	// GVA_LOG    *oplogging.Logger
	GVA_LOG                       *zap.Logger
	GVA_Concurrency_Control       = &singleflight.Group{}
	GVA_ROUTERS                   gin.RoutesInfo
	GVA_ACTIVE_DBNAME             *string
	BlackCache                    local_cache.Cache
	lock                          sync.RWMutex
	GVA_HYPERLIQUID_URL           string
	GVA_HYPERLIQUID_INFO_ENDPOINT string
	GVA_NATS_MEME                 *nats.NATSClient
	GVA_NATS_DEX                  *nats.NATSClient
)

// GetGlobalDBByDBName get list db by dbname
func GetGlobalDBByDBName(dbname string) *gorm.DB {
	lock.RLock()
	defer lock.RUnlock()
	return GVA_DBList[dbname]
}

// MustGetGlobalDBByDBName get list db by dbname,if not exist will panic
func MustGetGlobalDBByDBName(dbname string) *gorm.DB {
	lock.RLock()
	defer lock.RUnlock()
	db, ok := GVA_DBList[dbname]
	if !ok || db == nil {
		panic("db no init")
	}
	return db
}

func GetRedis(name string) redis.UniversalClient {
	redis, ok := GVA_REDISList[name]
	if !ok || redis == nil {
		panic(fmt.Sprintf("redis `%s` no init", name))
	}
	return redis
}

package request

import (
	"fmt"

	"github.com/google/uuid"
)

type ReqOH<PERSON> struct {
	Symbol    string `form:"symbol" binding:"required"`
	Interval  string `form:"interval" binding:"required"` // 1m, 5m, 15m, 1h, 4h, 1d
	Timestamp int64  `form:"timestamp" binding:"required"`
	IsForward bool   `form:"isForward" binding:"required"`
	Limit     int    `form:"limit" binding:"required"`
}

func (r *ReqOHLC) ValidateBasic() error {
	enumToShort := map[string]string{
		"ONE_MINUTE":      "1m",
		"FIVE_MINUTES":    "5m",
		"FIFTEEN_MINUTES": "15m",
		"THIRTY_MINUTES":  "30m",
		"ONE_HOUR":        "1h",
		"TWO_HOURS":       "2h",
		"FOUR_HOURS":      "4h",
		"EIGHT_HOURS":     "8h",
		"TWELVE_HOURS":    "12h",
		"ONE_DAY":         "1d",
		"THREE_DAYS":      "3d",
		"ONE_WEEK":        "1w",
		"ONE_MONTH":       "1M",
		"THREE_MONTHS":    "3m",
	}

	if _, exists := enumToShort[r.Interval]; !exists {
		return fmt.Errorf("invalid interval: %s", r.Interval)
	}
	return nil
}

type ReqSymbolList struct {
	Condition string `form:"condition" binding:"required"`
	Category  string `form:"category"`
}

func (r *ReqSymbolList) ValidateBasic() error {
	if r.Condition == "category" && r.Category == "" {
		return fmt.Errorf("from category is required")
	}
	return nil
}

type ReqGetSymbolDetail struct {
	Symbol string `form:"symbol" binding:"required"`
}

type ReqUpsertFavoriteSymbol struct {
	UserID     uuid.UUID `form:"user_id" binding:"required"`
	Symbol     []string  `form:"symbol" binding:"required"`
	IsFavorite bool      `form:"isFavorite" binding:"required"`
}

type ReqGetFavoriteSymbols struct {
	UserID uuid.UUID `form:"user_id" binding:"required"`
}

type GetUserSymbolPreferenceDto struct {
	UserID uuid.UUID `json:"user_id" binding:"required"`
	Symbol string    `json:"symbol" binding:"required"`
}

type UpsertUserSymbolPreferenceDto struct {
	UserID          uuid.UUID `json:"user_id" binding:"required"`
	Symbol          string    `json:"symbol" binding:"required"`
	IsCross         *bool     `json:"is_cross,omitempty"`
	Leverage        *int      `json:"leverage,omitempty"`
	OrderUnitInBase *bool     `json:"order_unit_in_base,omitempty"`
	TPSLUnit        *string   `json:"tpsl_unit,omitempty"`
}

type ReqGetAlertSymbolsSetting struct {
	UserID uuid.UUID `form:"user_id" binding:"required"`
}

type ReqCreateAlertSymbolSetting struct {
	UserID         uuid.UUID `form:"user_id" binding:"required"`
	Symbol         string    `form:"symbol" binding:"required"`
	Type           string    `form:"type" binding:"required"`
	Value          float64   `json:"value"`
	IsReminderOnce bool      `json:"isReminderOnce"`
	IsActivated    bool      `json:"isActivated"`
	Note           string
}

type ReqUpdateAlertSymbolSetting struct {
	UserID      uuid.UUID `form:"user_id" binding:"required"`
	SettingID   uuid.UUID `form:"setting_id" binding:"required"`
	IsActivated bool      `json:"isActivated"`
}
type ReqDeleteAlertSymbolSetting struct {
	SettingsID []uuid.UUID `json:"settings_ids"`
}
type ReqSignals struct {
	User   string `json:"user"`
	Offset int    `json:"offset"`
	Limit  int    `json:"limit"`
}

package request

import (
	"errors"
	"fmt"
	"strconv"
)

type ReqReset struct {
	OpHash string `json:"opHash"`
}

var (
	ErrInvalidSelectedWallets       = errors.New("invalid selected wallets")
	ErrInvalidBlockchain            = errors.New("invalid blockchain")
	ErrInvalidAddress               = errors.New("invalid address")
	ErrInvalidDestinationBlockchain = errors.New("invalid destination blockchain")
	ErrInvalidDestinationAddress    = errors.New("invalid destination address")
	ErrInvalidRequestId             = errors.New("invalid request id")
	ErrHistoryNotFound              = errors.New("history not found")
	ErrInvalidPage                  = errors.New("invalid page")
	ErrInvalidPageSize              = errors.New("invalid page size")
	ErrInvalidTimeRange             = errors.New("invalid time range")
)

type ReqGetQuote struct {
	User          string `json:"user"`
	OriginId      string `json:"originId"`
	DestinationId string `json:"destinationId"`
	Recipient     string `json:"recipient"`
	Amount        string `json:"amount"`
	Type          string `json:"type"`
}

// ValidateBasic validates the request parameters
func (r *ReqGetQuote) ValidateBasic() error {
	if r.Amount == "" {
		return fmt.Errorf("amount is required")
	}
	return nil
}

// ReqGetBestRoute represents the request structure for getting the best route
type ReqGetBestRoute struct {
	FromBlockChain   string `json:"fromBlockChain" binding:"required"`
	FromSymbol       string `json:"fromSymbol" binding:"required"`
	FromAddress      string `json:"fromAddress" binding:"required"`
	FromTokenAddress string `json:"fromTokenAddress" `
	ToBlockChain     string `json:"toBlockChain" binding:"required"`
	ToSymbol         string `json:"toSymbol" binding:"required"`
	ToTokenAddress   string `json:"toTokenAddress" `
	ToAddress        string `json:"toAddress" binding:"required"`
	Amount           string `json:"amount" binding:"required"`
}

// ValidateBasic validates the request parameters
func (r *ReqGetBestRoute) ValidateBasic() error {
	if r.FromBlockChain == "" {
		return fmt.Errorf("from blockchain is required")
	}
	if r.FromSymbol == "" {
		return fmt.Errorf("from symbol is required")
	}
	if r.FromAddress == "" {
		return fmt.Errorf("from address is required")
	}
	if r.ToBlockChain == "" {
		return fmt.Errorf("to blockchain is required")
	}
	if r.ToSymbol == "" {
		return fmt.Errorf("to symbol is required")
	}
	if r.ToAddress == "" {
		return fmt.Errorf("to address is required")
	}
	if r.Amount == "" {
		return fmt.Errorf("amount is required")
	}
	return nil
}

type ReqGetAllPossibleRoutes struct {
	FromBlockchain   string `json:"fromBlockchain"`
	FromSymbol       string `json:"fromSymbol"`
	FromTokenAddress string `json:"fromTokenAddress"`
	ToBlockchain     string `json:"toBlockchain"`
	ToSymbol         string `json:"toSymbol"`
	ToTokenAddress   string `json:"toTokenAddress"`
	Amount           string `json:"amount"`
	Slippage         string `json:"slippage"`

	User                string `json:"user"`
	OriginChainId       int64  `json:"originChainId"`
	DestinationChainId  int64  `json:"destinationChainId"`
	OriginCurrency      string `json:"originCurrency"`
	DestinationCurrency string `json:"destinationCurrency"`
	Recipient           string `json:"recipient"`
	TradeType           string `json:"tradeType"`
	//Amount               string `json:"amount"`
	Referrer             string `json:"referrer"`
	UseExternalLiquidity bool   `json:"useExternalLiquidity"`
	UseDepositAddress    bool   `json:"useDepositAddress"`
	TopupGas             bool   `json:"topupGas"`
}

// ValidateBasic validates the request parameters
func (r *ReqGetAllPossibleRoutes) ValidateBasic() error {
	if r.FromBlockchain == "" {
		return fmt.Errorf("from blockchain is required")
	}
	if r.FromSymbol == "" {
		return fmt.Errorf("from symbol is required")
	}
	if r.ToBlockchain == "" {
		return fmt.Errorf("to blockchain is required")
	}
	if r.ToSymbol == "" {
		return fmt.Errorf("to symbol is required")
	}
	if r.Amount == "" {
		return fmt.Errorf("amount is required")
	}

	amount, err := strconv.ParseFloat(r.Amount, 64)
	if err != nil {
		return fmt.Errorf("invalid amount format: %v", err)
	}
	if amount <= 0 {
		return fmt.Errorf("amount must be greater than 0")
	}

	if r.Slippage != "" {
		slippage, err := strconv.ParseFloat(r.Slippage, 64)
		if err != nil {
			return fmt.Errorf("invalid slippage format: %v", err)
		}
		if slippage < 0 || slippage > 100 {
			return fmt.Errorf("slippage must be between 0 and 100")
		}
	}

	return nil
}

type SelectedWallet struct {
	Blockchain string `json:"blockchain"`
	Address    string `json:"address"`
}

// ReqConfirmRoute represents the request structure for confirming a route
type ReqConfirmRoute struct {
	SelectedWallets []SelectedWallet `json:"selectedWallets"`
	Destination     string           `json:"destination"`
	RequestId       string           `json:"requestId"`
}

// ValidateBasic validates the request parameters
func (req *ReqConfirmRoute) ValidateBasic() error {
	if len(req.SelectedWallets) == 0 {
		return ErrInvalidSelectedWallets
	}
	for _, wallet := range req.SelectedWallets {
		if wallet.Blockchain == "" {
			return ErrInvalidBlockchain
		}
		if wallet.Address == "" {
			return ErrInvalidAddress
		}
	}

	//if req.Destination == "" {
	//	return ErrInvalidDestinationAddress
	//}
	if req.RequestId == "" {
		return ErrInvalidRequestId
	}
	return nil
}

// ReqRangoHistory represents the request structure for getting user's swap history
type ReqRangoHistory struct {
	Address    string `json:"address"`
	Blockchain string `json:"blockchain"`
	Page       int64  `json:"page"`
	PageSize   int64  `json:"pageSize"`
	StartTime  int64  `json:"startTime,omitempty"`
	EndTime    int64  `json:"endTime,omitempty"`
	Status     string `json:"status,omitempty"`
}

// ValidateBasic validates the request parameters
func (req *ReqRangoHistory) ValidateBasic() error {
	if req.Address == "" {
		return ErrInvalidAddress
	}
	if req.Blockchain == "" {
		return ErrInvalidBlockchain
	}
	if req.Page <= 0 {
		return ErrInvalidPage
	}
	if req.PageSize <= 0 {
		return ErrInvalidPageSize
	}
	if req.StartTime > 0 && req.EndTime > 0 && req.StartTime > req.EndTime {
		return ErrInvalidTimeRange
	}
	return nil
}

type History struct {
	RequestId      string `json:"requestId"`
	UserAddress    string `json:"userAddress"`
	FromBlockchain string `json:"fromBlockchain"`
	ToBlockchain   string `json:"toBlockchain"`
	FromSymbol     string `json:"fromSymbol"`
	ToSymbol       string `json:"toSymbol"`
	FromAmount     string `json:"fromAmount"`
	ToAmount       string `json:"toAmount"`
	Status         string `json:"status"`
	TxHash         string `json:"txHash,omitempty"`
	OpHash         string `json:"opHash,omitempty"`
	FailReason     string `json:"failReason,omitempty"`
	CreatedAt      int64  `json:"createdAt"`
	UpdatedAt      int64  `json:"updatedAt"`
}

// ReqCheckStatus represents the request structure for checking transaction status
type ReqCheckStatus struct {
	RequestId string `json:"requestId" binding:"required"`
	TxHash    string `json:"txHash" binding:"required"`
	Step      int64  `json:"step" binding:"required"`
}

// ValidateBasic validates the request parameters
func (r *ReqCheckStatus) ValidateBasic() error {
	if r.RequestId == "" {
		return ErrInvalidRequestId
	}
	if r.TxHash == "" {
		return fmt.Errorf("txHash is required")
	}
	if r.Step < 0 {
		return fmt.Errorf("step must be greater than or equal to 0")
	}
	return nil
}

// ReqCheckApproval represents the request structure for checking approval status
type ReqCheckApproval struct {
	RequestId string `json:"requestId" binding:"required"`
	TxHash    string `json:"txHash" binding:"required"`
}

// ValidateBasic validates the request parameters
func (r *ReqCheckApproval) ValidateBasic() error {
	if r.RequestId == "" {
		return ErrInvalidRequestId
	}
	if r.TxHash == "" {
		return fmt.Errorf("txHash is required")
	}
	return nil
}

type Validations struct {
	Balance bool `json:"balance"`
	Fee     bool `json:"fee"`
	Approve bool `json:"approve"`
}

type UserSettings struct {
	Slippage        float64 `json:"slippage"`
	InfiniteApprove bool    `json:"infiniteApprove"`
}

// ReqCreateTx represents the request structure for creating a transaction
type ReqCreateTx struct {
	RequestId    string       `json:"requestId" binding:"required"`
	Step         int64        `json:"step" binding:"required"`
	UserSettings UserSettings `json:"userSettings" binding:"required"`
	Validations  Validations  `json:"validations" binding:"required"`
}

// ValidateBasic validates the request parameters
func (r *ReqCreateTx) ValidateBasic() error {
	if r.RequestId == "" {
		return ErrInvalidRequestId
	}
	return nil
}

type ReqSignTx struct {
	RequestId string `json:"requestId" binding:"required"`
	Step      int64  `json:"step" binding:"required"`
}

// ValidateBasic validates the request parameters
func (r *ReqSignTx) ValidateBasic() error {
	if r.RequestId == "" {
		return ErrInvalidRequestId
	}
	return nil
}

type ReqCallBack struct {
	RequestId string `json:"requestId" binding:"required"`
	Step      int64  `json:"step" binding:"required"`
	TxHash    string `json:"txHash" binding:"required"`
}

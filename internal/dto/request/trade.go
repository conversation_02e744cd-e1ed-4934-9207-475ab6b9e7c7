package request

import "github.com/google/uuid"

type ReqGetUserOpenOrder struct {
	UserID uuid.UUID `form:"user_id" binding:"required"`
	LastID uuid.UUID `form:"last_id" binding:"required"`
	Limit  int       `form:"limit" binding:"required"`
}

type ReqGetUserPosition struct {
	UserID        uuid.UUID `form:"user_id" binding:"required"`
	WalletAddress string    `form:"wallet_address" binding:"required"`
}

type ReqGetUserTradeHistory struct {
	UserID        uuid.UUID `form:"user_id" binding:"required"`
	WalletAddress string    `form:"wallet_address" binding:"required"`
}

type ReqStoreTxInformation struct {
	ID           uuid.UUID `form:"id" binding:"required"`
	UserID       uuid.UUID `form:"user_id" binding:"required"`
	OID          uuid.UUID `form:"oid" binding:"required"`
	Type         string    `form:"type" binding:"required"`
	Symbol       string    `form:"symbol" binding:"required"`
	Direction    string    `form:"direction" binding:"required"`
	Status       string    `form:"status" binding:"required"`
	Size         float64   `form:"size"`
	OriginSize   float64   `form:"origin_size" binding:"required"`
	OrderPx      float64   `form:"order_px" binding:"required"`
	TriggerPx    *float64  `form:"trigger_px"`
	ReduceOnly   bool      `form:"reduce_only"`
	VaultAddress *string   `form:"vault_address"`
	ExpiresAfter *int64    `form:"expires_after"`
}

type ReqGetUserPrevDayBalance struct {
	UserID        uuid.UUID `form:"user_id" binding:"required"`
	WalletAddress string    `form:"wallet_address" binding:"required"`
}

type ReqCallbackUserBalance struct {
	UserID        uuid.UUID `form:"user_id" binding:"required"`
	WalletAddress string    `form:"wallet_address" binding:"required"`
}

package response

import "time"

type RespGetUserOrderStatus struct {
	Orders []OrderStatus `json:"orders"`
}
type OrderStatus struct {
	Oid              int64     `json:"oid"`
	CreatedAt        time.Time `json:"createdAt"`
	Time             string    `json:"time"`
	User             string    `json:"user"`
	Status           string    `json:"status"`
	UserId           *string   `json:"userId"`
	Cloid            string    `json:"cloid"`
	Coin             string    `json:"coin"`
	Side             string    `json:"side"`
	LimitPx          string    `json:"limitPx"`
	Sz               string    `json:"sz"`
	Timestamp        int64     `json:"timestamp"`
	TriggerCondition string    `json:"triggerCondition"`
	IsTrigger        bool      `json:"isTrigger"`
	TriggerPx        string    `json:"triggerPx"`
	Children         string    `json:"children"`
	IsPositionTpsl   bool      `json:"isPositionTpsl"`
	ReduceOnly       bool      `json:"reduceOnly"`
	OrderType        string    `json:"orderType"`
	OrigSz           string    `json:"origSz"`
	Tif              string    `json:"tif"`
}

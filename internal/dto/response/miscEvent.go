package response

type RespGetUserEvents struct {
	Events []MiscEvent `json:"events"`
}
type MiscEvent struct {
	Time                   string `json:"time"`
	Hash                   string `json:"hash"`
	Users                  string `json:"users"`
	CDepositUser           string `json:"c"`
	CDepositAmount         string `json:"cDepositAmount"`
	CWithdrawalUser        string `json:"cWithdrawalUser"`
	CWithdrawalAmount      string `json:"cWithdrawalAmount"`
	CWithdrawalIsFinalized bool   `json:"cWithdrawalIsFinalized"`
	Type                   string `json:"type"`
	Usdc                   string `json:"usdc"`
	ToPerp                 bool   `json:"toPerp"`
	Token                  string `json:"token"`
	Amount                 string `json:"amount"`
	UsdcValue              string `json:"usdcValue"`
	User                   string `json:"user"`
	UserId                 string `json:"userId"`
	Destination            string `json:"destination"`
	Fee                    string `json:"fee"`
	NativeTokenFee         string `json:"nativeTokenFee"`
	Nonce                  int64  `json:"nonce"`
}

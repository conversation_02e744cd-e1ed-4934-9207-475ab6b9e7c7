package response

type OHLC struct {
	Timestamp int64   `json:"timestamp"`
	Open      float64 `json:"open"`
	High      float64 `json:"high"`
	Low       float64 `json:"low"`
	Close     float64 `json:"close"`
	Volume    float64 `json:"volume"`
}

type RespOHLC struct {
	Symbol string `json:"symbol"`
	Data   []OHLC `json:"data"`
}

type Symbol struct {
	Symbol         string  `json:"symbol"`
	MaxLeverage    int     `json:"max_leverage"`
	MarketCap      float64 `json:"market_cap"`
	Volume         float64 `json:"volume"`
	OpenInterest   float64 `json:"open_interest"`
	CurrentPrice   float64 `json:"current_price"`
	ChangPxPercent float64 `json:"change_px"`
}

type RespSymbolList struct {
	List []Symbol `json:"list"`
}

type RespGetSymbolDetail struct {
	Symbol          string  `json:"symbol"`
	MaxLeverage     int     `json:"max_leverage"`
	OnlyIsolated    bool    `json:"only_isolated"`
	SizeDecimals    int     `json:"size_decimals"`
	MarginTableID   int     `json:"margin_table_id"`
	MarketCap       float64 `json:"market_cap"`
	High            float64 `json:"high"`
	Low             float64 `json:"low"`
	Volume          float64 `json:"volume"`
	ChangePxPercent float64 `json:"change_px_percent"`
	CurrentPrice    float64 `json:"current_price"`
}

type RespUpsertFavoriteSymbol struct {
	Status string `json:"status"`
	Error  string `json:"error,omitempty"`
}

type RespGetFavoriteSymbols struct {
	List []Symbol `json:"list"`
}

type RespGetCategory struct {
	Categories []string `json:"categories"`
}

type RespUserSymbolPreference struct {
	Symbol          string `json:"symbol"`
	IsFavorite      bool   `json:"is_favorite"`
	IsCross         bool   `json:"is_cross"`
	Leverage        int    `json:"leverage"`
	OrderUnitInBase bool   `json:"order_unit_in_base"`
	TPSLUnit        string `json:"tpsl_unit"`
}

type RespSearchSymbol struct {
	List []Symbol `json:"list"`
}

type RespGetNewSymbol struct {
	List []string `json:"list"`
}

type RespGetPopularSymbol struct {
	List []Symbol `json:"list"`
}

type AlertSymbolSetting struct {
	SettingID      string  `json:"setting_id"`
	Symbol         string  `json:"symbol"`
	Type           string  `json:"type"`
	TriggerValue   float64 `json:"trigger_value"`
	IsActive       bool    `json:"is_active"`
	IsReminderOnce bool    `json:"is_reminder_once"`
}
type RespGetAlertSymbolsSetting struct {
	Settings []AlertSymbolSetting `json:"settings"`
}

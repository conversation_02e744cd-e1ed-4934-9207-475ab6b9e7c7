package response

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/gagliardetto/solana-go"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	swap2 "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/swap"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/relay"
)

type RespQuote struct {
	Items                 []StepItem `json:"items"`
	RequestId             string     `json:"requestId"`
	Type                  string     `json:"type"`
	Description           string     `json:"description"`
	OutPutAmountFormatted string     `json:"outPutAmountFormatted"`
}

type StepItem struct {
	From                 string `json:"from"`
	To                   string `json:"to"`
	Data                 string `json:"data"`
	Value                string `json:"value"`
	ValueAmountUsd       string `json:"valueAmountUsd"`
	ValueAmountFormatted string `json:"valueAmountFormatted"`
	ChainId              string `json:"chainId"`
	Gas                  string `json:"gas"`
	GasAmountUsd         string `json:"gasAmountUsd"`
	MaxFeePerGas         string `json:"maxFeePerGas"`
	MaxPriorityFeePerGas string `json:"maxPriorityFeePerGas"`
	Nonce                string `json:"nonce"`
	BlockHash            string `json:"blockHash,omitempty"` // optional, used for solana transactions
	// no evm
	Instructions      *[]*Instruction `json:"instructions"`
	SerializedMessage []int           `json:"serializedMessage"`

	TransactionType        string `json:"transactionType"`        // optional, used for evm transactions
	EstimatedTimeInSeconds int64  `json:"estimatedTimeInSeconds"` // optional, used for evm transactions
	//relay
	GasTopupAmount          string `json:"amount"`
	GasTopupAmountFormatted string `json:"amountFormatted"`
	GasTopupAmountUsd       string `json:"amountUsd"`
}
type Instruction struct {
	Keys      []InstructionKey `json:"keys"`
	ProgramId string           `json:"programId"`
	Data      string           `json:"data"`
}

type InstructionKey struct {
	Pubkey     string `json:"pubkey"`
	IsSigner   bool   `json:"isSigner"`
	IsWritable bool   `json:"isWritable"`
}

func (resp *RespQuote) RlAssign(
	data *relay.QuoteResponse, nonce uint64, blockHash *solana.Hash, originChain *model.RelayMetaChains) error {
	if len(data.Steps) == 0 || len(data.Steps[0].Items) == 0 {
		return fmt.Errorf("no steps or items in response")
	}
	var (
		items                 []StepItem
		blockHashStr          string
		outPutAmountFormatted string
	)
	if blockHash != nil {
		blockHashStr = blockHash.String()
	}
	if data.Details.TimeEstimate == 0 {
		data.Details.TimeEstimate = 5
	}
	for _, item := range data.Steps[0].Items {
		outPutAmountFormatted = data.Details.CurrencyOut.AmountFormatted
		instructions := utils.Translate[[]*Instruction](item.Data.Instructions)
		_item := StepItem{
			From:                 item.Data.From,
			To:                   item.Data.To,
			Data:                 item.Data.Data,
			Value:                item.Data.Value,
			ValueAmountUsd:       data.Details.CurrencyOut.AmountUsd,
			ValueAmountFormatted: data.Details.CurrencyOut.AmountFormatted,
			ChainId:              originChain.ChainId,
			Gas:                  item.Data.Gas,
			GasAmountUsd:         data.Fees.Gas.AmountUsd,
			MaxFeePerGas:         item.Data.MaxFeePerGas,
			MaxPriorityFeePerGas: item.Data.MaxPriorityFeePerGas,

			Nonce:                   fmt.Sprintf("%d", nonce),
			BlockHash:               blockHashStr,
			Instructions:            instructions,
			TransactionType:         originChain.Name,
			EstimatedTimeInSeconds:  int64(data.Details.TimeEstimate),
			GasTopupAmount:          data.Details.CurrencyGasTopup.Amount,
			GasTopupAmountFormatted: data.Details.CurrencyGasTopup.AmountFormatted,
			GasTopupAmountUsd:       data.Details.CurrencyGasTopup.AmountUsd,
		}
		if _item.From == "" {
			_item.From = data.Details.Sender
			_item.To = data.Details.Recipient

			_item.Value = data.Details.CurrencyOut.Amount
			_item.ValueAmountFormatted = data.Details.CurrencyOut.AmountFormatted
			_item.ValueAmountUsd = data.Details.CurrencyOut.AmountUsd
			_item.ChainId = originChain.ChainIdHex
			if originChain.ChainId == "792703809" { //solana
				_item.ChainId = "501424"
			}
		}
		items = append(items, _item)
	}
	*resp = RespQuote{
		Items:                 items,
		RequestId:             data.Steps[0].RequestId,
		OutPutAmountFormatted: outPutAmountFormatted,
	}
	return nil
}

type Chain struct {
	ChainId    string `json:"chainId"`
	ChainImage string `json:"chainImage"`
	ChainName  string `json:"chainName"`

	Tokens []*TokenV2 `json:"tokens"`
}
type TokenV2 struct {
	Address    string           `json:"address"`
	Symbol     string           `json:"symbol"`
	Name       string           `json:"name"`
	Image      string           `json:"image"`
	Decimals   int              `json:"decimals"`
	UsdPrice   float64          `json:"usdPrice"`
	RelayExtra *RelayTokenExtra `json:"relayExtra,omitempty"`
	RangoExtra *RangoTokenExtra `json:"rangoExtra,omitempty"`
}
type RelayTokenExtra struct {
	Id string `json:"id"`
}

func (t *RelayTokenExtra) Assign(
	token *model.RelayMetaTokens) error {
	*t = RelayTokenExtra{
		Id: token.ID.String(),
	}
	return nil
}

type RangoTokenExtra struct {
	Id string `json:"id"`
}

func (t *RangoTokenExtra) Assign(
	token *model.RangoMetaTokens) error {
	*t = RangoTokenExtra{
		Id: token.ID.String(),
	}
	return nil
}

type RespExchangeMetaV2 struct {
	Chains []*Chain `json:"chains"`
}

func (r *RespExchangeMetaV2) Assign(
	rlTokens map[string][]*model.RelayMetaTokens,
	rlChains []*model.RelayMetaChains,

	rgTokens map[string][]*model.RangoMetaTokens,
	rgChains []*model.RangoMetaChains,
) error {
	var (
		chainsFilter = make(map[string]*Chain)
		tokensFilter = make(map[string]*TokenV2)
	)
	for _, chain := range rlChains {
		var (
			chainKey = chain.ChainIdHex
			chainId  = chain.ChainId
		)
		if resetValue, exist := swap2.SpecialChainIdMap[chainKey]; exist {
			chainKey = resetValue
			if resetValue == "SOLANA" {
				chainId = "501424"
			}
		}
		chainsFilter[chainKey] = &Chain{
			ChainId:    chainId,
			ChainImage: chain.IconUrl,
			ChainName:  chain.Name,
			Tokens:     nil,
		}

		for _, token := range rlTokens[chain.ChainIdHex] {
			t := &TokenV2{
				Address:  token.Address,
				Symbol:   token.Symbol,
				Name:     token.Name,
				Image:    token.LogoURI,
				Decimals: token.Decimals,
			}
			t.RelayExtra = &RelayTokenExtra{}
			if err := t.RelayExtra.Assign(token); err != nil {
				return fmt.Errorf("failed to assign relay extra: %w", err)
			}

			tokenKey := fmt.Sprintf("%s-%s", chainKey, token.Symbol)
			if _, exist := tokensFilter[tokenKey]; !exist {
				tokensFilter[tokenKey] = t
				chainsFilter[chainKey].Tokens = append(chainsFilter[chainKey].Tokens, t)
			}
		}
	}
	for _, chain := range rgChains {
		var (
			chainKey = chain.ChainId
			chainId  = chain.ChainId
		)
		chainId = HexToDecimalString(chain.ChainId)
		if resetValue, exist := swap2.SpecialChainIdMap[chainKey]; exist {
			chainKey = resetValue
			if resetValue == "SOLANA" {
				chainId = "501424"
			}
		}
		if _, exist := chainsFilter[chainKey]; !exist {
			chainsFilter[chainKey] = &Chain{
				ChainId:    chainId,
				ChainImage: chain.Logo,
				ChainName:  chain.Name,
				Tokens:     nil,
			}
		}

		for _, token := range rgTokens[chain.ChainId] {
			t := &TokenV2{
				Address:  token.Address,
				Symbol:   token.Symbol,
				Name:     token.Name,
				Image:    token.Image,
				Decimals: int(token.Decimals),
			}
			t.RangoExtra = &RangoTokenExtra{}
			if err := t.RangoExtra.Assign(token); err != nil {
				return fmt.Errorf("failed to assign rango extra: %w", err)
			}
			tokenKey := fmt.Sprintf("%s-%s", chainKey, token.Symbol)
			if _, exist := tokensFilter[tokenKey]; !exist {
				tokensFilter[tokenKey] = t
				chainsFilter[chainKey].Tokens = append(chainsFilter[chainKey].Tokens, t)
			} else {
				if tokensFilter[tokenKey].Image == "" {
					tokensFilter[tokenKey].Image = t.Image
				}
				if tokensFilter[tokenKey].UsdPrice == 0 {
					tokensFilter[tokenKey].UsdPrice = token.UsdPrice
				}
				tokensFilter[tokenKey].RangoExtra = t.RangoExtra
			}
		}
	}
	arr := utils.MapToArr(chainsFilter)
	*r = RespExchangeMetaV2{
		Chains: arr,
	}
	return nil
}
func HexToDecimalString(hexStr string) string {
	s := strings.TrimPrefix(hexStr, "0x")
	if s == "" {
		return ""
	}
	v, err := strconv.ParseInt(s, 16, 64)
	if err != nil {
		return hexStr
	}
	return strconv.FormatInt(v, 10)
}

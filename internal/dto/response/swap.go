package response

import (
	"fmt"
	"time"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
)

type History struct {
	RequestId                         string              `gorm:"request_id"`
	UserAddress                       string              `gorm:"user_address"`
	FromBlockchain                    string              `gorm:"from_blockchain"`
	FromSymbol                        string              `gorm:"from_symbol"`
	FromAddress                       string              `gorm:"from_address"`
	ToBlockchain                      string              `gorm:"to_blockchain"`
	ToSymbol                          string              `gorm:"to_symbol"`
	ToAddress                         string              `gorm:"to_address"`
	RequestAmount                     string              `gorm:"request_amount"`
	OutputAmount                      string              `gorm:"output_amount"`
	ResultType                        string              `gorm:"result_type"`
	ValidationStatus                  []string            `gorm:"validation_status"`
	WalletNotSupportingFromBlockchain bool                `gorm:"wallet_not_supporting_from_blockchain"`
	MissingBlockchains                string              `gorm:"missing_blockchains"`
	DiagnosisMessages                 string              `gorm:"diagnosis_messages"`
	Status                            string              `gorm:"status;index"`
	Step                              int64               `gorm:"step"`
	FailReason                        string              `gorm:"fail_reason"`
	Swaps                             []*model.RangoSwaps `gorm:"foreignKey:RequestId;references:RequestId" json:"swaps"`
}

func (h *History) HistoryAssign(history *model.RangoHistory) error {
	*h = History{
		RequestId:                         history.RequestId,
		UserAddress:                       history.UserAddress,
		FromBlockchain:                    history.FromBlockchain,
		FromSymbol:                        history.FromSymbol,
		FromAddress:                       history.FromAddress,
		ToBlockchain:                      history.ToBlockchain,
		ToSymbol:                          history.ToSymbol,
		ToAddress:                         history.ToAddress,
		RequestAmount:                     history.RequestAmount,
		OutputAmount:                      history.OutputAmount,
		ResultType:                        history.ResultType,
		WalletNotSupportingFromBlockchain: history.WalletNotSupportingFromBlockchain,
		MissingBlockchains:                history.MissingBlockchains,
		DiagnosisMessages:                 history.DiagnosisMessages,
		Status:                            history.Status,
		Step:                              history.Step,
		FailReason:                        history.FailReason,
		Swaps:                             history.Swaps,
	}
	var arr []string
	_, err := utils.ForceJsonUnmarshal([]byte(history.ValidationStatus), &arr)
	if err != nil {
		return fmt.Errorf("fail to unmarshal validation status: %v", err)
	}
	h.ValidationStatus = arr
	return nil
}

// RespHistory represents the response structure for getting user's swap history
type RespHistory struct {
	Total int64     `json:"total"`
	List  []History `json:"list"`
}

func (r *RespHistory) HistoryAssign(t int64, histories []*model.RangoHistory) error {
	for _, history := range histories {
		var h History
		err := h.HistoryAssign(history)
		if err != nil {
			return err
		}
		r.List = append(r.List, h)
	}
	return nil
}

type RespCallBack struct {
	Success string `json:"success"`
}
type Token struct {
	ID                string                 `json:"id"`
	Address           string                 `json:"address"`
	BlockChain        string                 `json:"blockChain"`
	ChainDetail       *model.RangoMetaChains `json:"chainDetail"`
	Symbol            string                 `json:"symbol"`
	Name              string                 `json:"name"`
	Image             string                 `json:"image"`
	UsdPrice          float64                `json:"usdPrice"`
	Decimals          int64                  `json:"decimals"`
	IsPopular         bool                   `json:"isPopular"`
	IsSecondaryCoin   bool                   `json:"isSecondaryCoin"`
	CoinSource        string                 `json:"coinSource"`
	CoinSourceUrl     string                 `json:"coinSourceUrl"`
	SupportedSwappers []string               `json:"supportedSwappers"`
}

func (t *Token) TokenAssign(token *model.RangoMetaTokens, chainMap map[string]*model.RangoMetaChains) error {
	*t = Token{
		ID:              token.ID.String(),
		Address:         token.Address,
		BlockChain:      token.BlockChain,
		ChainDetail:     chainMap[token.BlockChain],
		Symbol:          token.Symbol,
		Name:            token.Name,
		Image:           token.Image,
		UsdPrice:        token.UsdPrice,
		Decimals:        token.Decimals,
		IsPopular:       token.IsPopular,
		IsSecondaryCoin: token.IsSecondaryCoin,
		CoinSource:      token.CoinSource,
		CoinSourceUrl:   token.CoinSourceUrl,
	}
	_, err := utils.ForceJsonUnmarshal([]byte(token.SupportedSwappers), &t.SupportedSwappers)
	if err != nil {
		return fmt.Errorf("failed to unmarshal supported swappers: %v", err)
	}
	return nil
}

type RespExchangeMeta struct {
	Tokens        []*Token                   `json:"tokens"`
	PopularTokens []*model.RangoMetaTokens   `json:"popularTokens"`
	Blockchains   []*model.RangoMetaChains   `json:"blockchains"`
	Swappers      []*model.RangoMetaSwappers `json:"swappers"`
}

func (r *RespExchangeMeta) TokenAssign(
	tokens []*model.RangoMetaTokens,
	chains []*model.RangoMetaChains,
	swppers []*model.RangoMetaSwappers) error {

	var chainMap = make(map[string]*model.RangoMetaChains)
	for i, chain := range chains {
		chainMap[chain.Name] = chains[i]
	}
	// Assign tokens to the response structure
	for _, token := range tokens {
		var t Token
		err := t.TokenAssign(token, chainMap)
		if err != nil {
			return err
		}
		r.Tokens = append(r.Tokens, &t)
		if token.IsPopular {
			r.PopularTokens = append(r.PopularTokens, token)
		}
	}

	r.Blockchains = chains
	r.Swappers = swppers
	return nil
}

// RespConfirmRoute represents the response structure for confirming a route
type RespConfirmRoute struct {
	RequestId string `json:"requestId"`
	Status    string `json:"status"`
	Error     string `json:"error,omitempty"`
}

// RespCheckStatus represents the response structure for checking swap status
type RespCheckStatus struct {
	RequestId      string `json:"requestId"`
	Status         string `json:"status"`
	FromBlockchain string `json:"fromBlockchain"`
	ToBlockchain   string `json:"toBlockchain"`
	FromSymbol     string `json:"fromSymbol"`
	ToSymbol       string `json:"toSymbol"`
	FromAmount     string `json:"fromAmount"`
	ToAmount       string `json:"toAmount"`
	FailReason     string `json:"failReason,omitempty"`
}

type Signal struct {
	Username           string    `json:"username"`
	UUID               string    `json:"uuid"`
	SignalType         string    `json:"signalType"`
	SignalName         string    `json:"signalName"`
	SignalPnl          float64   `json:"signalPnl"`
	InitialCapital     float64   `json:"initialCapital"`
	LatestAssets       float64   `json:"latestAssets"`
	RunningStatus      string    `json:"runningStatus"`
	ConsecutiveWins    int       `json:"consecutiveWins"`
	Subscribers        int       `json:"subscribers"`
	OperationDirection string    `json:"operationDirection"`
	UnderlyingAsset    string    `json:"underlyingAsset"`
	MonthlyReturnRate  float64   `json:"monthlyReturnRate"`
	MonthlyAlpha       float64   `json:"monthlyAlpha"`
	AnnualWinRate      float64   `json:"annualWinRate"`
	CumulativeIncome   float64   `json:"cumulativeIncome"`
	SharpeRatio        float64   `json:"sharpeRatio"`
	ThreeYield         float64   `json:"threeYield"`
	SevenYield         float64   `json:"sevenYield"`
	MaxDrawdown7Days   float64   `json:"maxDrawdown7Days"`
	RunningTime        float64   `json:"runningTime"`
	HistoricalWinRate  float64   `json:"historicalWinRate"`
	ProfitLossCount    float64   `json:"profitLossCount"`
	ProfitLossRatio    float64   `json:"profitLossRatio"`
	Confidence         string    `json:"confidence"`
	EvaluationStatus   string    `json:"evaluationStatus"`
	Review             string    `json:"review"`
	UserID             int       `json:"userId"`
	CreatedDate        time.Time `json:"createdDate"`
	UpdatedDate        time.Time `json:"updatedDate"`
}

func (h *Signal) Assign(signal *model.SymbolSignal) {
	*h = Signal{
		Username:           signal.Username,
		UUID:               signal.UUID,
		SignalType:         signal.SignalType,
		SignalName:         signal.SignalName,
		SignalPnl:          signal.SignalPnl,
		InitialCapital:     signal.InitialCapital,
		LatestAssets:       signal.LatestAssets,
		RunningStatus:      signal.RunningStatus,
		ConsecutiveWins:    signal.ConsecutiveWins,
		Subscribers:        signal.Subscribers,
		OperationDirection: signal.OperationDirection,
		UnderlyingAsset:    signal.UnderlyingAsset,
		MonthlyReturnRate:  signal.MonthlyReturnRate,
		MonthlyAlpha:       signal.MonthlyAlpha,
		AnnualWinRate:      signal.AnnualWinRate,
		CumulativeIncome:   signal.CumulativeIncome,
		SharpeRatio:        signal.SharpeRatio,
		ThreeYield:         signal.ThreeYield,
		SevenYield:         signal.SevenYield,
		MaxDrawdown7Days:   signal.MaxDrawdown7Days,
		RunningTime:        signal.RunningTime,
		HistoricalWinRate:  signal.HistoricalWinRate,
		ProfitLossCount:    signal.ProfitLossCount,
		ProfitLossRatio:    signal.ProfitLossRatio,
		Confidence:         signal.Confidence,
		EvaluationStatus:   signal.EvaluationStatus,
		Review:             signal.Review,
		UserID:             signal.UserID,
		CreatedDate:        signal.CreatedDate,
		UpdatedDate:        signal.UpdatedDate,
	}
}

type RespSignals struct {
	Total   int64    `json:"total"`
	Signals []Signal `json:"signals"`
}

package response

type OpenOrder struct {
	Time       int64   `json:"time"`
	Type       string  `json:"type"`
	Symbol     string  `json:"symbol"`
	Direction  string  `json:"direction"`
	Size       float64 `json:"size"`
	OriginSize float64 `json:"originSize"`
	OrderPx    float64 `json:"orderPx"`
	TriggerPx  float64 `json:"triggerPx"`
	ReduceOnly bool    `json:"reduceOnly"`
}

type RespGetUserOpenOrder struct {
	OpenOrders []OpenOrder `json:"openOrders"`
}

type Leverage struct {
	Type  string `json:"type"`
	Value int    `json:"value"`
}

type Position struct {
	Symbol     string   `json:"symbol"`
	Size       float64  `json:"size"`
	Leverage   Leverage `json:"leverage"`
	Side       int      `json:"side"`
	EntryPx    float64  `json:"entryPx"`
	FundingFee float64  `json:"fundingFee"`
}

type RespGetUserPosition struct {
	RawUsd    float64    `json:"rawUSD"`
	Positions []Position `json:"positions"`
}

type RespStoreTxInformation struct {
	Status string `json:"status"`
	Error  string `json:"error,omitempty"`
}

type TradeHistory struct {
	Symbol        string  `json:"symbol"`
	Time          int64   `json:"time"`
	Pnl           float64 `json:"pnl"`
	PnlPercent    float64 `json:"pnlPercent"`
	Dir           string  `json:"dir"`
	Oid           int64   `json:"oid"`
	Hash          string  `json:"hash"`
	Px            float64 `json:"px"`
	StartPosition string  `json:"startPosition"`
	Sz            float64 `json:"sz"`
	Fee           float64 `json:"fee"`
	FeeToken      string
	Tid           int64 `json:"tid"`
}

type RespGetUserTradeHistory struct {
	Histories []TradeHistory `json:"histories"`
}

type RespGetUserPrevDayBalance struct {
	Balance float64 `json:"balance"`
}

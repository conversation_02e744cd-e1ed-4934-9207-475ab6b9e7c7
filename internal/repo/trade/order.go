package trade

import (
	"github.com/google/uuid"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
)

type OrderRepository struct {
}

func (r *OrderRepository) GetUserOpenOrder(userID uuid.UUID, lastID uuid.UUID, limit int) ([]*model.Order, error) {
	var orders []*model.Order
	err := global.GVA_DB.Model(&model.Order{}).
		Where("user_id = ?", userID).
		Where("id <= ?", lastID).
		Order("id desc").
		Limit(limit).
		Find(&orders).Error
	if err != nil {
		return nil, err
	}

	return orders, nil
}

func (r *OrderRepository) StoreTxInformation(order *model.Order) error {
	return global.GVA_DB.Model(&model.Order{}).Create(order).Error
}

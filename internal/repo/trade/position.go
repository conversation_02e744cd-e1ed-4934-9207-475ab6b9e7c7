package trade

import (
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gorm.io/gorm/clause"
)

type PositionRepository struct {
}

func (r *PositionRepository) GetUserPosition(walletAddress string) ([]*model.Position, error) {
	var positions []*model.Position
	err := global.GVA_DB.Model(&model.Position{}).
		Where("wallet_address = ? and size != 0", walletAddress).
		Order("created_at desc").
		Find(&positions).Error
	if err != nil {
		return nil, err
	}

	return positions, nil
}

func (r *PositionRepository) UpdateUserPosition(positions []*model.Position) error {
	for _, position := range positions {
		err := global.GVA_DB.Model(&model.Position{}).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: "wallet_address"}, {Name: "symbol"}},
				DoUpdates: clause.AssignmentColumns([]string{
					"size",
					"type",
					"entry_px",
					"side",
					"margin_mode",
					"funding_all_time",
					"funding_since_change",
					"update_at",
				}),
			}).Create(position).Error
		if err != nil {
			return err
		}
	}

	return nil
}

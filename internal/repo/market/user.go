package market

import (
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
)

type UserRepository struct {
}

func (r *UserRepository) GetUserByID(id int64) (*model.User, error) {
	var user model.User
	err := global.GVA_DB.First(&user, id).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *UserRepository) GetUserByEmail(email string) (*model.User, error) {
	var user model.User
	err := global.GVA_DB.Where("email = ?", email).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *UserRepository) GetUserByUsername(username string) (*model.User, error) {
	var user model.User
	err := global.GVA_DB.Where("username = ?", username).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *UserRepository) CreateUser(user *model.User) error {
	return global.GVA_DB.Create(user).Error
}

func (r *UserRepository) UpdateUser(user *model.User) error {
	return global.GVA_DB.Save(user).Error
}

func (r *UserRepository) UpdateUserFund(id int64, fund float64) error {
	return global.GVA_DB.Model(&model.User{}).
		Where("id = ?", id).
		Update("fund", fund).Error
}

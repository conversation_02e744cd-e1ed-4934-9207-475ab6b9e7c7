package market

import (
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
)

type VaultPositionRepository struct {
}

func (r *VaultPositionRepository) GetVaultPositions(userID int64, symbol string, page, pageSize int) ([]model.VaultPosition, int64, error) {
	var positions []model.VaultPosition
	var total int64

	query := global.GVA_DB.Model(&model.VaultPosition{}).Where("user_id = ?", userID)
	if symbol != "" {
		query = query.Where("symbol = ?", symbol)
	}

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = query.Offset((page - 1) * pageSize).Limit(pageSize).Find(&positions).Error
	if err != nil {
		return nil, 0, err
	}

	return positions, total, nil
}

func (r *VaultPositionRepository) GetVaultPositionByID(positionID int64) (*model.VaultPosition, error) {
	var position model.VaultPosition
	err := global.GVA_DB.First(&position, positionID).Error
	if err != nil {
		return nil, err
	}
	return &position, nil
}

func (r *VaultPositionRepository) CreateVaultPosition(position *model.VaultPosition) error {
	return global.GVA_DB.Create(position).Error
}

func (r *VaultPositionRepository) UpdateVaultPosition(position *model.VaultPosition) error {
	return global.GVA_DB.Save(position).Error
}

func (r *VaultPositionRepository) DeleteVaultPosition(positionID int64) error {
	return global.GVA_DB.Delete(&model.VaultPosition{}, positionID).Error
}

func (r *VaultPositionRepository) GetOpenPositions(userID int64) ([]model.VaultPosition, error) {
	var positions []model.VaultPosition
	err := global.GVA_DB.Where("user_id = ? AND is_open = ?", userID, true).Find(&positions).Error
	if err != nil {
		return nil, err
	}
	return positions, nil
}

func (r *VaultPositionRepository) GetTotalUnrealizedPnl(userID int64) (float64, error) {
	var total float64
	err := global.GVA_DB.Model(&model.VaultPosition{}).
		Where("user_id = ? AND is_open = ?", userID, true).
		Select("COALESCE(SUM(unrealized_pnl_usd), 0)").
		Scan(&total).Error
	if err != nil {
		return 0, err
	}
	return total, nil
}

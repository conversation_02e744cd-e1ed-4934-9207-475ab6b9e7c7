package market

import (
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
)

type TradeOrderRepository struct {
}

func (r *TradeOrderRepository) GetTradeOrders(accountID int64, symbol string, page, pageSize int) ([]model.TradeOrder, int64, error) {
	var orders []model.TradeOrder
	var total int64

	query := global.GVA_DB.Model(&model.TradeOrder{}).Where("account_id = ?", accountID)
	if symbol != "" {
		query = query.Where("symbol = ?", symbol)
	}

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = query.Order("created_date desc").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&orders).Error

	return orders, total, err
}

func (r *TradeOrderRepository) GetTradeOrderByID(orderID int64) (*model.TradeOrder, error) {
	var order model.TradeOrder
	err := global.GVA_DB.First(&order, orderID).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

func (r *TradeOrderRepository) CreateTradeOrder(order *model.TradeOrder) error {
	return global.GVA_DB.Create(order).Error
}

func (r *TradeOrderRepository) UpdateTradeOrder(order *model.TradeOrder) error {
	return global.GVA_DB.Save(order).Error
}

func (r *TradeOrderRepository) UpdateOrderStatus(orderID int64, status string) error {
	return global.GVA_DB.Model(&model.TradeOrder{}).
		Where("order_id = ?", orderID).
		Update("order_status", status).Error
}

package swap

import (
	"context"
	"errors"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gorm.io/gorm"
)

func (s *swap) CreateOrUpdateHistory(repoEntity *model.RangoHistory) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		var existing model.RangoHistory
		err := tx.Where("request_id = ?", repoEntity.RequestId).First(&existing).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// Create if it does not exist
				return tx.Create(repoEntity).Error
			}
			return err
		}

		for _, swap := range repoEntity.Swaps {
			if err := tx.Model(&model.RangoSwaps{}).Where("id = ?", swap.ID).Updates(swap).Error; err != nil {
				return err
			}
		}
		return tx.Model(&existing).Updates(repoEntity).Error
	})
}

func (s *swap) Histories(blockchain, address string) ([]*model.RangoHistory, int64, error) {
	var entities []*model.RangoHistory
	err := global.GVA_DB.WithContext(context.Background()).
		Where("from_blockchain = ? AND user_address = ?", blockchain, address).
		Preload("Swaps").
		Order("created_at desc").
		Find(&entities).Error
	if err != nil {
		return nil, 0, err
	}
	return entities, 0, nil
}
func (s *swap) GetHistoryById(requestId string) (*model.RangoHistory, error) {
	var entity model.RangoHistory
	dbTx := global.GVA_DB.Model(&model.RangoHistory{}).
		Preload("Swaps").
		Where("request_id = ?", requestId).Find(&entity)
	err := model.HandleDBTxError(dbTx)
	if err != nil {
		return nil, err
	}
	return &entity, nil
}

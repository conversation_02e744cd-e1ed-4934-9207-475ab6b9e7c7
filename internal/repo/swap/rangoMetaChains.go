package swap

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/cache"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/constant"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gorm.io/gorm"
)

func (s *swap) GetRangoChains() ([]*model.RangoMetaChains, error) {
	var dbEntities []*model.RangoMetaChains
	key := cache.KeyRangoMetaChains()
	ctx := context.Background()
	str, err := global.GVA_REDIS.Get(ctx, key).Result()
	if err == nil && str != "" {
		if err := json.Unmarshal([]byte(str), &dbEntities); err == nil {
			return dbEntities, nil
		}
	}
	dbEntities, err = s.RangoMetaChains()
	if err != nil {
		return nil, err
	}

	data, err := json.<PERSON>(dbEntities)
	if err == nil {
		_ = global.GVA_REDIS.Set(ctx, key, data, constant.TenMinuteCacheTime).Err()
	}

	return dbEntities, nil
}

func (s *swap) RangoMetaChains() ([]*model.RangoMetaChains, error) {
	var entities []*model.RangoMetaChains
	dbTx := global.GVA_DB.Select("*").
		//Where("chain_id IN ? and is_active = true", supportChainIds). //todo zw adjust this later
		Where("chain_id IN ?", supportChainIds).
		Order("sort asc").
		Find(&entities)
	err := model.HandleDBTxError(dbTx)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	return entities, nil
}

func (s *swap) UpdateRangoChainActiveStatus() error {
	for chain := range rangoMetaMap {
		err := global.GVA_DB.Model(&model.RangoMetaChains{}).
			Where("name = ?", chain).
			Update("is_active", true).Error
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *swap) UpdateRangoTokenActiveStatus() error {
	for chain, tokens := range rangoMetaMap {
		for _, token := range tokens {
			err := global.GVA_DB.Model(&model.RangoMetaTokens{}).
				Where("block_chain = ? and symbol = ? and address = ?", chain, token.symbol, token.address).
				Updates(model.RangoMetaTokens{
					IsActive: true,
					BaseModel: model.BaseModel{
						UpdatedAt: time.Now(),
					}}).Error
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func (s *swap) GetRangoChainActiveStatus(chain string) (bool, error) {
	var isActive = false
	err := global.GVA_DB.Model(&model.RangoMetaChains{}).
		Where("name = ?", chain).
		Select("is_active").
		Find(&isActive).Error

	return isActive, err
}

func (s *swap) GetRangoTokenActiveStatus(chain, symbol, address string) (bool, error) {
	var isActive = false
	err := global.GVA_DB.Model(&model.RangoMetaTokens{}).
		Where("block_chain = ? and symbol = ? and LOWER(address) = LOWER(?)", chain, symbol, address).
		Select("is_active").
		Find(&isActive).Error

	return isActive, err
}

func (s *swap) GetRangoChainByName(name string) (*model.RangoMetaChains, error) {
	var chain model.RangoMetaChains
	dbTx := global.GVA_DB.Model(&model.RangoMetaChains{}).Where("name = ?", name).First(&chain)
	err := model.HandleDBTxError(dbTx)
	if err != nil {
		return nil, err
	}
	return &chain, nil
}

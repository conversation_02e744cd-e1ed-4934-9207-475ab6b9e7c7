package swap

import (
	"fmt"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/constant"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm/clause"
	"reflect"
)

func (s *swap) SaveRangoMetaData(
	chains []*model.RangoMetaChains,
	_tokens []*model.RangoMetaTokens,
	swappers []*model.RangoMetaSwappers,
) error {
	tx := global.GVA_DB

	save := func(updateField []string, data any, conflictColumns []clause.Column) error {
		if reflect.ValueOf(data).Len() == 0 {
			return nil
		}
		dbTx := tx.Debug().Clauses(clause.OnConflict{
			Columns:   conflictColumns,
			DoUpdates: clause.AssignmentColumns(updateField),
		}).CreateInBatches(data, constant.MidCreateSize)
		return model.HandleDBTxError(dbTx)
	}

	var g errgroup.Group
	g.Go(func() error {
		return save([]string{
			"updated_at",
			"deleted_at",
			"chain_id",
			"default_decimals",
			"address_patterns ",
			"fee_assets",
			"logo",
			"display_name",
			"short_name",
			"sort",
			"color",
			"enabled",
			"type",
			"info",
		}, chains, []clause.Column{{Name: "name"}})
	})

	g.Go(func() error {
		m := make(map[string]*model.RangoMetaSwappers)
		for i, swapper := range swappers {
			if m[swapper.SwapperId] != nil {
				continue
			}
			m[swapper.SwapperId] = swappers[i]
		}
		return save([]string{
			"updated_at",
			"deleted_at",
			"title",
			"logo",
			"swapper_group",
			"types",
			"enabled",
		}, utils.MapToArr(m), []clause.Column{{Name: "swapper_id"}})
	})
	g.Go(func() error {
		var tokens []*model.RangoMetaTokens
		for i, token := range _tokens {
			if token.UsdPrice != 0 {
				tokens = append(tokens, _tokens[i])
			}
		}

		for i := 0; i < len(tokens); i += constant.MidCreateSize {
			end := i + constant.MidCreateSize
			if end > len(tokens) {
				end = len(tokens)
			}
			chunk := tokens[i:end]
			err := save([]string{
				"updated_at",
				"deleted_at",
				"image",
				"usd_price",
				"decimals",
				"is_popular",
				"is_secondary_coin",
				"coin_source",
				"coin_source_url",
				"supported_swappers",
				"is_native",
			}, chunk, []clause.Column{
				{Name: "block_chain"}, {Name: "address"}, {Name: "symbol"}})
			if err != nil {
				return fmt.Errorf("batch %d-%d failed: %w", i, end, err)
			}
		}

		return nil
	})

	return g.Wait()
}

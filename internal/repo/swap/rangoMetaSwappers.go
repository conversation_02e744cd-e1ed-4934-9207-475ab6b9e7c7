package swap

import (
	"context"
	"encoding/json"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/cache"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/constant"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
)

func (s *swap) UpdateSwapStep(repoEntity *model.RangoHistory, swap *model.RangoSwaps) error {
	tx := global.GVA_DB.Begin()

	if err := tx.Model(&model.RangoHistory{}).
		Where("id = ?", repoEntity.ID).
		Save(repoEntity).Error; err != nil {
		tx.Rollback()
		return err
	}
	if err := tx.Model(&model.RangoSwaps{}).
		Where("id = ?", swap.ID).
		Save(&swap).Error; err != nil {
		tx.Rollback()
		return err
	}
	return tx.Commit().Error
}
func (s *swap) Swappers() ([]*model.RangoMetaSwappers, error) {
	var entities []*model.RangoMetaSwappers
	dbTx := global.GVA_DB.Select("*").
		Order("created_at asc").
		Find(&entities)
	err := model.HandleDBTxError(dbTx)
	if err != nil {
		return nil, err
	}
	return entities, nil
}

func (s *swap) GetSwappers() ([]*model.RangoMetaSwappers, error) {
	key := cache.KeyRangoMetaSwappers()
	var dbEntities []*model.RangoMetaSwappers
	ctx := context.Background()
	str, err := global.GVA_REDIS.Get(ctx, key).Result()
	if err == nil && str != "" {
		if err := json.Unmarshal([]byte(str), &dbEntities); err == nil {
			return dbEntities, nil
		}
	}
	dbEntities, err = s.Swappers()
	if err != nil {
		return nil, err
	}

	data, err := json.Marshal(dbEntities)
	if err == nil {
		_ = global.GVA_REDIS.Set(ctx, key, data, constant.TenMinuteCacheTime).Err()
	}

	return dbEntities, nil
}

package swap

import (
	"errors"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gorm.io/gorm"
)

func (s *swap) GetChainByName(chain string) (*model.Affiliate, error) {
	var dbEntity *model.Affiliate
	dbTx := global.GVA_DB.Model(&model.Affiliate{}).Where("chain = ?", chain).First(&dbEntity)
	err := model.HandleDBTxError(dbTx)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	return dbEntity, nil
}

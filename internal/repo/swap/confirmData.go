package swap

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/redis/go-redis/v9"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/cache"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/constant"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/rango"
)

func (s *swap) CacheConfirm(data rango.ConfirmRouteResult) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	err = global.GVA_REDIS.Set(context.Background(), cache.KeyRangoConfirmedData(data.RequestId), jsonData,
		constant.OneHourCacheTime).Err()
	if err != nil {
		return err
	}
	return nil
}

func (s *swap) LoadConfirmed(requestId string) (confirm rango.ConfirmRouteResult, err error) {
	str, err := global.GVA_REDIS.Get(context.Background(), cache.KeyRangoConfirmedData(requestId)).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		return
	}
	if str == "" {
		confirm.RequestId = requestId
		return confirm, nil
	}
	err = json.Unmarshal([]byte(str), &confirm)
	if err != nil {
		return
	}
	return confirm, nil

}

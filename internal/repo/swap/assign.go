package swap

import (
	"encoding/json"
	"fmt"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/rango"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/relay"
)

func ChainsAssign(chains []*rango.Chain) ([]*model.RangoMetaChains, error) {
	var result []*model.RangoMetaChains
	for _, chain := range chains {
		addressPatterns, err := utils.ForceJsonMarshal(chain.AddressPatterns)
		feeAssets, err := utils.ForceJsonMarshal(chain.FeeAssets)
		jsonData, err := json.Marshal(chain.Info)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal chain info: %w", err)
		}

		info, err := utils.Compress(jsonData)
		if err != nil {
			return nil, fmt.<PERSON><PERSON><PERSON>("failed to compress chain info: %w", err)
		}

		t := &model.RangoMetaChains{
			Name:            chain.Name,
			ChainId:         chain.ChainId,
			DefaultDecimals: int(chain.DefaultDecimals),
			AddressPatterns: string(addressPatterns),
			FeeAssets:       feeAssets,
			Logo:            chain.Logo,
			DisplayName:     chain.DisplayName,
			ShortName:       chain.ShortName,
			Sort:            int(chain.Sort),
			Color:           chain.Color,
			Enabled:         chain.Enabled,
			Type:            chain.Type,
			Info:            info,
		}
		result = append(result, t)

	}

	return result, nil
}

func TokenAssign(tokens []*rango.Token) ([]*model.RangoMetaTokens, error) {
	var result []*model.RangoMetaTokens
	for _, entity := range tokens {
		supportedSwappers, err := utils.ForceJsonMarshal(entity.SupportedSwappers)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal entity supported swappers info: %w", err)
		}
		isNative := NativeTokenMap[fmt.Sprintf("rango_%s", entity.Address)]
		output := &model.RangoMetaTokens{
			Address:           entity.Address,
			BlockChain:        entity.BlockChain,
			Name:              entity.Name,
			Symbol:            entity.Symbol,
			Image:             entity.Image,
			UsdPrice:          entity.UsdPrice,
			Decimals:          entity.Decimals,
			IsPopular:         entity.IsPopular,
			IsSecondaryCoin:   entity.IsSecondaryCoin,
			CoinSource:        entity.CoinSource,
			CoinSourceUrl:     entity.CoinSourceUrl,
			SupportedSwappers: string(supportedSwappers),
			IsNative:          isNative,
		}

		result = append(result, output)
	}
	return result, nil
}

func SwappersAssign(swappers []*rango.Swapper) ([]*model.RangoMetaSwappers, error) {
	var result []*model.RangoMetaSwappers
	for _, swapper := range swappers {
		types, err := utils.ForceJsonMarshal(swapper.Types)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal entity supported swappers info: %w", err)
		}
		output := &model.RangoMetaSwappers{
			SwapperId:    swapper.Id,
			Title:        swapper.Title,
			Logo:         swapper.Logo,
			SwapperGroup: swapper.SwapperGroup,
			Types:        string(types),
			Enabled:      swapper.Enabled,
		}

		result = append(result, output)
	}
	return result, nil
}

func RelayChainsAssign(chains []*relay.Chain) ([]*model.RelayMetaChains, []*model.RelayMetaTokens, error) {
	var (
		chainsModel    []*model.RelayMetaChains
		tokensModel    []*model.RelayMetaTokens
		repeatedFilter = make(map[string]*model.RelayMetaTokens)
	)
	for _, chain := range chains {
		jsonData, err := json.Marshal(chain.Contracts)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to marshal chain info: %w", err)
		}

		contractsBytes, err := utils.Compress(jsonData)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to compress chain info: %w", err)
		}
		solverAddresses, err := utils.ForceJsonMarshal(chain.SolverAddresses)
		tags, err := utils.ForceJsonMarshal(chain.Tags)
		// 映射链
		chainModel := &model.RelayMetaChains{
			ChainId:                fmt.Sprintf("%d", chain.Id),
			ChainIdHex:             fmt.Sprintf("0x%x", chain.Id),
			Name:                   chain.Name,
			DisplayName:            chain.DisplayName,
			HttpRpcUrl:             chain.HttpRpcUrl,
			WsRpcUrl:               chain.WsRpcUrl,
			ExplorerUrl:            chain.ExplorerUrl,
			ExplorerName:           chain.ExplorerName,
			DepositEnabled:         chain.DepositEnabled,
			TokenSupport:           chain.TokenSupport,
			Disabled:               chain.Disabled,
			PartialDisableLimit:    chain.PartialDisableLimit,
			BlockProductionLagging: chain.BlockProductionLagging,
			WithdrawalFee:          chain.WithdrawalFee,
			DepositFee:             chain.DepositFee,
			SurgeEnabled:           chain.SurgeEnabled,
			IconUrl:                chain.IconUrl,
			Contracts:              contractsBytes,
			VmType:                 chain.VmType,
			BaseChainId:            chain.BaseChainId,
			SolverAddresses:        string(solverAddresses),
			Tags:                   string(tags),
			LogoUrl:                chain.LogoUrl,
			BrandColor:             chain.BrandColor,
			ExplorerPaths: func() *string {
				if chain.ExplorerPaths != nil {
					return &chain.ExplorerPaths.Transaction
				}
				return nil
			}(),
		}
		chainsModel = append(chainsModel, chainModel)

		// 映射 Erc20Currencies
		for _, token := range chain.Erc20Currencies {
			isNative := NativeTokenMap[fmt.Sprintf("relay_%s", token.Address)]
			tokenModel := &model.RelayMetaTokens{
				ChainIdHex:       fmt.Sprintf("0x%x", chain.Id),
				ChainId:          fmt.Sprintf("%d", chain.Id),
				TokenId:          token.Id,
				Symbol:           token.Symbol,
				Name:             token.Name,
				Address:          token.Address,
				Decimals:         token.Decimals,
				SupportsBridging: token.SupportsBridging,
				WithdrawalFee:    token.WithdrawalFee,
				DepositFee:       token.DepositFee,
				SurgeEnabled:     token.SurgeEnabled,
				SupportsPermit:   token.SupportsPermit,
				UsdPrice:         "", // relay API 未提供
				LogoURI:          "", // relay API 未提供
				IsNative:         isNative,
			}
			key := fmt.Sprintf("%s-%s-%s", tokenModel.TokenId, tokenModel.ChainId, tokenModel.Address)
			if repeatedFilter[key] == nil {
				repeatedFilter[key] = tokenModel
				tokensModel = append(tokensModel, tokenModel)
			}
		}
		for _, token := range chain.FeaturedTokens {
			isNative := NativeTokenMap[fmt.Sprintf("relay_%s", token.Address)]
			tokenModel := &model.RelayMetaTokens{
				ChainIdHex:       fmt.Sprintf("0x%x", chain.Id),
				ChainId:          fmt.Sprintf("%d", chain.Id),
				TokenId:          token.Id,
				Symbol:           token.Symbol,
				Name:             token.Name,
				Address:          token.Address,
				Decimals:         token.Decimals,
				SupportsBridging: token.SupportsBridging,
				UsdPrice:         "", // relay API 未提供
				LogoURI:          token.Metadata.LogoURI,
				IsNative:         isNative,
			}

			key := fmt.Sprintf("%s-%s-%s", tokenModel.TokenId, tokenModel.ChainId, tokenModel.Address)
			if repeatedFilter[key] == nil {
				repeatedFilter[key] = tokenModel
				tokensModel = append(tokensModel, tokenModel)
			}
		}

	}
	return chainsModel, tokensModel, nil
}

package swap

var supportChainIds = []string{
	"0x1",          // ETH
	"0x38",         // BSC
	"mainnet-beta", // SOLANA
	"0x2f3fb341",   // relay SOLANA
	"0xa4b1",       // ARBITRUM
	//"0x2b6653dc", // TRON
}
var SpecialChainIdMap = map[string]string{
	"mainnet-beta": "SOLANA", // rango SOLANA
	"0x2f3fb341":   "SOLANA", // relay SOLANA
}
var NativeTokenMap = map[string]bool{
	"rango_":                                 true, // rango SOLANA
	"relay_********************************": true, // relay SOLANA
	"relay_******************************************": true, // relay SOLANA
}
var supportChains = []string{
	"ETH",
	"BSC",
	"SOLANA",
	"ARBITRUM",
	//"TRON",
}
var supportTokens = []string{
	"",                                 //native token
	"********************************", // native token for SOLANA
	"******************************************", // native token for relay SOLANA

	// ETH Chain
	//"******************************************", // USDT
	//"******************************************", // USDC
	//"******************************************", // DAI
	//"******************************************", // BUSD
	//"******************************************", // WETH

	// BSC Chain
	//"******************************************", // ETH
	//"******************************************", // USDT
	//"******************************************", // USDC
	//"******************************************", // AVAX

	//// SOLANA Chain
	//"es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8benwNYB", // USDT
	//"epjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", // USDC
	//"so********************************111111112",  // WSOL
	//"9n4nbm75f5ui33zbpyxn59ewsg8cgshtaeth5yfej9e",  // BTC

	// ARBITRUM Chain
	//"******************************************", // USDT
	"******************************************", // USDC
	//"******************************************", // WBTC
	//"******************************************", // WETH

	//// TRON Chain
	//"tr7nhqjekqxgtci8q8zy4pl8otszgjlj6t", // USDT
	//"tnuc9qb1rrps5cbwlmnmxxbjyfoydxjwfr", // WTRX
	//"tn3w4h6rk2ce4vxnynfqhwkennhjoxb3m9", // BTC
	//"tekxitehnzsmse2xqrbj4w32run966rdz8", // USDC
}
var supportChainIdsMap = map[string]string{ //name : chainId
	"ETH":      "0x1",
	"BSC":      "0x38",
	"SOLANA":   "mainnet-beta",
	"ARBITRUM": "0xa4b1",
	"TRON":     "0x2b6653dc",
}

type Token struct {
	symbol  string
	address string
}

var rangoMetaMap = map[string][]Token{
	"ETH": {
		{"ETH", ""}, // Native ETH
		{"USDT", "******************************************"},
		{"USDC", "******************************************"},
		{"DAI", "******************************************"},
		{"BUSD", "******************************************"},
		{"WETH", "******************************************"},
	},
	"BSC": {
		{"BNB", ""}, // Native BNB
		{"ETH", "******************************************"},
		{"USDT", "******************************************"},
		{"USDC", "******************************************"},
		{"AVAX", "******************************************"},
	},
	"SOLANA": {
		{"SOL", ""}, // Native SOL
		{"USDT", "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"},
		{"USDC", "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"},
		{"WSOL", "So********************************111111112"},
		{"BTC", "9n4nbM75f5Ui33ZbPYXn59EwSgE8CGsHtAeTH5YFeJ9E"},
	},
	"ARBITRUM": {
		{"ETH", ""}, // Native ETH on Arbitrum (for gas)
		{"USDT", "******************************************"},
		{"USDC", "******************************************"},
		{"USDC", "******************************************"},
		{"WBTC", "******************************************"},
		{"WETH", "******************************************"},
	},
	"TRON": {
		{"TRX", ""}, // Native TRX
		{"USDT", "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"},
		{"WTRX", "TNUC9Qb1rRpS5CbWLmNMxXBjyFoydXjWFR"},
		{"BTC", "TN3W4H6rK2ce4vX9YnFQHwKENnHjoxb3m9"},
		{"USDC", "TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8"},
	},
}

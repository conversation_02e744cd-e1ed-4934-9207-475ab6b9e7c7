package swap

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/cache"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/constant"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func (s *swap) GetRelayChains() ([]*model.RelayMetaChains, error) {
	var dbEntities []*model.RelayMetaChains
	key := cache.KeyRelayMetaChains()
	ctx := context.Background()
	str, err := global.GVA_REDIS.Get(ctx, key).Result()
	if err == nil && str != "" {
		if err := json.Unmarshal([]byte(str), &dbEntities); err == nil {
			return dbEntities, nil
		}
	}
	dbEntities, err = s.RelayMetaChains()
	if err != nil {
		return nil, err
	}

	data, err := json.Marshal(dbEntities)
	if err == nil {
		_ = global.GVA_REDIS.Set(ctx, key, data, constant.TenMinuteCacheTime).Err()
	}

	return dbEntities, nil
}
func (s *swap) GetRelayTokens() ([]*model.RelayMetaTokens, error) {
	var dbEntities []*model.RelayMetaTokens
	key := cache.KeyRelayMetaTokens()
	ctx := context.Background()
	str, err := global.GVA_REDIS.Get(ctx, key).Result()
	if err == nil && str != "" {
		if err := json.Unmarshal([]byte(str), &dbEntities); err == nil {
			return dbEntities, nil
		}
	}
	dbEntities, err = s.RelayMetaTokens()
	if err != nil {
		return nil, err
	}

	data, err := json.Marshal(dbEntities)
	if err == nil {
		_ = global.GVA_REDIS.Set(ctx, key, data, constant.TenMinuteCacheTime).Err()
	}

	return dbEntities, nil
}

func (s *swap) GetRelayTokenByUUID(id string) (*model.RelayMetaTokens, error) {
	var token model.RelayMetaTokens
	dbTx := global.GVA_DB.Model(&model.RelayMetaTokens{}).Where("id = ?", id).First(&token)
	err := model.HandleDBTxError(dbTx)
	if err != nil {
		return nil, err
	}
	return &token, nil
}

func (s *swap) GetRelayChainById(id string) (*model.RelayMetaChains, error) {
	var chain model.RelayMetaChains
	dbTx := global.GVA_DB.Model(&model.RelayMetaChains{}).Where("chain_id = ?", id).First(&chain)
	err := model.HandleDBTxError(dbTx)
	if err != nil {
		return nil, err
	}
	return &chain, nil
}

func (s *swap) RelayMetaChains() ([]*model.RelayMetaChains, error) {
	var entities []*model.RelayMetaChains
	dbTx := global.GVA_DB.Select("*").
		Where("chain_id_hex IN ? ", supportChainIds).
		Order("created_at asc").
		Find(&entities)
	err := model.HandleDBTxError(dbTx)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	return entities, nil
}
func (s *swap) RelayMetaTokens() ([]*model.RelayMetaTokens, error) {
	var entities []*model.RelayMetaTokens
	dbTx := global.GVA_DB.Select("*").
		Where("chain_id_hex IN ? AND address IN ?", supportChainIds, supportTokens).
		Order("created_at asc").
		Find(&entities)
	err := model.HandleDBTxError(dbTx)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	return entities, nil
}

func (s *swap) SaveRelayMetaData(
	chains []*model.RelayMetaChains,
	tokens []*model.RelayMetaTokens,
) error {
	tx := global.GVA_DB

	save := func(updateField []string, data any, conflictColumns []clause.Column) error {
		if reflect.ValueOf(data).Len() == 0 {
			return nil
		}
		dbTx := tx.Debug().Clauses(clause.OnConflict{
			Columns:   conflictColumns,
			DoUpdates: clause.AssignmentColumns(updateField),
		}).CreateInBatches(data, constant.MidCreateSize)
		return model.HandleDBTxError(dbTx)
	}

	var g errgroup.Group
	g.Go(func() error {
		return save([]string{
			//"updated_at",
			//"deleted_at",
			"chain_id",
			"chain_id_hex",
			"name",
			"display_name",
			"http_rpc_url",
			"ws_rpc_url",
			"explorer_url",
			"explorer_name",
			"deposit_enabled",
			"token_support",
			"disabled",
			"partial_disable_limit",
			"block_production_lagging",
			"withdrawal_fee",
			"deposit_fee",
			"surge_enabled",
			"icon_url",
			"contracts",
			"vm_type",
			"base_chain_id",
			"solver_addresses",
			"tags",
			"logo_url",
			"brand_color",
			"explorer_paths",
		}, chains, []clause.Column{{Name: "chain_id_hex"}})

	})

	g.Go(func() error {

		for i := 0; i < len(tokens); i += constant.MidCreateSize {
			end := i + constant.MidCreateSize
			if end > len(tokens) {
				end = len(tokens)
			}
			chunk := tokens[i:end]
			err := save([]string{
				//"updated_at",
				//"deleted_at",
				"chain_id",
				"chain_id_hex",
				"token_id",
				"symbol",
				"name",
				"address",
				"decimals",
				"supports_bridging",
				"withdrawal_fee",
				"deposit_fee",
				"surge_enabled",
				"supports_permit",
				"usd_price",
				"logo_uri",
			},
				chunk, []clause.Column{
					{Name: "token_id"}, {Name: "chain_id_hex"}, {Name: "address"}},
			)
			if err != nil {
				return fmt.Errorf("batch %d-%d failed: %w", i, end, err)
			}
		}

		return nil
	})

	return g.Wait()
}

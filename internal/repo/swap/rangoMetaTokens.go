package swap

import (
	"context"
	"encoding/json"
	"errors"

	"gorm.io/gorm"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/cache"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/constant"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
)

func (s *swap) Tokens() ([]*model.RangoMetaTokens, error) {
	var entities []*model.RangoMetaTokens
	//dbTx := global.GVA_DB.Where("block_chain IN ? AND address IN ? AND is_active = true", supportChains, supportTokens).//todo zw adjust this later
	dbTx := global.GVA_DB.Where("block_chain IN ? AND address IN ?", supportChains, supportTokens).
		Order("is_popular DESC, created_at DESC").
		Find(&entities)
	err := model.HandleDBTxError(dbTx)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return entities, nil
}

func (s *swap) GetSupportTokens() ([]*model.RangoMetaTokens, error) {

	key := cache.KeyRangoMetaTokens()
	var entities []*model.RangoMetaTokens
	ctx := context.Background()
	str, err := global.GVA_REDIS.Get(ctx, key).Result()
	if err == nil && str != "" {
		if err := json.Unmarshal([]byte(str), &entities); err == nil {
			return entities, nil
		}
	}
	entities, err = s.Tokens()
	if err != nil {
		return nil, err
	}
	data, err := json.Marshal(entities)
	if err == nil {
		_ = global.GVA_REDIS.Set(ctx, key, data, constant.TenMinuteCacheTime).Err()
	}

	return entities, nil
}

func (s *swap) GetRangoTokenByUUID(id string) (*model.RangoMetaTokens, error) {
	var token model.RangoMetaTokens
	dbTx := global.GVA_DB.Model(&model.RangoMetaTokens{}).Where("id = ?", id).First(&token)
	err := model.HandleDBTxError(dbTx)
	if err != nil {
		return nil, err
	}
	return &token, nil
}

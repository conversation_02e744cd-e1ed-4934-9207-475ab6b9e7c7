package swap

import (
	"context"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/cache"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/constant"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
)

func (s *swap) CacheStatus(requestId string, step int64, result string) error {
	err := global.GVA_REDIS.Set(context.Background(), cache.KeyRangoCheckStatus(requestId, step), result,
		constant.FiveMinuteCacheTime).Err()
	if err != nil {
		return err
	}
	return nil
}

func (s *swap) LoadStatus(requestId string, step int64) (string, error) {
	str, err := global.GVA_REDIS.Get(context.Background(), cache.KeyRangoCheckStatus(requestId, step)).Result()
	if err != nil {
		return "", err
	}
	return str, err
}

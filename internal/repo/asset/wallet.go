package asset

import (
	"github.com/google/uuid"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gorm.io/gorm/clause"
)

func (asset *asset) GetUserWalletsByID(userID uuid.UUID) ([]string, error) {
	var wallet []string
	err := global.GVA_DB.Model(&model.UserWallet{}).
		Select("wallet_address").
		Where("user_id = ?", userID).
		Find(&wallet).Error

	return wallet, err
}

func (asset *asset) SaveUserBalance(balance model.UserBalance) error {
	return global.GVA_DB.Model(&model.UserBalance{}).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "wallet_address"}}, // Conflict on these primary keys
			UpdateAll: true,
		}).Create(&balance).Error
}

func (asset *asset) GetUserBalance(wallet string) (model.UserBalance, error) {
	var balance model.UserBalance
	err := global.GVA_DB.Model(&model.UserBalance{}).
		Where("wallet_address = ?", wallet).
		Find(&balance).Error
	return balance, err
}

func (asset *asset) UpsertUserWallet(data model.UserWallet) error {
	return global.GVA_DB.Model(model.UserWallet{}).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "user_id"}, {Name: "wallet_address"}},
			UpdateAll: true,
		}).Create(&data).Error
}

func (asset *asset) BatchInsertUserWallet(data []*model.UserWallet) error {
	return global.GVA_DB.Model(model.UserWallet{}).
		Create(data).Error
}

func (asset *asset) GetAllUserWallets(offset, limit int) ([]*model.UserWallet, error) {
	var wallets []*model.UserWallet
	err := global.GVA_DB.Model(&model.UserWallet{}).
		Offset(offset).
		Limit(limit).
		Find(&wallets).Error
	if err != nil {
		return nil, err
	}

	return wallets, err
}

package miscEvent

import (
	"github.com/google/uuid"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo"
)

var MiscEvent repo.MiscEventRepo = new(miscEvent)

type miscEvent struct {
}

func (s *miscEvent) GetUserMiscEvents(userID uuid.UUID, lastID uuid.UUID, limit int) ([]*model.UserMiscEvent, error) {

	return nil, nil
}

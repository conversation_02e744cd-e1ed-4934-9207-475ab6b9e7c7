package orderStatus

import (
	"github.com/google/uuid"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo"
)

var OrderStatus repo.OrderStatusRepo = new(orderStatus)

type orderStatus struct {
}

func (s *orderStatus) GetUserOrderStatus(userID uuid.UUID, lastID uuid.UUID, limit int) ([]*model.UserOrderStatus, error) {

	return nil, nil
}

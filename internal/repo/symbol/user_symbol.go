package symbol

import (
	"context"
	"github.com/google/uuid"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gorm.io/gorm/clause"
	"time"
)

type UserSymbolRepository struct {
}

func (r *CoinRepository) UpsertFavoriteSymbol(userID uuid.UUID, symbol string, isFavorite bool) (*model.UserCoin, error) {
	userSymbol := model.UserCoin{
		Symbol:     symbol,
		IsFavorite: isFavorite,
		UserID:     userID,
	}

	var returnData model.UserCoin
	rawSQL := `
		INSERT INTO user_coin (user_id, symbol, is_favorite, created_at, updated_at)
		VALUES (?, ?, ?, NOW(), NOW())
		ON CONFLICT (user_id, symbol)
		DO UPDATE SET
			is_favorite = EXCLUDED.is_favorite,
			updated_at = NOW()
		RETURNING *
	`
	result := global.GVA_DB.Raw(rawSQL, userSymbol.UserID, userSymbol.Symbol, userSymbol.IsFavorite).Scan(&returnData)
	if result.Error != nil {
		return nil, result.Error
	}

	return &returnData, nil
}

func (r *CoinRepository) GetFavoriteSymbols(userID uuid.UUID) ([]*model.UserCoin, error) {
	var favoriteSymbols []*model.UserCoin
	result := global.GVA_DB.Model(&model.UserCoin{}).
		Where("user_id = ? AND is_favorite = true", userID).
		Find(&favoriteSymbols)
	if result.Error != nil {
		return nil, result.Error
	}

	return favoriteSymbols, nil
}

func (r *UserSymbolRepository) GetUserSymbolDetail(userID uuid.UUID, symbol string) (*model.UserCoin, error) {
	var userSymbol model.UserCoin

	result := global.GVA_DB.Model(&model.UserCoin{}).
		Where("user_id = ? AND symbol = ?", userID, symbol).
		First(&userSymbol)

	if result.Error != nil {
		if result.RowsAffected == 0 {
			return nil, nil
		}
		return nil, result.Error
	}

	return &userSymbol, nil
}

func (r *UserSymbolRepository) UpsertUserSymbolPreference(userID uuid.UUID, symbol string, upsertField map[string]interface{}) (*model.UserCoin, error) {
	var userCoin model.UserCoin
	insertData := map[string]interface{}{
		"user_id":     userID,
		"symbol":      symbol,
		"is_favorite": false,
		"created_at":  time.Now(),
		"updated_at":  time.Now(),
	}

	for k, v := range upsertField {
		insertData[k] = v
	}

	upsertField["updated_at"] = time.Now()
	result := global.GVA_DB.Model(&model.UserCoin{}).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "user_id"}, {Name: "symbol"}}, // Conflict on these primary keys
			DoUpdates: clause.Assignments(upsertField),
		}).Create(&insertData)
	if result.Error != nil {
		return nil, result.Error
	}

	err := global.GVA_DB.Model(&model.UserCoin{}).
		Where("user_id = ? AND symbol = ?", userID, symbol).
		First(&userCoin).Error
	if err != nil {
		return nil, err
	}

	return &userCoin, nil
}

func (r *UserSymbolRepository) GetUserSymbolPreference(userID uuid.UUID, symbol string) (*model.UserCoin, error) {
	var userCoinPreference *model.UserCoin
	err := global.GVA_DB.Model(&model.UserCoin{}).
		Where("user_id = ? AND symbol = ?", userID, symbol).
		First(&userCoinPreference).Error
	return userCoinPreference, err
}

func (r *UserSymbolRepository) GetAlertSymbolsSetting(userID uuid.UUID) ([]*model.UserNotificationSetting, error) {
	var settings []*model.UserNotificationSetting
	result := global.GVA_DB.Model(&model.UserNotificationSetting{}).
		Where("user_id = ? and is_deleted = false", userID).
		Find(&settings)
	if result.Error != nil {
		return nil, result.Error
	}

	return settings, nil
}

func (r *UserSymbolRepository) UpdateAlertSymbolSetting(setting model.UserNotificationSetting) error {
	return global.GVA_DB.Model(&model.UserNotificationSetting{}).
		Where("id = ?", setting.ID).
		Updates(map[string]interface{}{
			"update_at": time.Now(),
			"is_active": setting.IsActive,
		}).Error
}

func (r *UserSymbolRepository) MarkNotifiedSetting(setting model.UserNotificationSetting) error {
	return global.GVA_DB.Model(&model.UserNotificationSetting{}).
		Where("id = ?", setting.ID).
		Updates(map[string]interface{}{
			"update_at":        time.Now(),
			"last_notified_at": time.Now(),
		}).Error
}

func (r *UserSymbolRepository) InsertAlertSymbolSetting(setting model.UserNotificationSetting) error {
	return global.GVA_DB.Model(&model.UserNotificationSetting{}).
		Create(&setting).Error
}

func (r *UserSymbolRepository) DeleteAlertSymbolSetting(settingIDs []uuid.UUID) error {
	return global.GVA_DB.Model(&model.UserNotificationSetting{}).
		Where("id IN (?)", settingIDs).
		Update("is_deleted", true).Error
}

func (r *UserSymbolRepository) GetActiveSettingsBySymbol(ctx context.Context, symbol string) ([]*model.UserNotificationSetting, error) {
	var settings []*model.UserNotificationSetting
	result := global.GVA_DB.Model(&model.UserNotificationSetting{}).
		Where("symbol = ? and is_active = true and is_deleted = false", symbol).
		Find(&settings)
	if result.Error != nil {
		return nil, result.Error
	}

	return settings, nil
}

package symbol

import (
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"time"
)

type OHLCRepository struct {
}

func (r *OHLCRepository) GetOHLC(symbol, interval string, timestamp int64, isForward bool, limit int) ([]*model.OHLC, error) {
	var (
		ohlc        []*model.OHLC
		tableName   = model.OHLC{}.TableName() + "_" + interval
		err         error
		timeValue   string
		whereClause string
	)

	// Format the timestamp once
	timeValue = time.UnixMilli(timestamp).Format("'2006-01-02 15:04:05'") // Enclose in single quotes

	if isForward {
		whereClause = "time_open >= " + timeValue + " AND symbol = '" + symbol + "'"
	} else {
		whereClause = "time_open <= " + timeValue + " AND symbol = '" + symbol + "'"
	}

	if interval == "1m" {
		err = global.GVA_CLICKHOUSE.Table(tableName).
			Where(whereClause).
			Order("time_open desc").
			Limit(limit).
			Find(&ohlc).Error
	} else {
		err = global.GVA_CLICKHOUSE.Table(tableName).
			Select("symbol, time_open, time_close, " +
				"finalizeAggregation(open) AS open, " +
				"finalizeAggregation(close) AS close, " +
				"high, " +
				"low, " +
				"total_volume").
			Where(whereClause).
			Order("time_open DESC").
			Limit(limit).
			Find(&ohlc).Error
	}

	if err != nil {
		return nil, err
	}
	return ohlc, nil
}

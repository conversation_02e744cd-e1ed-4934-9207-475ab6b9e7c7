package symbol

import (
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
)

type SignalRepository struct {
}

func (r *SignalRepository) GetSignal(user string, limit, offset int) ([]*model.SymbolSignal, int64, error) {
	var (
		data  []*model.SymbolSignal
		total int64
	)
	db := global.GVA_DB.Model(&model.SymbolSignal{})
	if user != "" {
		db = db.Where("username = ?", user)
	}
	db.Count(&total)
	result := db.Limit(limit).Offset(offset).Order("created_date DESC").Find(&data)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	return data, total, nil
}

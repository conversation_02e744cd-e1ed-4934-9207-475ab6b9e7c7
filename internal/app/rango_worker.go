package app

import (
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/initializer"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/workflow"
	"go.uber.org/zap"
	"os"
	"os/signal"
)

func RunRangoWorker() {
	interrupt := make(chan os.Signal, 1)
	signal.Notify(interrupt, os.Interrupt)

	global.GVA_VP = initializer.Viper()
	global.GVA_LOG = initializer.Zap()
	global.GVA_DB = initializer.Gorm()
	if global.GVA_CONFIG.System.UseMultipoint || global.GVA_CONFIG.System.UseRedis {
		// init redis server
		initializer.Redis()
		initializer.RedisList()
	}
	zap.ReplaceGlobals(global.GVA_LOG)
	s := workflow.NewRangoWorker()
	s.RegisterWorker()
	err := s.RunWorker()
	if err != nil {
		global.GVA_LOG.Error("Failed to run worker", zap.Error(err))
		os.Exit(1)
	}
}

package app

import (
	"encoding/json"
	"log"
	"os"
	"os/signal"
	"time"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/initializer"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/trade"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/hyperliquid"
	"go.uber.org/zap"
)

const (
	wsURL        = "wss://api.hyperliquid.xyz/ws"
	subscribeMsg = `{
		"method": "subscribe",
		"subscription": {
			"type": "trades",
			"coin": "BTC"
		}
	}`
)

func RunHyperLiquidWorker() {
	interrupt := make(chan os.Signal, 1)
	signal.Notify(interrupt, os.Interrupt)

	global.GVA_VP = initializer.Viper()
	global.GVA_LOG = initializer.Zap()
	global.GVA_DB = initializer.Gorm()

	zap.ReplaceGlobals(global.GVA_LOG)
	initializer.InitMqtt()
	global.GVA_MQTT = initializer.GetMqttClient()

	ws, err := initializer.NewHyperLiquidWSConnection()
	if err != nil {
		global.GVA_LOG.Error("Failed to connect to HyperLiquid WebSocket", zap.Error(err))
		os.Exit(1)
	}

	wsTradeService := trade.NewWssTradeHandlerService(ws, global.GVA_MQTT)

	wsTradeService.SubscribeTradeSocket()
	wsTradeService.SubscribeL2BookSocket()

	done := make(chan struct{})

	// 4. Read from WebSocket and publish to MQTT
	go func() {
		defer close(done)
		for {
			_, msg, err := ws.ReadMessage()
			if err != nil {
				log.Printf("WebSocket read error: %v", err)
				return
			}

			// decocde msg as generic type WsMessage and get channel
			var wsMessage hyperliquid.WsGeneric
			if err := json.Unmarshal(msg, &wsMessage); err != nil {
				log.Printf("Failed to unmarshal message: %v", err)
				log.Printf("Message: %s", string(msg))
				continue
			}

			switch wsMessage.Channel {
			case "trades":
				{

					var wsTradeMsg hyperliquid.WsMessageList[hyperliquid.WsTrade]
					if err := json.Unmarshal(msg, &wsTradeMsg); err != nil {
						log.Printf("Failed to unmarshal trade message: %v", err)
						continue
					}
					wsTradeService.HandleTradeMessage(&wsTradeMsg)
				}
			case "l2Book":
				{
					var wsOrderbookMsg hyperliquid.WsMessageObject[hyperliquid.WsBook]
					if err := json.Unmarshal(msg, &wsOrderbookMsg); err != nil {
						log.Printf("Failed to unmarshal l2book message: %v", err)
						continue
					}
					wsTradeService.HandleL2BookMessage(&wsOrderbookMsg)
				}
			default:
				log.Printf("Unknown channel: %s", wsMessage.Channel)
			}
		}
	}()

	// 5. Handle shutdown
	for {
		select {
		case <-done:
			return
		case <-interrupt:
			log.Println("Interrupt received, closing connections...")
			// ws.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
			defer (*global.GVA_MQTT).Disconnect(250)
			defer ws.Close()
			time.Sleep(time.Second)
			return
		}
	}
}

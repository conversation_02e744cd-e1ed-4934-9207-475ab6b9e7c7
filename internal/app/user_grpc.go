package app

import (
	"log"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	user_signing "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/protogen/user/v1"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

func NewUserGrpcClient() *user_signing.UserSigningServiceClient {
	if global.GVA_CONFIG.Grpc.UserService == "" {
		log.Fatalf("gRPC server address is not configured")
	}

	conn, err := grpc.Dial(global.GVA_CONFIG.Grpc.UserService, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("Failed to connect to gRPC server: %v", err)
	}

	client := user_signing.NewUserSigningServiceClient(conn)
	return &client
}

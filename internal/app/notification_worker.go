package app

import (
	"context"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	repoAsset "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/asset"
	repoSymbol "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/symbol"
	repoTrade "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/trade"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/asset"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/notification"
)

func RunNotificationWorker() {
	userService := asset.NewUserService(repoAsset.Asset, repoTrade.Trade, global.GVA_NATS_MEME)
	go userService.Start(context.Background())

	priceChangeDetectorService := notification.NewPriceChangeDetectorService(global.GVA_NATS_DEX)
	go priceChangeDetectorService.Start(context.Background())

	notificationDispatcherService := notification.NewNotificationDispatcherService(global.GVA_NATS_DEX, global.GVA_NATS_MEME, repoSymbol.Symbol)
	go notificationDispatcherService.Start(context.Background())
}

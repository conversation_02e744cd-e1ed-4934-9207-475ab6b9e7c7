services:
  postgres:
    image: postgres:16.7
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_DB=dex_v2
      - POSTGRES_USER=dev
      - POSTGRES_PASSWORD=dev
    command: ["postgres", "-c", "log_statement=all"]

  redis:
    image: 'bitnami/redis:6.2.3'
    ports:
      - 6379:6379
    environment:
      - REDIS_PASSWORD=dev
  
  # Uncomment to enable MongoDB service
  mongodb:
     image: mongo:8.0.4
     hostname: mongodb
     ports:
       - "27017:27017"
     environment:
       MONGO_INITDB_ROOT_USERNAME: dev
       MONGO_INITDB_ROOT_PASSWORD: dev
     volumes:
       - mongodb_data:/data/db

  # Uncomment to test build dockerfile
  # app:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #   ports:
  #     - "9000:9000"
  #   command: "./app"
  #   env_file:
  #     - .env

  emqx:
    image: emqx/emqx:latest
    ports:
      - "1883:1883"
      - "8083:8083"
      - "8084:8084"
      - "8883:8883"
      - "18083:18083"
    volumes:
      - emqx_data:/opt/emqx/data
      - emqx_log:/opt/emqx/log

  nats:
    image: nats:latest # Use the official NATS image
    container_name: nats-jetstream
    ports:
      - "4222:4222" # Client port
      - "8222:8222" # HTTP monitoring port
    command:
      - "-js"          # Enable JetStream
      - "-c"           # Use a configuration file (optional, but good practice for more complex setups)
      - "/etc/nats/nats.conf"
    volumes:
      - nats_data:/data # Mount a named volume to persist JetStream data
      - ./nats.conf:/etc/nats/nats.conf # Mount a custom config file (read-only)
    healthcheck:
      test: [ "CMD", "nats", "tool", "req", "--ignore-auth", "-s", "nats://localhost:8222"]
      interval: 5s
      timeout: 3s
      retries: 5

volumes:
  postgres_data:
  mongodb_data:
  emqx_data:
  emqx_log:
  nats_data:
version: '3.8'

services:
  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: agent
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 5

  xbit-agent:
    build: .
    ports:
      - "8080:8080"
    env_file:
      - ./env/docker.env
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./config.yaml:/root/config.yaml
      - ./log:/root/log

volumes:
  postgres_data:

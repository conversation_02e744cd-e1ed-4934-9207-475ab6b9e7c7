package main

import (
	"flag"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/initializer"
	repoSwap "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/swap"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/command"
	"go.uber.org/zap"
)

func main() {
	var arg string
	flag.StringVar(&arg, "update", "", "update database data")

	global.GVA_VP = initializer.Viper()
	global.GVA_LOG = initializer.Zap()
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initializer.Gorm()

	cmd := command.NewSwapCommand(repoSwap.Swap)
	if arg == "rango" {
		err := cmd.UpdateRangoActiveStatus()
		if err != nil {
			global.GVA_LOG.Error("update rango failed\n", zap.Error(err))
		}
	} else {
		global.GVA_LOG.Info("Unknown command\n", zap.String("command", arg))
	}

	if global.GVA_DB != nil {
		db, _ := global.GVA_DB.DB()
		defer db.Close()
	}
}

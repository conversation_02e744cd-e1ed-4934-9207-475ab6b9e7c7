package main

import (
	"fmt"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/initializer"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/nats"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/hyperliquid"
	"go.uber.org/zap"
	"os"
	"os/signal"
	"syscall"
)

func main() {
	global.GVA_VP = initializer.Viper()
	global.GVA_LOG = initializer.Zap()
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initializer.Gorm()
	initializer.DBList()
	initializer.InitMqtt()
	global.GVA_MQTT = initializer.GetMqttClient()
	global.GVA_NATS_DEX = nats.InitNatsJetStream(global.GVA_CONFIG.NatsDex)
	global.GVA_NATS_MEME = nats.InitNatsJetStream(global.GVA_CONFIG.NatsMeme)
	global.GVA_NATS_MEME.PublisherAddStream(utils.WalletBalanceStream, []string{utils.WalletBalanceSubject})

	if global.GVA_CONFIG.System.UseMultipoint || global.GVA_CONFIG.System.UseRedis {
		// init redis server
		initializer.Redis()
		initializer.RedisList()
	}

	if global.GVA_CONFIG.Hyperliquid.UseMainNet {
		global.GVA_HYPERLIQUID_URL = hyperliquid.MainnetURL
		global.GVA_HYPERLIQUID_INFO_ENDPOINT = hyperliquid.InfoAPIMainnetEndpoint
	} else {
		global.GVA_HYPERLIQUID_URL = hyperliquid.TestnetURL
		global.GVA_HYPERLIQUID_INFO_ENDPOINT = hyperliquid.InfoAPITestnetEndpoint
	}

	if global.GVA_DB != nil {
		db, _ := global.GVA_DB.DB()
		defer db.Close()
	}
	go initializer.InitTask()

	quit := make(chan os.Signal, 1)

	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	fmt.Println("Shutting down server...")
}

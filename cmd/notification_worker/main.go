package main

import (
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/app"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/initializer"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/nats"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/hyperliquid"
	"go.uber.org/zap"
	"log"
	"os"
	"os/signal"
	"time"
)

func main() {
	interrupt := make(chan os.Signal, 1)
	signal.Notify(interrupt, os.Interrupt)

	global.GVA_VP = initializer.Viper()
	global.GVA_LOG = initializer.Zap()
	global.GVA_DB = initializer.Gorm()

	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_NATS_MEME = nats.InitNatsJetStream(global.GVA_CONFIG.NatsMeme)
	global.GVA_NATS_DEX = nats.InitNatsJetStream(global.GVA_CONFIG.NatsDex)

	if global.GVA_CONFIG.System.UseMultipoint || global.GVA_CONFIG.System.UseRedis {
		// init redis server
		initializer.Redis()
		initializer.RedisList()
	}

	if global.GVA_CONFIG.Hyperliquid.UseMainNet {
		global.GVA_HYPERLIQUID_URL = hyperliquid.MainnetURL
		global.GVA_HYPERLIQUID_INFO_ENDPOINT = hyperliquid.InfoAPIMainnetEndpoint
	} else {
		global.GVA_HYPERLIQUID_URL = hyperliquid.TestnetURL
		global.GVA_HYPERLIQUID_INFO_ENDPOINT = hyperliquid.InfoAPITestnetEndpoint
	}

	go app.RunNotificationWorker()

	done := make(chan struct{})

	for {
		select {
		case <-done:
			return
		case <-interrupt:
			log.Println("Interrupt received, closing connections...")
			defer (*global.GVA_NATS_MEME).Close()
			time.Sleep(time.Second)
			return
		}
	}
}

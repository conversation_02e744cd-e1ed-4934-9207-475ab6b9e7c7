definitions:
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqCallBack:
    properties:
      requestId:
        type: string
      step:
        type: integer
      txHash:
        type: string
    required:
    - requestId
    - step
    - txHash
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqCheckApproval:
    properties:
      requestId:
        type: string
      txHash:
        type: string
    required:
    - requestId
    - txHash
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqCheckStatus:
    properties:
      requestId:
        type: string
      step:
        type: integer
      txHash:
        type: string
    required:
    - requestId
    - step
    - txHash
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqConfirmRoute:
    properties:
      destination:
        type: string
      requestId:
        type: string
      selectedWallets:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.SelectedWallet'
        type: array
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqCreateTx:
    properties:
      requestId:
        type: string
      step:
        type: integer
      userSettings:
        $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.UserSettings'
      validations:
        $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.Validations'
    required:
    - requestId
    - step
    - userSettings
    - validations
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqGetAllPossibleRoutes:
    properties:
      amount:
        type: string
      fromBlockchain:
        type: string
      fromSymbol:
        type: string
      fromTokenAddress:
        type: string
      slippage:
        type: string
      toBlockchain:
        type: string
      toSymbol:
        type: string
      toTokenAddress:
        type: string
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqGetBestRoute:
    properties:
      amount:
        type: string
      fromAddress:
        type: string
      fromBlockChain:
        type: string
      fromSymbol:
        type: string
      fromTokenAddress:
        type: string
      toAddress:
        type: string
      toBlockChain:
        type: string
      toSymbol:
        type: string
      toTokenAddress:
        type: string
    required:
    - amount
    - fromAddress
    - fromBlockChain
    - fromSymbol
    - toAddress
    - toBlockChain
    - toSymbol
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqOHLC:
    properties:
      from_timestamp:
        type: integer
      interval:
        description: 1m, 5m, 15m, 1h, 4h, 1d
        type: string
      limit:
        type: integer
      symbol:
        type: string
    required:
    - interval
    - limit
    - symbol
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqRangoHistory:
    properties:
      address:
        type: string
      blockchain:
        type: string
      endTime:
        type: integer
      page:
        type: integer
      pageSize:
        type: integer
      startTime:
        type: integer
      status:
        type: string
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqSwapStatus:
    properties:
      requestId:
        type: string
      step:
        type: integer
      txHash:
        type: string
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqTradeOrders:
    properties:
      account_Id:
        type: integer
      address:
        type: string
      chain_id:
        type: integer
      end_time:
        type: integer
      page:
        type: integer
      page_size:
        type: integer
      start_time:
        type: integer
      symbol:
        type: string
    required:
    - account_Id
    - address
    - chain_id
    - page
    - page_size
    - symbol
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqUserInfo:
    properties:
      address:
        type: string
      chain_id:
        type: integer
      user_id:
        type: integer
    required:
    - address
    - chain_id
    - user_id
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqVaultPositions:
    properties:
      address:
        type: string
      chain_id:
        type: integer
      user_id:
        type: integer
    required:
    - address
    - chain_id
    - user_id
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.SelectedWallet:
    properties:
      address:
        type: string
      blockchain:
        type: string
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.UserSettings:
    properties:
      infiniteApprove:
        type: boolean
      slippage:
        type: number
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.Validations:
    properties:
      approve:
        type: boolean
      balance:
        type: boolean
      fee:
        type: boolean
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.History:
    properties:
      diagnosisMessages:
        type: string
      failReason:
        type: string
      fromAddress:
        type: string
      fromBlockchain:
        type: string
      fromSymbol:
        type: string
      missingBlockchains:
        type: string
      outputAmount:
        type: string
      requestAmount:
        type: string
      requestId:
        type: string
      resultType:
        type: string
      status:
        type: string
      step:
        type: integer
      swaps:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_model.RangoSwaps'
        type: array
      toAddress:
        type: string
      toBlockchain:
        type: string
      toSymbol:
        type: string
      userAddress:
        type: string
      validationStatus:
        items:
          type: string
        type: array
      walletNotSupportingFromBlockchain:
        type: boolean
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.OHLC:
    properties:
      close:
        type: number
      high:
        type: number
      low:
        type: number
      open:
        type: number
      timestamp:
        type: integer
      volume:
        type: number
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespCallBack:
    properties:
      success:
        type: string
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespCheckStatus:
    properties:
      failReason:
        type: string
      fromAmount:
        type: string
      fromBlockchain:
        type: string
      fromSymbol:
        type: string
      requestId:
        type: string
      status:
        type: string
      toAmount:
        type: string
      toBlockchain:
        type: string
      toSymbol:
        type: string
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespConfirmRoute:
    properties:
      error:
        type: string
      requestId:
        type: string
      status:
        type: string
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespExchangeMeta:
    properties:
      blockchains:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_model.RangoMetaChains'
        type: array
      popularTokens:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_model.RangoMetaTokens'
        type: array
      swappers:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_model.RangoMetaSwappers'
        type: array
      tokens:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Token'
        type: array
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespHistory:
    properties:
      list:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.History'
        type: array
      total:
        type: integer
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespOHLC:
    properties:
      data:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.OHLC'
        type: array
      symbol:
        type: string
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespSignals:
    properties:
      signals:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Signal'
        type: array
      total:
        type: integer
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespTradeOrders:
    properties:
      orders:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.TradeOrder'
        type: array
      total:
        type: integer
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespUserInfo:
    properties:
      user_info:
        $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.UserInfo'
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespVaultPositions:
    properties:
      positions:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.VaultPosition'
        type: array
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response:
    properties:
      code:
        type: integer
      data: {}
      msg:
        type: string
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Signal:
    properties:
      confidence:
        description: signal confidence 0 1
        type: number
      description:
        type: string
      expiry_time:
        description: signal validity period
        type: integer
      price:
        type: number
      signal_type:
        description: buy, sell, hold
        type: string
      source:
        description: signal source
        type: string
      symbol:
        type: string
      time:
        type: integer
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Token:
    properties:
      address:
        type: string
      blockChain:
        type: string
      chainLogo:
        type: string
      coinSource:
        type: string
      coinSourceUrl:
        type: string
      decimals:
        type: integer
      image:
        type: string
      isPopular:
        type: boolean
      isSecondaryCoin:
        type: boolean
      name:
        type: string
      supportedSwappers:
        items:
          type: string
        type: array
      symbol:
        type: string
      usdPrice:
        type: number
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.TradeOrder:
    properties:
      amount:
        type: number
      create_time:
        type: integer
      order_id:
        type: string
      price:
        type: number
      side:
        type: string
      status:
        type: string
      symbol:
        type: string
      update_time:
        type: integer
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.UserInfo:
    properties:
      active_trades:
        type: integer
      address:
        type: string
      last_updated:
        type: integer
      total_value:
        type: number
      vault_value:
        type: number
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.VaultPosition:
    properties:
      amount:
        type: number
      apy:
        type: number
      last_updated:
        type: integer
      symbol:
        type: string
      value:
        type: number
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_model.RangoMetaChains:
    properties:
      address_patterns:
        type: string
      chain_id:
        type: string
      color:
        type: string
      createdAt:
        type: string
      defaultDecimals:
        type: integer
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      displayName:
        type: string
      enabled:
        type: boolean
      feeAssets:
        items:
          type: integer
        type: array
      id:
        type: string
      info:
        items:
          type: integer
        type: array
      logo:
        type: string
      name:
        type: string
      shortName:
        type: string
      sort:
        type: integer
      type:
        type: string
      updatedAt:
        type: string
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_model.RangoMetaSwappers:
    properties:
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      enabled:
        type: boolean
      id:
        type: string
      logo:
        type: string
      swapper_id:
        type: string
      swapperGroup:
        type: string
      title:
        type: string
      types:
        type: string
      updatedAt:
        type: string
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_model.RangoMetaTokens:
    properties:
      address:
        type: string
      block_chain:
        type: string
      coinSource:
        type: string
      coinSourceUrl:
        type: string
      createdAt:
        type: string
      decimals:
        type: integer
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      id:
        type: string
      image:
        type: string
      isPopular:
        type: boolean
      isSecondaryCoin:
        type: boolean
      name:
        type: string
      supportedSwappers:
        type: string
      symbol:
        type: string
      updatedAt:
        type: string
      usdPrice:
        type: number
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_model.RangoSwaps:
    properties:
      call_data:
        items:
          type: integer
        type: array
      call_data_hash:
        type: string
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      estimatedTimeInSeconds:
        type: integer
      fee:
        items:
          type: integer
        type: array
      fromAmount:
        type: string
      id:
        type: string
      maxRequiredSign:
        type: integer
      requestId:
        type: string
      step:
        type: integer
      swapChainType:
        type: string
      swapperId:
        type: string
      swapperLogo:
        type: string
      swapperType:
        type: string
      toAmount:
        type: string
      tx_hash:
        type: string
      updatedAt:
        type: string
      userAddress:
        type: string
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.AllPossibleRoutes:
    properties:
      diagnosisMessages:
        items:
          type: string
        type: array
      error:
        type: string
      errorCode:
        type: integer
      from:
        $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.FromTo'
      requestAmount:
        type: string
      results:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Result'
        type: array
      routeId:
        type: string
      to:
        $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.FromTo'
      traceId:
        type: integer
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Asset:
    properties:
      address:
        type: string
      blockchain:
        type: string
      symbol:
        type: string
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Fee:
    properties:
      amount:
        type: string
      asset:
        $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Asset'
      expenseType:
        type: string
      meta:
        $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Meta'
      name:
        type: string
      price:
        type: number
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.FromTo:
    properties:
      address:
        type: string
      blockchain:
        type: string
      symbol:
        type: string
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.InternalSwaps:
    properties:
      estimatedTimeInSeconds:
        type: integer
      fee:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Fee'
        type: array
      from:
        $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.TokenInfo'
      fromAmount:
        type: string
      fromAmountMaxValue:
        type: string
      fromAmountMinValue:
        type: string
      fromAmountPrecision:
        type: string
      fromAmountRestrictionType:
        type: string
      includesDestinationTx:
        type: boolean
      internalSwaps:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.InternalSwaps'
        type: array
      maxRequiredSign:
        type: integer
      recommendedSlippage:
        type: string
      routes:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Route'
        type: array
      swapChainType:
        type: string
      swapperId:
        type: string
      swapperLogo:
        type: string
      swapperType:
        type: string
      timeStat:
        type: string
      to:
        $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.TokenInfo'
      toAmount:
        type: string
      warnings:
        type: string
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Meta:
    properties:
      gasLimit:
        type: string
      gasPrice:
        type: string
      type:
        type: string
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Node:
    properties:
      inputAmount:
        type: string
      marketId:
        type: string
      marketName:
        type: string
      outputAmount:
        type: string
      percent:
        type: number
      pools:
        items:
          type: string
        type: array
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Nodes:
    properties:
      from:
        type: string
      fromAddress:
        type: string
      fromBlockchain:
        type: string
      fromLogo:
        type: string
      nodes:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Node'
        type: array
      to:
        type: string
      toAddress:
        type: string
      toBlockchain:
        type: string
      toLogo:
        type: string
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.RecommendedSlippage:
    properties:
      error:
        type: boolean
      slippage:
        type: string
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.RespBestRoute:
    properties:
      diagnosisMessages:
        items:
          type: string
        type: array
      error:
        type: string
      errorCode:
        type: string
      from:
        $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.FromTo'
      missingBlockchains:
        items:
          type: string
        type: array
      requestAmount:
        type: string
      requestId:
        type: string
      result:
        $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Result'
      to:
        $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.FromTo'
      traceId:
        type: string
      validationStatus:
        type: string
      walletNotSupportingFromBlockchain:
        type: boolean
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.RespCheckApproval:
    properties:
      currentApprovedAmount:
        type: string
      isApproved:
        type: boolean
      requiredApprovedAmount:
        type: string
      txStatus:
        type: string
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.RespCreateTx:
    properties:
      error:
        type: string
      errorCode:
        type: integer
      ok:
        type: boolean
      traceId:
        type: integer
      transaction:
        $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Transaction'
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Result:
    properties:
      missingBlockchains:
        items:
          type: string
        type: array
      outputAmount:
        type: string
      priceImpactUsd:
        type: string
      priceImpactUsdPercent:
        type: string
      requestId:
        type: string
      resultType:
        type: string
      scores:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Score'
        type: array
      swaps:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Swap'
        type: array
      tags:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Tag'
        type: array
      walletNotSupportingFromBlockchain:
        type: boolean
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Route:
    properties:
      nodes:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Nodes'
        type: array
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Score:
    properties:
      preferenceType:
        type: string
      score:
        type: integer
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Swap:
    properties:
      estimatedTimeInSeconds:
        type: integer
      fee:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Fee'
        type: array
      from:
        $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.TokenInfo'
      fromAmount:
        type: string
      fromAmountMaxValue:
        type: string
      fromAmountMinValue:
        type: string
      fromAmountPrecision:
        type: string
      fromAmountRestrictionType:
        type: string
      includesDestinationTx:
        type: boolean
      internalSwaps:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.InternalSwaps'
        type: array
      maxRequiredSign:
        type: integer
      recommendedSlippage:
        $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.RecommendedSlippage'
      routes:
        items:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Route'
        type: array
      swapChainType:
        type: string
      swapperId:
        type: string
      swapperLogo:
        type: string
      swapperType:
        type: string
      timeStat:
        $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.TimeStat'
      to:
        $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.TokenInfo'
      toAmount:
        type: string
      warnings:
        items:
          type: string
        type: array
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Tag:
    properties:
      label:
        type: string
      value:
        type: string
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.TimeStat:
    properties:
      avg:
        type: integer
      max:
        type: integer
      min:
        type: integer
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.TokenInfo:
    properties:
      address:
        type: string
      blockchain:
        type: string
      blockchainLogo:
        type: string
      decimals:
        type: integer
      logo:
        type: string
      symbol:
        type: string
      usdPrice:
        type: number
    type: object
  gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Transaction:
    properties:
      blockChain:
        type: string
      data:
        type: string
      from:
        type: string
      gasLimit:
        type: string
      gasPrice:
        type: string
      identifier:
        description: NoEvm
        type: string
      instructions:
        items:
          type: string
        type: array
      isApprovalTx:
        type: boolean
      maxFeePerGas:
        type: string
      maxPriorityFeePerGas:
        type: string
      nonce:
        type: string
      recentBlockhash:
        type: string
      serializedMessage:
        items:
          type: integer
        type: array
      signatures:
        items:
          type: string
        type: array
      spender:
        type: string
      to:
        description: Evm
        type: string
      txType:
        type: string
      type:
        type: string
      value:
        type: string
    type: object
  gorm.DeletedAt:
    properties:
      time:
        type: string
      valid:
        description: Valid is true if Time is not NULL
        type: boolean
    type: object
info:
  contact: {}
paths:
  /v1/callBack:
    post:
      consumes:
      - application/json
      parameters:
      - description: Callback parameters
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqCallBack'
      produces:
      - application/json
      responses:
        "200":
          description: Handle swap callback
          schema:
            allOf:
            - $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response'
            - properties:
                data:
                  $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespCallBack'
              type: object
      summary: Handle swap callback
      tags:
      - SwapHup
  /v1/checkApproval:
    post:
      consumes:
      - application/json
      parameters:
      - description: CheckApproval parameters
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqCheckApproval'
      produces:
      - application/json
      responses:
        "200":
          description: CheckApproval
          schema:
            allOf:
            - $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response'
            - properties:
                data:
                  $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.RespCheckApproval'
              type: object
      summary: CheckApproval
      tags:
      - SwapHup
  /v1/checkStatus:
    post:
      consumes:
      - application/json
      parameters:
      - description: Status check parameters
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqCheckStatus'
      produces:
      - application/json
      responses:
        "200":
          description: Check swap status
          schema:
            allOf:
            - $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response'
            - properties:
                data:
                  $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespCheckStatus'
              type: object
      summary: Check swap status
      tags:
      - SwapHup
  /v1/confirmRoute:
    post:
      consumes:
      - application/json
      parameters:
      - description: Route confirmation parameters
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqConfirmRoute'
      produces:
      - application/json
      responses:
        "200":
          description: Confirm swap route
          schema:
            allOf:
            - $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response'
            - properties:
                data:
                  $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespConfirmRoute'
              type: object
      summary: Confirm swap route
      tags:
      - SwapHup
  /v1/createTx:
    post:
      consumes:
      - application/json
      parameters:
      - description: ReqCreateTx parameters
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqCreateTx'
      produces:
      - application/json
      responses:
        "200":
          description: CreateTx
          schema:
            allOf:
            - $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response'
            - properties:
                data:
                  $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.RespCreateTx'
              type: object
      summary: CreateTx
      tags:
      - SwapHup
  /v1/getAllPossibleRoutes:
    post:
      consumes:
      - application/json
      parameters:
      - description: ReqGetAllPossibleRoutes parameters
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqGetAllPossibleRoutes'
      produces:
      - application/json
      responses:
        "200":
          description: GetAllPossibleRoutes
          schema:
            allOf:
            - $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response'
            - properties:
                data:
                  $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.AllPossibleRoutes'
              type: object
      summary: GetAllPossibleRoutes
      tags:
      - SwapHup
  /v1/getBestRoute:
    post:
      consumes:
      - application/json
      parameters:
      - description: GetBestRoute parameters
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqGetBestRoute'
      produces:
      - application/json
      responses:
        "200":
          description: Get BestRoute
          schema:
            allOf:
            - $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response'
            - properties:
                data:
                  $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.RespBestRoute'
              type: object
      summary: Get BestRoute
      tags:
      - SwapHup
  /v1/getExchangeMeta:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: Get exchange metadata
          schema:
            allOf:
            - $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response'
            - properties:
                data:
                  $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespExchangeMeta'
              type: object
      summary: Get exchange metadata
      tags:
      - SwapHup
  /v1/getHistories:
    post:
      consumes:
      - application/json
      parameters:
      - description: History query parameters
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqRangoHistory'
      produces:
      - application/json
      responses:
        "200":
          description: Get swap histories
          schema:
            allOf:
            - $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response'
            - properties:
                data:
                  $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespHistory'
              type: object
      summary: Get swap histories
      tags:
      - SwapHup
  /v1/getOHLC:
    get:
      consumes:
      - application/json
      parameters:
      - description: OHLC parameters
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqOHLC'
      produces:
      - application/json
      responses:
        "200":
          description: Get OHLC data
          schema:
            allOf:
            - $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response'
            - properties:
                data:
                  $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespOHLC'
              type: object
      summary: Get OHLC data
      tags:
      - Symbol
  /v1/getSignals:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: Get trading signals
          schema:
            allOf:
            - $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response'
            - properties:
                data:
                  $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespSignals'
              type: object
      summary: Get trading signals
      tags:
      - Market
  /v1/getStatus:
    post:
      consumes:
      - application/json
      parameters:
      - description: Status query parameters
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqSwapStatus'
      produces:
      - application/json
      responses:
        "200":
          description: Get swap status
          schema:
            allOf:
            - $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response'
            - properties:
                data:
                  $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespCheckStatus'
              type: object
      summary: Get swap status
      tags:
      - SwapHup
  /v1/getTradeOrders:
    get:
      consumes:
      - application/json
      parameters:
      - description: Trade orders parameters
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqTradeOrders'
      produces:
      - application/json
      responses:
        "200":
          description: Get trade orders
          schema:
            allOf:
            - $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response'
            - properties:
                data:
                  $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespTradeOrders'
              type: object
      summary: Get trade orders
      tags:
      - Market
  /v1/getUserInfo:
    get:
      consumes:
      - application/json
      parameters:
      - description: User information parameters
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqUserInfo'
      produces:
      - application/json
      responses:
        "200":
          description: Get user information
          schema:
            allOf:
            - $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response'
            - properties:
                data:
                  $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespUserInfo'
              type: object
      summary: Get user information
      tags:
      - Market
  /v1/getVaultPositions:
    get:
      consumes:
      - application/json
      parameters:
      - description: Vault positions parameters
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqVaultPositions'
      produces:
      - application/json
      responses:
        "200":
          description: Get vault positions
          schema:
            allOf:
            - $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response'
            - properties:
                data:
                  $ref: '#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespVaultPositions'
              type: object
      summary: Get vault positions
      tags:
      - Market
swagger: "2.0"

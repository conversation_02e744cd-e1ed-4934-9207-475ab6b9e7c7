{"swagger": "2.0", "info": {"contact": {}}, "paths": {"/v1/callBack": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["SwapHup"], "summary": "Handle swap callback", "parameters": [{"description": "Callback parameters", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqCallBack"}}], "responses": {"200": {"description": "Handle swap callback", "schema": {"allOf": [{"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespCallBack"}}}]}}}}}, "/v1/checkApproval": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["SwapHup"], "summary": "CheckApproval", "parameters": [{"description": "CheckApproval parameters", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqCheckApproval"}}], "responses": {"200": {"description": "CheckApproval", "schema": {"allOf": [{"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.RespCheckApproval"}}}]}}}}}, "/v1/checkStatus": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["SwapHup"], "summary": "Check swap status", "parameters": [{"description": "Status check parameters", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqCheckStatus"}}], "responses": {"200": {"description": "Check swap status", "schema": {"allOf": [{"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespCheckStatus"}}}]}}}}}, "/v1/confirmRoute": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["SwapHup"], "summary": "Confirm swap route", "parameters": [{"description": "Route confirmation parameters", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqConfirmRoute"}}], "responses": {"200": {"description": "Confirm swap route", "schema": {"allOf": [{"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespConfirmRoute"}}}]}}}}}, "/v1/createTx": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["SwapHup"], "summary": "CreateTx", "parameters": [{"description": "ReqCreateTx parameters", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqCreateTx"}}], "responses": {"200": {"description": "CreateTx", "schema": {"allOf": [{"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.RespCreateTx"}}}]}}}}}, "/v1/getAllPossibleRoutes": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["SwapHup"], "summary": "GetAllPossibleRoutes", "parameters": [{"description": "ReqGetAllPossibleRoutes parameters", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqGetAllPossibleRoutes"}}], "responses": {"200": {"description": "GetAllPossibleRoutes", "schema": {"allOf": [{"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.AllPossibleRoutes"}}}]}}}}}, "/v1/getBestRoute": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["SwapHup"], "summary": "Get BestRoute", "parameters": [{"description": "GetBestRoute parameters", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqGetBestRoute"}}], "responses": {"200": {"description": "Get BestRoute", "schema": {"allOf": [{"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.RespBestRoute"}}}]}}}}}, "/v1/getExchangeMeta": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["SwapHup"], "summary": "Get exchange metadata", "responses": {"200": {"description": "Get exchange metadata", "schema": {"allOf": [{"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespExchangeMeta"}}}]}}}}}, "/v1/getHistories": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["SwapHup"], "summary": "Get swap histories", "parameters": [{"description": "History query parameters", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqRangoHistory"}}], "responses": {"200": {"description": "Get swap histories", "schema": {"allOf": [{"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespHistory"}}}]}}}}}, "/v1/getOHLC": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["Symbol"], "summary": "Get OHLC data", "parameters": [{"description": "OHLC parameters", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqOHLC"}}], "responses": {"200": {"description": "Get OHLC data", "schema": {"allOf": [{"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespOHLC"}}}]}}}}}, "/v1/getSignals": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["Market"], "summary": "Get trading signals", "responses": {"200": {"description": "Get trading signals", "schema": {"allOf": [{"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespSignals"}}}]}}}}}, "/v1/getStatus": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["SwapHup"], "summary": "Get swap status", "parameters": [{"description": "Status query parameters", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqSwapStatus"}}], "responses": {"200": {"description": "Get swap status", "schema": {"allOf": [{"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespCheckStatus"}}}]}}}}}, "/v1/getTradeOrders": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["Market"], "summary": "Get trade orders", "parameters": [{"description": "Trade orders parameters", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqTradeOrders"}}], "responses": {"200": {"description": "Get trade orders", "schema": {"allOf": [{"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespTradeOrders"}}}]}}}}}, "/v1/getUserInfo": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["Market"], "summary": "Get user information", "parameters": [{"description": "User information parameters", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqUserInfo"}}], "responses": {"200": {"description": "Get user information", "schema": {"allOf": [{"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespUserInfo"}}}]}}}}}, "/v1/getVaultPositions": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["Market"], "summary": "Get vault positions", "parameters": [{"description": "Vault positions parameters", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqVaultPositions"}}], "responses": {"200": {"description": "Get vault positions", "schema": {"allOf": [{"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespVaultPositions"}}}]}}}}}}, "definitions": {"gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqCallBack": {"type": "object", "required": ["requestId", "step", "txHash"], "properties": {"requestId": {"type": "string"}, "step": {"type": "integer"}, "txHash": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqCheckApproval": {"type": "object", "required": ["requestId", "txHash"], "properties": {"requestId": {"type": "string"}, "txHash": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqCheckStatus": {"type": "object", "required": ["requestId", "step", "txHash"], "properties": {"requestId": {"type": "string"}, "step": {"type": "integer"}, "txHash": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqConfirmRoute": {"type": "object", "properties": {"destination": {"type": "string"}, "requestId": {"type": "string"}, "selectedWallets": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.SelectedWallet"}}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqCreateTx": {"type": "object", "required": ["requestId", "step", "userSettings", "validations"], "properties": {"requestId": {"type": "string"}, "step": {"type": "integer"}, "userSettings": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.UserSettings"}, "validations": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.Validations"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqGetAllPossibleRoutes": {"type": "object", "properties": {"amount": {"type": "string"}, "fromBlockchain": {"type": "string"}, "fromSymbol": {"type": "string"}, "fromTokenAddress": {"type": "string"}, "slippage": {"type": "string"}, "toBlockchain": {"type": "string"}, "toSymbol": {"type": "string"}, "toTokenAddress": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqGetBestRoute": {"type": "object", "required": ["amount", "fromAddress", "fromBlockChain", "fromSymbol", "to<PERSON><PERSON><PERSON>", "to<PERSON><PERSON><PERSON><PERSON><PERSON>", "toSymbol"], "properties": {"amount": {"type": "string"}, "fromAddress": {"type": "string"}, "fromBlockChain": {"type": "string"}, "fromSymbol": {"type": "string"}, "fromTokenAddress": {"type": "string"}, "toAddress": {"type": "string"}, "toBlockChain": {"type": "string"}, "toSymbol": {"type": "string"}, "toTokenAddress": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqOHLC": {"type": "object", "required": ["interval", "limit", "symbol"], "properties": {"from_timestamp": {"type": "integer"}, "interval": {"description": "1m, 5m, 15m, 1h, 4h, 1d", "type": "string"}, "limit": {"type": "integer"}, "symbol": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqRangoHistory": {"type": "object", "properties": {"address": {"type": "string"}, "blockchain": {"type": "string"}, "endTime": {"type": "integer"}, "page": {"type": "integer"}, "pageSize": {"type": "integer"}, "startTime": {"type": "integer"}, "status": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqSwapStatus": {"type": "object", "properties": {"requestId": {"type": "string"}, "step": {"type": "integer"}, "txHash": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqTradeOrders": {"type": "object", "required": ["account_Id", "address", "chain_id", "page", "page_size", "symbol"], "properties": {"account_Id": {"type": "integer"}, "address": {"type": "string"}, "chain_id": {"type": "integer"}, "end_time": {"type": "integer"}, "page": {"type": "integer"}, "page_size": {"type": "integer"}, "start_time": {"type": "integer"}, "symbol": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqUserInfo": {"type": "object", "required": ["address", "chain_id", "user_id"], "properties": {"address": {"type": "string"}, "chain_id": {"type": "integer"}, "user_id": {"type": "integer"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.ReqVaultPositions": {"type": "object", "required": ["address", "chain_id", "user_id"], "properties": {"address": {"type": "string"}, "chain_id": {"type": "integer"}, "user_id": {"type": "integer"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.SelectedWallet": {"type": "object", "properties": {"address": {"type": "string"}, "blockchain": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.UserSettings": {"type": "object", "properties": {"infiniteApprove": {"type": "boolean"}, "slippage": {"type": "number"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_request.Validations": {"type": "object", "properties": {"approve": {"type": "boolean"}, "balance": {"type": "boolean"}, "fee": {"type": "boolean"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.History": {"type": "object", "properties": {"diagnosisMessages": {"type": "string"}, "failReason": {"type": "string"}, "fromAddress": {"type": "string"}, "fromBlockchain": {"type": "string"}, "fromSymbol": {"type": "string"}, "missingBlockchains": {"type": "string"}, "outputAmount": {"type": "string"}, "requestAmount": {"type": "string"}, "requestId": {"type": "string"}, "resultType": {"type": "string"}, "status": {"type": "string"}, "step": {"type": "integer"}, "swaps": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_model.RangoSwaps"}}, "toAddress": {"type": "string"}, "toBlockchain": {"type": "string"}, "toSymbol": {"type": "string"}, "userAddress": {"type": "string"}, "validationStatus": {"type": "array", "items": {"type": "string"}}, "walletNotSupportingFromBlockchain": {"type": "boolean"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.OHLC": {"type": "object", "properties": {"close": {"type": "number"}, "high": {"type": "number"}, "low": {"type": "number"}, "open": {"type": "number"}, "timestamp": {"type": "integer"}, "volume": {"type": "number"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespCallBack": {"type": "object", "properties": {"success": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespCheckStatus": {"type": "object", "properties": {"failReason": {"type": "string"}, "fromAmount": {"type": "string"}, "fromBlockchain": {"type": "string"}, "fromSymbol": {"type": "string"}, "requestId": {"type": "string"}, "status": {"type": "string"}, "toAmount": {"type": "string"}, "toBlockchain": {"type": "string"}, "toSymbol": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespConfirmRoute": {"type": "object", "properties": {"error": {"type": "string"}, "requestId": {"type": "string"}, "status": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespExchangeMeta": {"type": "object", "properties": {"blockchains": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_model.RangoMetaChains"}}, "popularTokens": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_model.RangoMetaTokens"}}, "swappers": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_model.RangoMetaSwappers"}}, "tokens": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Token"}}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespHistory": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.History"}}, "total": {"type": "integer"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespOHLC": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.OHLC"}}, "symbol": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespSignals": {"type": "object", "properties": {"signals": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Signal"}}, "total": {"type": "integer"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespTradeOrders": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.TradeOrder"}}, "total": {"type": "integer"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespUserInfo": {"type": "object", "properties": {"user_info": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.UserInfo"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.RespVaultPositions": {"type": "object", "properties": {"positions": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.VaultPosition"}}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Response": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "msg": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Signal": {"type": "object", "properties": {"confidence": {"description": "signal confidence 0 1", "type": "number"}, "description": {"type": "string"}, "expiry_time": {"description": "signal validity period", "type": "integer"}, "price": {"type": "number"}, "signal_type": {"description": "buy, sell, hold", "type": "string"}, "source": {"description": "signal source", "type": "string"}, "symbol": {"type": "string"}, "time": {"type": "integer"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.Token": {"type": "object", "properties": {"address": {"type": "string"}, "blockChain": {"type": "string"}, "chainLogo": {"type": "string"}, "coinSource": {"type": "string"}, "coinSourceUrl": {"type": "string"}, "decimals": {"type": "integer"}, "image": {"type": "string"}, "isPopular": {"type": "boolean"}, "isSecondaryCoin": {"type": "boolean"}, "name": {"type": "string"}, "supportedSwappers": {"type": "array", "items": {"type": "string"}}, "symbol": {"type": "string"}, "usdPrice": {"type": "number"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.TradeOrder": {"type": "object", "properties": {"amount": {"type": "number"}, "create_time": {"type": "integer"}, "order_id": {"type": "string"}, "price": {"type": "number"}, "side": {"type": "string"}, "status": {"type": "string"}, "symbol": {"type": "string"}, "update_time": {"type": "integer"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.UserInfo": {"type": "object", "properties": {"active_trades": {"type": "integer"}, "address": {"type": "string"}, "last_updated": {"type": "integer"}, "total_value": {"type": "number"}, "vault_value": {"type": "number"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_dto_response.VaultPosition": {"type": "object", "properties": {"amount": {"type": "number"}, "apy": {"type": "number"}, "last_updated": {"type": "integer"}, "symbol": {"type": "string"}, "value": {"type": "number"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_model.RangoMetaChains": {"type": "object", "properties": {"address_patterns": {"type": "string"}, "chain_id": {"type": "string"}, "color": {"type": "string"}, "createdAt": {"type": "string"}, "defaultDecimals": {"type": "integer"}, "deletedAt": {"$ref": "#/definitions/gorm.DeletedAt"}, "displayName": {"type": "string"}, "enabled": {"type": "boolean"}, "feeAssets": {"type": "array", "items": {"type": "integer"}}, "id": {"type": "string"}, "info": {"type": "array", "items": {"type": "integer"}}, "logo": {"type": "string"}, "name": {"type": "string"}, "shortName": {"type": "string"}, "sort": {"type": "integer"}, "type": {"type": "string"}, "updatedAt": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_model.RangoMetaSwappers": {"type": "object", "properties": {"createdAt": {"type": "string"}, "deletedAt": {"$ref": "#/definitions/gorm.DeletedAt"}, "enabled": {"type": "boolean"}, "id": {"type": "string"}, "logo": {"type": "string"}, "swapperGroup": {"type": "string"}, "swapper_id": {"type": "string"}, "title": {"type": "string"}, "types": {"type": "string"}, "updatedAt": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_model.RangoMetaTokens": {"type": "object", "properties": {"address": {"type": "string"}, "block_chain": {"type": "string"}, "coinSource": {"type": "string"}, "coinSourceUrl": {"type": "string"}, "createdAt": {"type": "string"}, "decimals": {"type": "integer"}, "deletedAt": {"$ref": "#/definitions/gorm.DeletedAt"}, "id": {"type": "string"}, "image": {"type": "string"}, "isPopular": {"type": "boolean"}, "isSecondaryCoin": {"type": "boolean"}, "name": {"type": "string"}, "supportedSwappers": {"type": "string"}, "symbol": {"type": "string"}, "updatedAt": {"type": "string"}, "usdPrice": {"type": "number"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_internal_model.RangoSwaps": {"type": "object", "properties": {"call_data": {"type": "array", "items": {"type": "integer"}}, "call_data_hash": {"type": "string"}, "createdAt": {"type": "string"}, "deletedAt": {"$ref": "#/definitions/gorm.DeletedAt"}, "estimatedTimeInSeconds": {"type": "integer"}, "fee": {"type": "array", "items": {"type": "integer"}}, "fromAmount": {"type": "string"}, "id": {"type": "string"}, "maxRequiredSign": {"type": "integer"}, "requestId": {"type": "string"}, "step": {"type": "integer"}, "swapChainType": {"type": "string"}, "swapperId": {"type": "string"}, "swapperLogo": {"type": "string"}, "swapperType": {"type": "string"}, "toAmount": {"type": "string"}, "tx_hash": {"type": "string"}, "updatedAt": {"type": "string"}, "userAddress": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.AllPossibleRoutes": {"type": "object", "properties": {"diagnosisMessages": {"type": "array", "items": {"type": "string"}}, "error": {"type": "string"}, "errorCode": {"type": "integer"}, "from": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.FromTo"}, "requestAmount": {"type": "string"}, "results": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Result"}}, "routeId": {"type": "string"}, "to": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.FromTo"}, "traceId": {"type": "integer"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Asset": {"type": "object", "properties": {"address": {"type": "string"}, "blockchain": {"type": "string"}, "symbol": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Fee": {"type": "object", "properties": {"amount": {"type": "string"}, "asset": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Asset"}, "expenseType": {"type": "string"}, "meta": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Meta"}, "name": {"type": "string"}, "price": {"type": "number"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.FromTo": {"type": "object", "properties": {"address": {"type": "string"}, "blockchain": {"type": "string"}, "symbol": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.InternalSwaps": {"type": "object", "properties": {"estimatedTimeInSeconds": {"type": "integer"}, "fee": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Fee"}}, "from": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.TokenInfo"}, "fromAmount": {"type": "string"}, "fromAmountMaxValue": {"type": "string"}, "fromAmountMinValue": {"type": "string"}, "fromAmountPrecision": {"type": "string"}, "fromAmountRestrictionType": {"type": "string"}, "includesDestinationTx": {"type": "boolean"}, "internalSwaps": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.InternalSwaps"}}, "maxRequiredSign": {"type": "integer"}, "recommendedSlippage": {"type": "string"}, "routes": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Route"}}, "swapChainType": {"type": "string"}, "swapperId": {"type": "string"}, "swapperLogo": {"type": "string"}, "swapperType": {"type": "string"}, "timeStat": {"type": "string"}, "to": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.TokenInfo"}, "toAmount": {"type": "string"}, "warnings": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Meta": {"type": "object", "properties": {"gasLimit": {"type": "string"}, "gasPrice": {"type": "string"}, "type": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Node": {"type": "object", "properties": {"inputAmount": {"type": "string"}, "marketId": {"type": "string"}, "marketName": {"type": "string"}, "outputAmount": {"type": "string"}, "percent": {"type": "number"}, "pools": {"type": "array", "items": {"type": "string"}}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Nodes": {"type": "object", "properties": {"from": {"type": "string"}, "fromAddress": {"type": "string"}, "fromBlockchain": {"type": "string"}, "fromLogo": {"type": "string"}, "nodes": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Node"}}, "to": {"type": "string"}, "toAddress": {"type": "string"}, "toBlockchain": {"type": "string"}, "toLogo": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.RecommendedSlippage": {"type": "object", "properties": {"error": {"type": "boolean"}, "slippage": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.RespBestRoute": {"type": "object", "properties": {"diagnosisMessages": {"type": "array", "items": {"type": "string"}}, "error": {"type": "string"}, "errorCode": {"type": "string"}, "from": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.FromTo"}, "missingBlockchains": {"type": "array", "items": {"type": "string"}}, "requestAmount": {"type": "string"}, "requestId": {"type": "string"}, "result": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Result"}, "to": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.FromTo"}, "traceId": {"type": "string"}, "validationStatus": {"type": "string"}, "walletNotSupportingFromBlockchain": {"type": "boolean"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.RespCheckApproval": {"type": "object", "properties": {"currentApprovedAmount": {"type": "string"}, "isApproved": {"type": "boolean"}, "requiredApprovedAmount": {"type": "string"}, "txStatus": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.RespCreateTx": {"type": "object", "properties": {"error": {"type": "string"}, "errorCode": {"type": "integer"}, "ok": {"type": "boolean"}, "traceId": {"type": "integer"}, "transaction": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Transaction"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Result": {"type": "object", "properties": {"missingBlockchains": {"type": "array", "items": {"type": "string"}}, "outputAmount": {"type": "string"}, "priceImpactUsd": {"type": "string"}, "priceImpactUsdPercent": {"type": "string"}, "requestId": {"type": "string"}, "resultType": {"type": "string"}, "scores": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Score"}}, "swaps": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Swap"}}, "tags": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Tag"}}, "walletNotSupportingFromBlockchain": {"type": "boolean"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Route": {"type": "object", "properties": {"nodes": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Nodes"}}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Score": {"type": "object", "properties": {"preferenceType": {"type": "string"}, "score": {"type": "integer"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Swap": {"type": "object", "properties": {"estimatedTimeInSeconds": {"type": "integer"}, "fee": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Fee"}}, "from": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.TokenInfo"}, "fromAmount": {"type": "string"}, "fromAmountMaxValue": {"type": "string"}, "fromAmountMinValue": {"type": "string"}, "fromAmountPrecision": {"type": "string"}, "fromAmountRestrictionType": {"type": "string"}, "includesDestinationTx": {"type": "boolean"}, "internalSwaps": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.InternalSwaps"}}, "maxRequiredSign": {"type": "integer"}, "recommendedSlippage": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.RecommendedSlippage"}, "routes": {"type": "array", "items": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Route"}}, "swapChainType": {"type": "string"}, "swapperId": {"type": "string"}, "swapperLogo": {"type": "string"}, "swapperType": {"type": "string"}, "timeStat": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.TimeStat"}, "to": {"$ref": "#/definitions/gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.TokenInfo"}, "toAmount": {"type": "string"}, "warnings": {"type": "array", "items": {"type": "string"}}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Tag": {"type": "object", "properties": {"label": {"type": "string"}, "value": {"type": "string"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.TimeStat": {"type": "object", "properties": {"avg": {"type": "integer"}, "max": {"type": "integer"}, "min": {"type": "integer"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.TokenInfo": {"type": "object", "properties": {"address": {"type": "string"}, "blockchain": {"type": "string"}, "blockchainLogo": {"type": "string"}, "decimals": {"type": "integer"}, "logo": {"type": "string"}, "symbol": {"type": "string"}, "usdPrice": {"type": "number"}}}, "gitlab_xbit_live_xbit_xbit-dex_xbit-goback_server_pkg_rango.Transaction": {"type": "object", "properties": {"blockChain": {"type": "string"}, "data": {"type": "string"}, "from": {"type": "string"}, "gasLimit": {"type": "string"}, "gasPrice": {"type": "string"}, "identifier": {"description": "NoEvm", "type": "string"}, "instructions": {"type": "array", "items": {"type": "string"}}, "isApprovalTx": {"type": "boolean"}, "maxFeePerGas": {"type": "string"}, "maxPriorityFeePerGas": {"type": "string"}, "nonce": {"type": "string"}, "recentBlockhash": {"type": "string"}, "serializedMessage": {"type": "array", "items": {"type": "integer"}}, "signatures": {"type": "array", "items": {"type": "string"}}, "spender": {"type": "string"}, "to": {"description": "Evm", "type": "string"}, "txType": {"type": "string"}, "type": {"type": "string"}, "value": {"type": "string"}}}, "gorm.DeletedAt": {"type": "object", "properties": {"time": {"type": "string"}, "valid": {"description": "Valid is true if Time is not NULL", "type": "boolean"}}}}}
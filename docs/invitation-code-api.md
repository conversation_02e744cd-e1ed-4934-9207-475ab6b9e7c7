# Invitation Code API

This document describes the GraphQL endpoints for managing user invitation codes.

## Overview

The invitation code system allows users to:
1. Generate a unique invitation code for themselves
2. Update their invitation code to a custom value
3. Retrieve their current invitation code

Based on the UI requirements, the invitation code is stored without the "@" symbol - the "@" symbol is only used by the frontend for display purposes.

## GraphQL Endpoints

### 1. Generate Invitation Code

Generates a new invitation code for a user. If the user already has an invitation code, it returns the existing one.

**Mutation:**
```graphql
mutation GenerateInvitationCode($userId: ID!) {
  generateInvitationCode(userId: $userId) {
    user {
      id
      email
      invitationCode
    }
    success
    message
  }
}
```

**Variables:**
```json
{
  "userId": "123e4567-e89b-12d3-a456-426614174000"
}
```

**Response:**
```json
{
  "data": {
    "generateInvitationCode": {
      "user": {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "email": "<EMAIL>",
        "invitationCode": "ABCD1234"
      },
      "success": true,
      "message": "Invitation code generated successfully"
    }
  }
}
```

### 2. Update Invitation Code

Updates a user's invitation code to a custom value. The code must be between 5-15 characters and unique.

**Mutation:**
```graphql
mutation UpdateInvitationCode($input: UpdateInvitationCodeInput!) {
  updateInvitationCode(input: $input) {
    user {
      id
      email
      invitationCode
    }
    success
    message
  }
}
```

**Variables:**
```json
{
  "input": {
    "userId": "123e4567-e89b-12d3-a456-426614174000",
    "invitationCode": "Alicece"
  }
}
```

**Response:**
```json
{
  "data": {
    "updateInvitationCode": {
      "user": {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "email": "<EMAIL>",
        "invitationCode": "Alicece"
      },
      "success": true,
      "message": "Invitation code updated successfully"
    }
  }
}
```

**Error Response (when code already exists):**
```json
{
  "data": {
    "updateInvitationCode": {
      "user": null,
      "success": false,
      "message": "Failed to update invitation code: invitation code already exists"
    }
  }
}
```

### 3. Get User Invitation Code

Retrieves the current invitation code for a user.

**Query:**
```graphql
query GetUserInvitationCode($userId: ID!) {
  getUserInvitationCode(userId: $userId)
}
```

**Variables:**
```json
{
  "userId": "123e4567-e89b-12d3-a456-426614174000"
}
```

**Response (with invitation code):**
```json
{
  "data": {
    "getUserInvitationCode": "Alicece"
  }
}
```

**Response (no invitation code):**
```json
{
  "data": {
    "getUserInvitationCode": null
  }
}
```

## Frontend Integration

Based on the UI screenshots provided, the frontend should:

1. **Display invitation codes with "@" prefix**: When showing the invitation code to users, prepend "@" to the stored value (e.g., display "@Alicece" for stored value "Alicece")

2. **Remove "@" when updating**: When users input an invitation code starting with "@", remove the "@" before sending to the API

3. **Generate invitation link**: Create shareable links in the format: `https://www.xbit.com?invite=@Alicece`

## Validation Rules

- **Length**: Invitation codes must be between 5-15 characters
- **Uniqueness**: Each invitation code must be unique across all users
- **Format**: Alphanumeric characters are recommended
- **Storage**: Codes are stored without the "@" symbol

## Error Handling

The API returns structured error responses with:
- `success: false` for failed operations
- Descriptive error messages in the `message` field
- `null` user object for failed operations

Common error scenarios:
- Invalid user ID format
- Invitation code too short/long
- Invitation code already exists
- User not found

## Example Frontend Usage

```javascript
// Generate invitation code
const generateCode = async (userId) => {
  const response = await graphqlClient.mutate({
    mutation: GENERATE_INVITATION_CODE,
    variables: { userId }
  });
  
  if (response.data.generateInvitationCode.success) {
    const code = response.data.generateInvitationCode.user.invitationCode;
    // Display as @code to user
    displayInvitationCode(`@${code}`);
  }
};

// Update invitation code (remove @ if present)
const updateCode = async (userId, inputCode) => {
  const cleanCode = inputCode.startsWith('@') ? inputCode.slice(1) : inputCode;
  
  const response = await graphqlClient.mutate({
    mutation: UPDATE_INVITATION_CODE,
    variables: {
      input: {
        userId,
        invitationCode: cleanCode
      }
    }
  });
  
  return response.data.updateInvitationCode;
};
```

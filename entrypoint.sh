#!/bin/sh
set -e

_terminate() {
  echo "Signal (SIGINT/SIGTERM) received. Waiting for gracefully shutdown..."
  kill $(jobs -p)
  wait
  exit 0
}

trap _terminate SIGINT SIGTERM

# Run migration
export DATABASE_URL="postgresql://$POSTGRES_USER:$POSTGRES_PASS@$POSTGRES_HOST:$POSTGRES_PORT/${POSTGRES_DB:-agent}?sslmode=disable"
echo "DATABASE_URL: $DATABASE_URL"

# Check if migrations directory exists and has files
if [ -d "migrations" ] && [ "$(ls -A migrations)" ]; then
    echo "Migration files found, but using GORM AutoMigrate instead of Atlas migrations."
    echo "Atlas migrations are disabled to avoid conflicts with existing database schema."
    echo "Database schema will be managed by GORM AutoMigrate in the application."
else
    echo "No migration files found or migrations directory doesn't exist."
fi

echo "Skipping Atlas migrations - using GORM AutoMigrate for database schema management."

# Wait for any process to exit
# wait -n

# Exit with status of process that exited first
exec $@

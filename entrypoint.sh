#!/bin/sh
set -e

_terminate() {
  echo "Signal (SIGINT/SIGTERM) received. Waiting for gracefully shutdown..."
  kill $(jobs -p)
  wait
  exit 0
}

trap _terminate SIGINT SIGTERM

# Run migration
export DATABASE_URL="postgresql://$POSTGRES_USER:$POSTGRES_PASS@$POSTGRES_HOST:$POSTGRES_PORT/dex_v2?sslmode=disable"
echo "DATABASE_URL: $DATABASE_URL"
atlas migrate apply --url $DATABASE_URL --dir file://migrations


# Wait for any process to exit
# wait -n

# Exit with status of process that exited first
exec $@

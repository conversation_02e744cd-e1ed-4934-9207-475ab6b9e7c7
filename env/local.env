# Local Development Environment
# This file contains environment variables for local development

# Application Configuration
APP_ENV=local
APP_NAME=xbit-agent
APP_VERSION=1.0.0

# Server Configuration
SERVER_HOST=127.0.0.1
SERVER_PORT=8080

# Database Configuration (PostgreSQL)
POSTGRES_HOST=127.0.0.1
POSTGRES_PORT=5432
POSTGRES_USER=dev
POSTGRES_PASS=dev
POSTGRES_DB=agent

# Database URL for migrations
DATABASE_URL=postgresql://dev:dev@127.0.0.1:5432/agent?sslmode=disable

# Redis Configuration (Optional - for caching)
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASS=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=local-development-secret-key-not-for-production
JWT_EXPIRES_TIME=7d
JWT_BUFFER_TIME=1d
JWT_ISSUER=xbit-agent-local

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=console
LOG_DIRECTOR=log
LOG_SHOW_LINE=true
LOG_IN_CONSOLE=true

# CORS Configuration (Allow all for development)
CORS_ALLOW_ORIGINS=*
CORS_ALLOW_METHODS=GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS
CORS_ALLOW_HEADERS=Origin,Content-Length,Content-Type,Authorization,X-Requested-With
CORS_ALLOW_CREDENTIALS=true

# Rate Limiting (More lenient for development)
RATE_LIMIT_COUNT=50000
RATE_LIMIT_TIME=3600

# Development Settings
DEBUG=true
ENABLE_PLAYGROUND=true

# Docker Compose Settings
COMPOSE_PROJECT_NAME=xbit-agent-local

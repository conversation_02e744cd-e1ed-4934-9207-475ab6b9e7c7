# nats.conf
# This file is mounted into the NATS container to configure it.

# Client connection port
port: 4222

# HTTP monitoring port
http_port: 8222

# Enable JetStream
jetstream {
  # Directory where JetStream will store its data.
  # This should match the -sd argument and the mounted volume in docker-compose.yml
  store_dir: /data

  # Max memory/disk limits (optional, good for dev to prevent runaway usage)
  # max_memory_store: 1GB
  # max_file_store: 10GB
}

# Add logging verbosity for development
debug: true
trace: true
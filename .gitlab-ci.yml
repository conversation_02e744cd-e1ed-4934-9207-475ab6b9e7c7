variables:
  PROJECT_NAME: dex-api-service

stages:
  - lint
  - build
  - deploy

.unstable_template: &unstable_template
  tags:
    - xbit
  only:
    - unstable

.staging_template: &staging_template
  tags:
    - xbit
  only:
    - staging

.prod_template: &prod_template
  tags:
    - xbit
  only:
    - prod

.base_build_stage:
  stage: build
  script:
    - /home/<USER>/scripts/build.sh $PROJECT_NAME $STAGE.$CI_PIPELINE_ID $STAGE

  variables:
    STAGE: staging

.base_deploy_stage:
  stage: deploy
  script:
    - /home/<USER>/scripts/deploy_to_k8s.sh $PROJECT_NAME $STAGE.$CI_PIPELINE_ID $STAGE

  variables:
    STAGE: staging

# CI for unstable
# -------------------------------------------------------------------------------
build_unstable:
  <<: *unstable_template
  extends: .base_build_stage
  variables:
    STAGE: unstable

deploy_unstable:
  <<: *unstable_template
  extends: .base_deploy_stage
  variables:
    STAGE: unstable
  environment:
    name: unstable

# CI for staging
# -------------------------------------------------------------------------------
build_staging:
  <<: *staging_template
  extends: .base_build_stage
  variables:
    STAGE: staging

deploy_staging:
  <<: *staging_template
  extends: .base_deploy_stage
  variables:
    STAGE: staging
  environment:
    name: staging

# CI for prod
# -------------------------------------------------------------------------------
build_prod:
  <<: *prod_template
  extends: .base_build_stage
  variables:
    STAGE: prod

deploy_prod:
  <<: *prod_template
  extends: .base_deploy_stage
  variables:
    STAGE: prod
  environment:
    name: prod

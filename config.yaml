server-name: api
zap:
  level: info
  format: console
  prefix: "[gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server]"
  director: log
  show-line: true
  encode-level: LowercaseColorLevelEncoder
  stacktrace-key: stacktrace
  log-in-console: true
  retention-day: -1

# redis configuration
redis:
  #whether to use cluster mode
  useCluster: false
  #When using cluster mode, the addr and db settings are disabled by default.
  addr: {{ index . "REDIS_HOST" }}:{{ index . "REDIS_PORT" }}
  password: {{ index . "REDIS_PASS" }}
  db: 0
  clusterAddrs:
    - "**********:7000"
    - "**********:7001"
    - "**********:7002"

# redis-list configuration
redis-list:
  - name: cache           # The database name. Note: The name needs to be unique in the redis-list.
    useCluster: false     # Whether to use Redis cluster mode.
    addr: {{ index . "REDIS_HOST" }}:{{ index . "REDIS_PORT" }}  # When using cluster mode, the addr and db settings are disabled by default.
    password: {{ index . "REDIS_PASS" }}
    db: 0
    clusterAddrs:
      - "**********:7000"
      - "**********:7001"
      - "**********:7002"

# mongo configuration
mongo:
  coll: ''
  options: ''
  database: ''
  username: ''
  password: ''
  auth-source: ''
  min-pool-size: 0
  max-pool-size: 100
  socket-timeout-ms: 0
  connect-timeout-ms: 0
  is-zap: false
  hosts:
    - host: '***********'
      port: '27017'

# system configuration
system:
  env: local # change it to public can close the log console
  addr: 9000
  db-type: pgsql
  use-redis: true
  use-mongo: false
  use-multipoint: false
  # IP limit per 15000 times one hour
  iplimit-count: 15000
  #  IP limit one hour
  iplimit-time: 360
  #  Global route prefix
  router-prefix: "/api/dex"
  graphql-prefix: "/api/graphql-dex"

hyperliquid:
  use-mainnet: true

coinmarketcap:
  api-key: "f9a547af-46fe-4bdb-8b6f-7e8d5f22c0e4"
# pgsql connect configuration
# Do not manually modify the database information before initialization!!!
pgsql:
  path: {{ index . "POSTGRES_HOST" }}
  port: {{ index . "POSTGRES_PORT" }}
  config: ""
  db-name: "dex_v2"
  username: {{ index . "POSTGRES_USER" }}
  password: {{ index . "POSTGRES_PASS" }}
  max-idle-conns: 10
  max-open-conns: 100
  log-mode: ""
  log-zap: false

nats-meme:
  url: {{ index . "MEME_NATS_URL" }}
  user: {{ index . "MEME_NATS_USER" }}
  pass: {{ index . "MEME_NATS_PASS" }}
  use-tls: false

nats-dex:
  url: {{ index . "NATS_URL" }}
  token: {{ index . "NATS_AUTH_TOKEN" }}

clickhouse:
  path: {{ index . "CLICKHOUSE_HOST" }}
  port: {{ index . "CLICKHOUSE_PORT" }}
  username: {{ index . "CLICKHOUSE_USER" }}
  password: {{ index . "CLICKHOUSE_PASS" }}
  db-name: "default"
  max-idle-conns: 10
  max-open-conns: 100
  log-mode: ""
  log-zap: false
  is-secure: false

mqtt:
  host: {{ index . "EMQX_HOST" }}
  port: {{ index . "EMQX_PORT" }}
  username: {{ index . "EMQX_USER" }}
  password: {{ index . "EMQX_PASS" }}
  protocol: {{ index . "EMQX_PROTOCOL" }}

grpc:
  user_service: "xbit-user-grpc.unstable.svc.cluster.local:5001"

db-list:
  - disable: true # is disable
    type: "" # database type ,support :mysql、pgsql、mssql、oracle
    alias-name: "" # database name,attention: alias-name need db-list only one
    path: ""
    port: ""
    config: ""
    db-name: ""
    username: ""
    password: ""
    max-idle-conns: 10
    max-open-conns: 100
    log-mode: ""
    log-zap: false

# local configuration
local:
  path: uploads/file
  store-path: uploads/file

# autocode configuration
autocode:
  web: web/src
  root: "" # root Automatic adaptation to the project root directory. Please do not configure it manually; it will detect the root path automatically when the project is loaded.
  server: server
  module: 'gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server'
  ai-path: ""

# excel configuration
excel:
  dir: ./resource/excel/
# Cross-Origin Configuration
# Requires coordination with  server/initialize/router.go -> `Router.Use(middleware.CorsByRules())`
cors:
  mode: strict-whitelist # Access Control Modes: allow-all allows all requests; whitelist adds CORS headers to requests from domains in the whitelist; strict-whitelist rejects all requests outside the whitelist.
  whitelist:
    - allow-origin: example1.com
      allow-headers: Content-Type,AccessToken,X-CSRF-Token, Authorization, Token,X-Token,X-User-Id
      allow-methods: POST, GET
      expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type

      allow-credentials: true
    - allow-origin: example2.com
      allow-headers: content-type
      allow-methods: GET, POST
      expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
      allow-credentials: true

cron-tasks:
  - id: "sync_rango_metadata"
    cron: "0 */5 * * * *"
  - id: "crawl_symbol_list"
    cron: "*/1 * * * * *"
  - id: "crawl_market_cap_data"
    cron: "0 */10 * * * *"
  - id: "craw_category_list"
    cron: "0 0 */12 * * *"
  - id: "track_user_balance"
    cron: "0 * * * * *"

temporal:
  hostport: "dex-temporal:7233"
  namespace: "default"
